<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试</title>
    
    <!-- CSS样式 -->
    <link rel="stylesheet" href="/static/css/theme-variables.css">
    <link rel="stylesheet" href="/static/css/fullscreen-layout.css">
    <link rel="stylesheet" href="/static/css/modern-components.css">
    <link rel="stylesheet" href="/static/css/components/card-components.css">
    
    <style>
        body {
            background: var(--theme-background);
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 40px;
        }
        
        .test-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ffffff;
        }
        
        .debug-info {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-family: monospace;
        }
        
        .error {
            color: #ff6b6b;
        }
        
        .success {
            color: #51cf66;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>卡片组件最终测试</h1>
        
        <div class="debug-info" id="debug-info">
            正在加载...
        </div>
        
        <div class="test-section">
            <h2 class="test-title">病人信息卡片测试</h2>
            <div id="patient-card-container"></div>
        </div>
        
        <div class="test-section">
            <h2 class="test-title">手术安排卡片</h2>
            <div id="surgery-card-container"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">通知卡片</h2>
            <div id="notification-card-container"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">设备状态卡片</h2>
            <div id="device-card-container"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">快速操作卡片</h2>
            <div id="quick-action-container"></div>
        </div>

        <div class="test-section">
            <h2 class="test-title">时间轴卡片</h2>
            <div id="timeline-container"></div>
        </div>
    </div>
    
    <!-- 直接内联JavaScript以避免缓存问题 -->
    <script>
        // 内联卡片组件定义
        class CardComponents {
            constructor() {
                this.version = '3.0';
            }

            createPatientCard(config) {
                const {
                    name = '患者姓名',
                    age = '',
                    gender = '',
                    bedNumber = '',
                    diagnosis = '',
                    allergies = [],
                    specialNotes = '',
                    riskLevel = 'low',
                    className = ''
                } = config;

                const riskColors = {
                    low: '#51cf66',
                    medium: '#ffd43b',
                    high: '#ff6b6b'
                };

                return `
                    <div class="modern-patient-card ${className}" style="
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 16px;
                        padding: 24px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        backdrop-filter: blur(10px);
                        margin-bottom: 20px;
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 20px;">
                            <div>
                                <h3 style="margin: 0 0 8px 0; color: white; font-size: 24px;">${name}</h3>
                                <div style="color: rgba(255, 255, 255, 0.8);">
                                    ${age ? `${age}岁` : ''} ${gender || ''} ${bedNumber ? `床号: ${bedNumber}` : ''}
                                </div>
                            </div>
                            <div style="
                                background: ${riskColors[riskLevel]};
                                color: white;
                                padding: 4px 12px;
                                border-radius: 12px;
                                font-size: 12px;
                                font-weight: bold;
                            ">
                                ${riskLevel === 'low' ? '低风险' : riskLevel === 'medium' ? '中风险' : '高风险'}
                            </div>
                        </div>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 24px;">
                            <div>
                                <h4 style="color: white; margin: 0 0 12px 0;">基础信息</h4>
                                ${diagnosis ? `<p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0;">诊断: ${diagnosis}</p>` : ''}
                            </div>
                            
                            <div>
                                <h4 style="color: white; margin: 0 0 12px 0;">特殊情况交班</h4>
                                ${allergies.length > 0 ? `
                                    <div style="margin-bottom: 12px;">
                                        <strong style="color: #ff6b6b;">⚠️ 过敏史:</strong>
                                        <div style="margin-top: 4px;">
                                            ${allergies.map(allergy => `
                                                <span style="
                                                    background: #ff6b6b;
                                                    color: white;
                                                    padding: 2px 8px;
                                                    border-radius: 12px;
                                                    font-size: 12px;
                                                    margin-right: 4px;
                                                    display: inline-block;
                                                ">${allergy}</span>
                                            `).join('')}
                                        </div>
                                    </div>
                                ` : ''}
                                ${specialNotes ? `
                                    <div style="
                                        background: rgba(255, 193, 7, 0.2);
                                        border: 1px solid rgba(255, 212, 59, 0.3);
                                        padding: 12px;
                                        border-radius: 4px;
                                        color: rgba(255, 255, 255, 0.9);
                                        font-size: 14px;
                                        line-height: 1.4;
                                    ">
                                        📝 ${specialNotes}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }

            createSurgeryScheduleCard(config) {
                const {
                    patientName = '',
                    surgeryName = '',
                    scheduledTime = '',
                    priority = 'routine',
                    status = 'scheduled'
                } = config;

                const priorityColors = {
                    emergency: '#ff6b6b',
                    urgent: '#ffd43b',
                    routine: '#51cf66'
                };

                return `
                    <div style="
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 16px;
                        padding: 20px;
                        backdrop-filter: blur(10px);
                        margin-bottom: 16px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
                            <div style="color: white; font-size: 18px; font-weight: bold;">${scheduledTime}</div>
                            <div style="
                                background: ${priorityColors[priority]};
                                color: white;
                                padding: 4px 8px;
                                border-radius: 8px;
                                font-size: 12px;
                                font-weight: bold;
                            ">
                                ${priority === 'emergency' ? '急诊' : priority === 'urgent' ? '紧急' : '常规'}
                            </div>
                        </div>
                        <h4 style="color: white; margin: 0 0 4px 0;">${patientName}</h4>
                        <p style="color: rgba(255, 255, 255, 0.8); margin: 0;">${surgeryName}</p>
                    </div>
                `;
            }

            createNotificationCard(config) {
                const {
                    title = '',
                    message = '',
                    type = 'info',
                    timestamp = '',
                    isImportant = false,
                    isRead = false
                } = config;

                const typeColors = {
                    info: '#3b82f6',
                    warning: '#f59e0b',
                    error: '#ef4444',
                    success: '#10b981'
                };

                const typeIcons = {
                    info: '📢',
                    warning: '⚠️',
                    error: '❌',
                    success: '✅'
                };

                return `
                    <div style="
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 16px;
                        padding: 20px;
                        backdrop-filter: blur(10px);
                        margin-bottom: 16px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        position: relative;
                    ">

                        ${!isRead ? `
                            <div style="
                                position: absolute;
                                top: 12px;
                                right: 12px;
                                width: 8px;
                                height: 8px;
                                background: #3b82f6;
                                border-radius: 50%;
                            "></div>
                        ` : ''}
                        <div style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px;">
                            <div style="font-size: 20px;">${typeIcons[type]}</div>
                            <div style="flex: 1;">
                                <h4 style="color: white; margin: 0 0 4px 0; font-size: 16px;">${title}</h4>
                                <div style="color: rgba(255, 255, 255, 0.7); font-size: 12px;">${timestamp}</div>
                            </div>
                        </div>
                        <p style="color: rgba(255, 255, 255, 0.9); margin: 0; line-height: 1.4;">${message}</p>
                    </div>
                `;
            }

            createDeviceStatusCard(config) {
                const {
                    deviceName = '',
                    deviceType = '',
                    status = 'normal',
                    location = '',
                    batteryLevel = null,
                    connectionStatus = 'connected'
                } = config;

                const statusColors = {
                    normal: '#10b981',
                    warning: '#f59e0b',
                    error: '#ef4444',
                    maintenance: '#3b82f6'
                };

                const statusIcons = {
                    normal: '✅',
                    warning: '⚠️',
                    error: '❌',
                    maintenance: '🔧'
                };

                return `
                    <div style="
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 16px;
                        padding: 20px;
                        backdrop-filter: blur(10px);
                        margin-bottom: 16px;
                        border: 1px solid rgba(255, 255, 255, 0.2);
                    ">
                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 16px;">
                            <div>
                                <h4 style="color: white; margin: 0 0 4px 0; font-size: 16px;">${deviceName}</h4>
                                <div style="color: rgba(255, 255, 255, 0.7); font-size: 14px;">${deviceType}</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: 6px;">
                                <span style="font-size: 16px;">${statusIcons[status]}</span>
                                <span style="color: ${statusColors[status]}; font-weight: bold; font-size: 14px;">
                                    ${status === 'normal' ? '正常' : status === 'warning' ? '警告' : status === 'error' ? '故障' : '维护中'}
                                </span>
                            </div>
                        </div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px; font-size: 14px;">
                            <div style="color: rgba(255, 255, 255, 0.8);">
                                <strong>位置:</strong> ${location}
                            </div>
                            <div style="color: rgba(255, 255, 255, 0.8);">
                                <strong>连接:</strong> ${connectionStatus === 'connected' ? '已连接' : '未连接'}
                            </div>
                            ${batteryLevel !== null ? `
                                <div style="color: rgba(255, 255, 255, 0.8); grid-column: 1 / -1;">
                                    <strong>电池:</strong>
                                    <div style="
                                        display: inline-block;
                                        width: 60px;
                                        height: 8px;
                                        background: rgba(255, 255, 255, 0.2);
                                        border-radius: 4px;
                                        margin-left: 8px;
                                        position: relative;
                                        vertical-align: middle;
                                    ">
                                        <div style="
                                            width: ${batteryLevel}%;
                                            height: 100%;
                                            background: ${batteryLevel > 50 ? '#10b981' : batteryLevel > 20 ? '#f59e0b' : '#ef4444'};
                                            border-radius: 4px;
                                        "></div>
                                    </div>
                                    <span style="margin-left: 8px;">${batteryLevel}%</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            createQuickActionCard(config) {
                const {
                    title = '快速操作',
                    actions = []
                } = config;

                return `
                    <div style="
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 16px;
                        padding: 20px;
                        backdrop-filter: blur(10px);
                        margin-bottom: 16px;
                    ">
                        <h4 style="color: white; margin: 0 0 16px 0; font-size: 16px;">${title}</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 12px;">
                            ${actions.map(action => `
                                <button style="
                                    background: rgba(255, 255, 255, 0.1);
                                    border: 1px solid rgba(255, 255, 255, 0.2);
                                    border-radius: 12px;
                                    padding: 16px 12px;
                                    color: white;
                                    cursor: pointer;
                                    transition: all 0.2s;
                                    text-align: center;
                                    font-size: 14px;
                                " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'"
                                   onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'"
                                   onclick="${action.onClick || ''}">
                                    <div style="font-size: 24px; margin-bottom: 8px;">${action.icon || '⚡'}</div>
                                    <div style="font-weight: bold; margin-bottom: 4px;">${action.label}</div>
                                    ${action.description ? `<div style="font-size: 12px; color: rgba(255, 255, 255, 0.7);">${action.description}</div>` : ''}
                                </button>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            createTimelineCard(config) {
                const {
                    title = '时间轴',
                    events = []
                } = config;

                return `
                    <div style="
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 16px;
                        padding: 20px;
                        backdrop-filter: blur(10px);
                        margin-bottom: 16px;
                    ">
                        <h4 style="color: white; margin: 0 0 20px 0; font-size: 16px;">${title}</h4>
                        <div style="position: relative;">
                            ${events.map((event, index) => `
                                <div style="
                                    display: flex;
                                    margin-bottom: ${index < events.length - 1 ? '20px' : '0'};
                                    position: relative;
                                ">
                                    <div style="
                                        width: 12px;
                                        height: 12px;
                                        border-radius: 50%;
                                        background: ${event.type === 'success' ? '#10b981' : event.type === 'warning' ? '#f59e0b' : event.type === 'error' ? '#ef4444' : '#3b82f6'};
                                        margin-right: 16px;
                                        margin-top: 4px;
                                        flex-shrink: 0;
                                        position: relative;
                                        z-index: 2;
                                    "></div>
                                    ${index < events.length - 1 ? `
                                        <div style="
                                            position: absolute;
                                            left: 5px;
                                            top: 16px;
                                            width: 2px;
                                            height: calc(100% + 8px);
                                            background: rgba(255, 255, 255, 0.2);
                                            z-index: 1;
                                        "></div>
                                    ` : ''}
                                    <div style="flex: 1;">
                                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 12px; margin-bottom: 4px;">
                                            ${event.time || ''}
                                        </div>
                                        <div style="color: white; font-weight: bold; margin-bottom: 4px;">
                                            ${event.title || ''}
                                        </div>
                                        ${event.description ? `
                                            <div style="color: rgba(255, 255, 255, 0.8); font-size: 14px; line-height: 1.4;">
                                                ${event.description}
                                            </div>
                                        ` : ''}
                                        ${event.author ? `
                                            <div style="color: rgba(255, 255, 255, 0.6); font-size: 12px; margin-top: 4px;">
                                                by ${event.author}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
        }

        // 创建全局实例
        window.cardComponents = new CardComponents();
        
        // 调试信息
        const debugInfo = document.getElementById('debug-info');
        debugInfo.innerHTML = `
            <div class="success">✓ CardComponents v${window.cardComponents.version} 已加载</div>
            <div class="success">✓ createPatientCard: ${typeof window.cardComponents.createPatientCard}</div>
            <div class="success">✓ createSurgeryScheduleCard: ${typeof window.cardComponents.createSurgeryScheduleCard}</div>
            <div class="success">✓ createNotificationCard: ${typeof window.cardComponents.createNotificationCard}</div>
            <div class="success">✓ createDeviceStatusCard: ${typeof window.cardComponents.createDeviceStatusCard}</div>
            <div class="success">✓ createQuickActionCard: ${typeof window.cardComponents.createQuickActionCard}</div>
            <div class="success">✓ createTimelineCard: ${typeof window.cardComponents.createTimelineCard}</div>
        `;

        // 测试病人卡片
        try {
            const patientCard = window.cardComponents.createPatientCard({
                name: '张三',
                age: 45,
                gender: '男',
                bedNumber: '301-1',
                diagnosis: '胆囊结石伴慢性胆囊炎',
                allergies: ['青霉素', '磺胺类药物'],
                specialNotes: '患者有高血压病史，术前血压控制良好。术中需密切监测血压变化。',
                riskLevel: 'medium'
            });
            document.getElementById('patient-card-container').innerHTML = patientCard;
            debugInfo.innerHTML += '<div class="success">✓ 病人卡片创建成功</div>';
        } catch (error) {
            debugInfo.innerHTML += `<div class="error">✗ 病人卡片创建失败: ${error.message}</div>`;
        }

        // 测试手术卡片
        try {
            const surgeryCard = window.cardComponents.createSurgeryScheduleCard({
                patientName: '王五',
                surgeryName: '阑尾切除术',
                scheduledTime: '09:00',
                priority: 'emergency',
                status: 'in-progress'
            });
            document.getElementById('surgery-card-container').innerHTML = surgeryCard;
            debugInfo.innerHTML += '<div class="success">✓ 手术卡片创建成功</div>';
        } catch (error) {
            debugInfo.innerHTML += `<div class="error">✗ 手术卡片创建失败: ${error.message}</div>`;
        }

        // 测试通知卡片
        try {
            const notificationCard = window.cardComponents.createNotificationCard({
                title: '紧急通知',
                message: '手术室2设备故障，请立即安排维修人员检查。影响可能持续30分钟。',
                type: 'error',
                timestamp: '2024-08-04 14:30',
                isImportant: true,
                isRead: false
            });
            document.getElementById('notification-card-container').innerHTML = notificationCard;
            debugInfo.innerHTML += '<div class="success">✓ 通知卡片创建成功</div>';
        } catch (error) {
            debugInfo.innerHTML += `<div class="error">✗ 通知卡片创建失败: ${error.message}</div>`;
        }

        // 测试设备状态卡片
        try {
            const deviceCard = window.cardComponents.createDeviceStatusCard({
                deviceName: '监护仪-001',
                deviceType: '生命体征监护仪',
                status: 'warning',
                location: '手术室1',
                batteryLevel: 35,
                connectionStatus: 'connected'
            });
            document.getElementById('device-card-container').innerHTML = deviceCard;
            debugInfo.innerHTML += '<div class="success">✓ 设备状态卡片创建成功</div>';
        } catch (error) {
            debugInfo.innerHTML += `<div class="error">✗ 设备状态卡片创建失败: ${error.message}</div>`;
        }

        // 测试快速操作卡片
        try {
            const quickActionCard = window.cardComponents.createQuickActionCard({
                title: '常用操作',
                actions: [
                    { icon: '📋', label: '查看病历', description: '查看患者详细病历', onClick: 'alert("查看病历")' },
                    { icon: '💊', label: '用药记录', description: '记录用药情况', onClick: 'alert("用药记录")' },
                    { icon: '📊', label: '生命体征', description: '记录生命体征', onClick: 'alert("生命体征")' },
                    { icon: '🔔', label: '发送通知', description: '发送重要通知', onClick: 'alert("发送通知")' },
                    { icon: '📞', label: '呼叫医生', description: '紧急呼叫医生', onClick: 'alert("呼叫医生")' },
                    { icon: '🚨', label: '紧急呼叫', description: '启动紧急响应', onClick: 'alert("紧急呼叫")' }
                ]
            });
            document.getElementById('quick-action-container').innerHTML = quickActionCard;
            debugInfo.innerHTML += '<div class="success">✓ 快速操作卡片创建成功</div>';
        } catch (error) {
            debugInfo.innerHTML += `<div class="error">✗ 快速操作卡片创建失败: ${error.message}</div>`;
        }

        // 测试时间轴卡片
        try {
            const timelineCard = window.cardComponents.createTimelineCard({
                title: '患者治疗时间轴',
                events: [
                    {
                        time: '08:00',
                        title: '患者入院',
                        description: '患者因腹痛入院，完成入院手续和初步检查',
                        author: '护士小王',
                        type: 'default'
                    },
                    {
                        time: '09:30',
                        title: '术前检查',
                        description: '完成血常规、心电图、胸片等术前必要检查',
                        author: '检验科',
                        type: 'default'
                    },
                    {
                        time: '11:00',
                        title: '麻醉评估',
                        description: '麻醉医生完成术前评估，制定个性化麻醉方案',
                        author: '李麻醉师',
                        type: 'success'
                    },
                    {
                        time: '14:00',
                        title: '手术开始',
                        description: '患者进入手术室，开始腹腔镜胆囊切除术',
                        author: '张医生',
                        type: 'warning'
                    },
                    {
                        time: '15:30',
                        title: '手术完成',
                        description: '手术顺利完成，患者生命体征平稳，转入恢复室',
                        author: '张医生',
                        type: 'success'
                    }
                ]
            });
            document.getElementById('timeline-container').innerHTML = timelineCard;
            debugInfo.innerHTML += '<div class="success">✓ 时间轴卡片创建成功</div>';
        } catch (error) {
            debugInfo.innerHTML += `<div class="error">✗ 时间轴卡片创建失败: ${error.message}</div>`;
        }
    </script>
</body>
</html>
