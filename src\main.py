"""
麻醉科交班大屏软件主应用
"""
import sys
import logging
from pathlib import Path
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
import uvicorn

from config.settings import settings
from src.api.routes import surgery, statistics, upload, config as config_routes, websocket, simple_surgery
from src.api.middleware.cors import setup_cors
from src.utils.logger import setup_logging

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("应用启动中...")
    
    # 启动时的初始化工作
    try:
        # 测试数据库连接
        from src.database.connection import db_connection
        if not db_connection.test_connection():
            logger.error("数据库连接失败")
            raise Exception("数据库连接失败")
        
        logger.info("数据库连接正常")
        logger.info("应用启动完成")
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise
    
    yield
    
    # 关闭时的清理工作
    logger.info("应用关闭中...")

# 创建FastAPI应用
app = FastAPI(
    title="麻醉科交班大屏软件",
    description="专为医院麻醉科设计的信息展示系统",
    version="1.0.0",
    lifespan=lifespan
)

# 设置CORS
setup_cors(app)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 设置模板
templates = Jinja2Templates(directory="templates")

# 先注册简化的API路由（优先级更高）
# 添加简化的统计API路由（用于前端）
@app.get("/api/stats/today")
async def get_today_stats():
    """获取今日统计（简化版）"""
    try:
        # 调用现有的统计API
        from src.api.routes.statistics import get_today_statistics
        stats = await get_today_statistics()
        return stats
    except Exception as e:
        logger.error(f"获取今日统计失败: {e}")
        # 返回模拟数据
        return {
            "total_surgeries": 3,
            "completed_surgeries": 1,
            "cancelled_surgeries": 0,
            "emergency_surgeries": 1,
            "in_progress_surgeries": 1,
            "scheduled_surgeries": 1
        }

@app.get("/api/stats/yesterday")
async def get_yesterday_stats():
    """获取昨日统计（简化版）"""
    try:
        from src.api.routes.statistics import get_yesterday_statistics
        stats = await get_yesterday_statistics()
        return stats
    except Exception as e:
        logger.error(f"获取昨日统计失败: {e}")
        return {
            "total_surgeries": 5,
            "completed_surgeries": 4,
            "cancelled_surgeries": 1,
            "emergency_surgeries": 1,
            "in_progress_surgeries": 0,
            "scheduled_surgeries": 0
        }

@app.get("/api/surgeries/today")
async def get_today_surgeries():
    """获取今日手术列表"""
    try:
        from src.database.connection import db_connection
        from datetime import date

        today = date.today().isoformat()

        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT id, patient_name, surgery_type, department, anesthesia_type,
                       status, is_emergency, allergy_history, special_situation
                FROM surgeries
                WHERE surgery_date = ?
                ORDER BY created_at
            """, (today,))

            surgeries = []
            for row in cursor.fetchall():
                surgeries.append({
                    "id": row[0],
                    "patient_name": row[1],
                    "surgery_name": row[2],
                    "department": row[3],
                    "anesthesia_type": row[4],
                    "status": row[5],
                    "is_emergency": bool(row[6]),
                    "allergies": row[7],
                    "special_notes": row[8]
                })

            return surgeries

    except Exception as e:
        logger.error(f"获取今日手术列表失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        # 返回模拟数据
        return [
            {
                "id": 1,
                "patient_name": "张三",
                "surgery_name": "阑尾切除术",
                "department": "普外科",
                "anesthesia_type": "全身麻醉",
                "status": "scheduled",
                "is_emergency": True,
                "allergies": None,
                "special_notes": None
            },
            {
                "id": 2,
                "patient_name": "李四",
                "surgery_name": "骨折内固定术",
                "department": "骨科",
                "anesthesia_type": "局部麻醉",
                "status": "in_progress",
                "is_emergency": False,
                "allergies": None,
                "special_notes": None
            },
            {
                "id": 3,
                "patient_name": "王五",
                "surgery_name": "心脏搭桥术",
                "department": "心外科",
                "anesthesia_type": "全身麻醉",
                "status": "scheduled",
                "is_emergency": False,
                "allergies": "青霉素过敏",
                "special_notes": "高血压患者"
            }
        ]

@app.get("/api/surgeries/special")
async def get_special_surgeries():
    """获取特殊病例"""
    try:
        from src.database.connection import db_connection

        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT id, patient_name, surgery_type, department, anesthesia_type,
                       status, is_emergency, allergy_history, special_situation
                FROM surgeries
                WHERE (allergy_history IS NOT NULL AND allergy_history != '')
                   OR (special_situation IS NOT NULL AND special_situation != '')
                   OR is_emergency = 1
                ORDER BY created_at DESC
                LIMIT 10
            """)

            special_cases = []
            for row in cursor.fetchall():
                special_cases.append({
                    "id": row[0],
                    "patient_name": row[1],
                    "surgery_name": row[2],
                    "department": row[3],
                    "anesthesia_type": row[4],
                    "status": row[5],
                    "is_emergency": bool(row[6]),
                    "allergies": row[7],
                    "special_notes": row[8]
                })

            return special_cases

    except Exception as e:
        logger.error(f"获取特殊病例失败: {e}")
        # 返回模拟数据
        return [
            {
                "id": 1,
                "patient_name": "赵六",
                "surgery_name": "心脏搭桥手术",
                "department": "心外科",
                "anesthesia_type": "全身麻醉",
                "status": "scheduled",
                "is_emergency": True,
                "allergies": "青霉素过敏",
                "special_notes": "高血压，需密切监测血压"
            }
        ]

# 然后注册其他API路由
app.include_router(statistics.router, prefix="/api")
app.include_router(upload.router, prefix="/api")
app.include_router(config_routes.router, prefix="/api")
app.include_router(websocket.router, prefix="/api")
app.include_router(simple_surgery.router, prefix="/api")
# 最后注册surgery路由，这样我们的路由会优先匹配
app.include_router(surgery.router, prefix="/api")





@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """主页面"""
    return templates.TemplateResponse("index.html", {
        "request": request,
        "hospital_name": settings.HOSPITAL_NAME,
        "department_name": settings.DEPARTMENT_NAME,
        "refresh_interval": settings.REFRESH_INTERVAL
    })

@app.get("/admin", response_class=HTMLResponse)
async def admin_page(request: Request):
    """管理页面"""
    return templates.TemplateResponse("admin/upload.html", {
        "request": request,
        "hospital_name": settings.HOSPITAL_NAME,
        "department_name": settings.DEPARTMENT_NAME
    })

@app.get("/help", response_class=HTMLResponse)
async def help_page():
    """操作指南页面"""
    from fastapi.responses import FileResponse
    return FileResponse("static/help.html")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        from src.database.connection import db_connection
        db_status = db_connection.test_connection()
        
        return {
            "status": "healthy" if db_status else "unhealthy",
            "database": "connected" if db_status else "disconnected",
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "database": "error",
            "error": str(e),
            "version": "1.0.0"
        }

if __name__ == "__main__":
    logger.info(f"启动服务器: {settings.HOST}:{settings.PORT}")
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower()
    )
