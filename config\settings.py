"""
系统配置文件
"""
import os
from pathlib import Path
from typing import Optional

# 项目根目录
BASE_DIR = Path(__file__).parent.parent

class Settings:
    """系统配置类"""
    
    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", f"sqlite:///{BASE_DIR}/data/database/handover.db")
    
    # 服务器配置
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", 8001))
    DEBUG: bool = os.getenv("DEBUG", "False").lower() == "true"
    RELOAD: bool = os.getenv("RELOAD", "False").lower() == "true"
    
    # 安全配置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here-change-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30))
    
    # 文件上传配置
    UPLOAD_DIR: Path = BASE_DIR / "data" / "uploads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".xlsx", ".xls"}
    
    # 医院信息配置
    HOSPITAL_NAME: str = os.getenv("HOSPITAL_NAME", "示例医院")
    HOSPITAL_LOGO: str = os.getenv("HOSPITAL_LOGO", "/static/images/logos/default.png")
    DEPARTMENT_NAME: str = os.getenv("DEPARTMENT_NAME", "麻醉科")
    
    # 显示配置
    REFRESH_INTERVAL: int = int(os.getenv("REFRESH_INTERVAL", 30))  # 秒
    TIMEZONE: str = os.getenv("TIMEZONE", "Asia/Shanghai")
    
    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: Path = BASE_DIR / "logs" / "app.log"
    
    # CORS配置
    CORS_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
    ]
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL: int = int(os.getenv("WS_HEARTBEAT_INTERVAL", 30))
    
    def __init__(self):
        """初始化配置，创建必要的目录"""
        self.UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
        self.LOG_FILE.parent.mkdir(parents=True, exist_ok=True)
        (BASE_DIR / "data" / "database").mkdir(parents=True, exist_ok=True)

# 全局配置实例
settings = Settings()
