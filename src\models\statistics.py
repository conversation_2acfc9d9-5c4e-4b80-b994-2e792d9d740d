"""
统计数据模型
"""
from datetime import date, datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass

@dataclass
class Statistics:
    """统计数据模型"""
    id: Optional[int] = None
    date: Optional[date] = None
    total_surgeries: int = 0
    completed_surgeries: int = 0
    cancelled_surgeries: int = 0
    emergency_surgeries: int = 0
    average_duration: Optional[float] = None
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'date': self.date.isoformat() if self.date else None,
            'total_surgeries': self.total_surgeries,
            'completed_surgeries': self.completed_surgeries,
            'cancelled_surgeries': self.cancelled_surgeries,
            'emergency_surgeries': self.emergency_surgeries,
            'average_duration': self.average_duration,
            'created_at': self.created_at.isoformat() if self.created_at else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Statistics':
        """从字典创建Statistics对象"""
        return cls(
            id=data.get('id'),
            date=date.fromisoformat(data['date']) if data.get('date') else None,
            total_surgeries=data.get('total_surgeries', 0),
            completed_surgeries=data.get('completed_surgeries', 0),
            cancelled_surgeries=data.get('cancelled_surgeries', 0),
            emergency_surgeries=data.get('emergency_surgeries', 0),
            average_duration=data.get('average_duration'),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
        )
    
    @classmethod
    def from_row(cls, row) -> 'Statistics':
        """从数据库行创建Statistics对象"""
        return cls(
            id=row['id'],
            date=date.fromisoformat(row['date']) if row['date'] else None,
            total_surgeries=row['total_surgeries'],
            completed_surgeries=row['completed_surgeries'],
            cancelled_surgeries=row['cancelled_surgeries'],
            emergency_surgeries=row['emergency_surgeries'],
            average_duration=row['average_duration'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
        )
    
    @property
    def completion_rate(self) -> float:
        """完成率"""
        if self.total_surgeries == 0:
            return 0.0
        return (self.completed_surgeries / self.total_surgeries) * 100
    
    @property
    def cancellation_rate(self) -> float:
        """取消率"""
        if self.total_surgeries == 0:
            return 0.0
        return (self.cancelled_surgeries / self.total_surgeries) * 100
    
    @property
    def emergency_rate(self) -> float:
        """急诊率"""
        if self.total_surgeries == 0:
            return 0.0
        return (self.emergency_surgeries / self.total_surgeries) * 100

@dataclass
class Configuration:
    """系统配置模型"""
    id: Optional[int] = None
    config_key: str = ""
    config_value: str = ""
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'config_key': self.config_key,
            'config_value': self.config_value,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def from_row(cls, row) -> 'Configuration':
        """从数据库行创建Configuration对象"""
        return cls(
            id=row['id'],
            config_key=row['config_key'],
            config_value=row['config_value'],
            description=row['description'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None,
        )

@dataclass
class UploadRecord:
    """文件上传记录模型"""
    id: Optional[int] = None
    filename: str = ""
    original_filename: str = ""
    file_path: str = ""
    file_size: int = 0
    upload_time: Optional[datetime] = None
    processed_time: Optional[datetime] = None
    status: str = "uploaded"
    error_message: Optional[str] = None
    imported_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'upload_time': self.upload_time.isoformat() if self.upload_time else None,
            'processed_time': self.processed_time.isoformat() if self.processed_time else None,
            'status': self.status,
            'error_message': self.error_message,
            'imported_count': self.imported_count,
        }
    
    @classmethod
    def from_row(cls, row) -> 'UploadRecord':
        """从数据库行创建UploadRecord对象"""
        return cls(
            id=row['id'],
            filename=row['filename'],
            original_filename=row['original_filename'],
            file_path=row['file_path'],
            file_size=row['file_size'],
            upload_time=datetime.fromisoformat(row['upload_time']) if row['upload_time'] else None,
            processed_time=datetime.fromisoformat(row['processed_time']) if row['processed_time'] else None,
            status=row['status'],
            error_message=row['error_message'],
            imported_count=row['imported_count'],
        )
