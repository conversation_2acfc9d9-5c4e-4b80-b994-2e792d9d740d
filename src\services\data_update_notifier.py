"""
数据更新通知服务
"""
import asyncio
import logging
from typing import Any, Dict, Optional
from datetime import datetime

from src.services.websocket_manager import manager

logger = logging.getLogger(__name__)

class DataUpdateNotifier:
    """数据更新通知器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def notify_surgery_update(self, surgery_id: int, update_type: str = "surgery_status"):
        """
        通知手术数据更新
        
        Args:
            surgery_id: 手术ID
            update_type: 更新类型
        """
        try:
            update_data = {
                "surgery_id": surgery_id,
                "timestamp": datetime.now().isoformat()
            }
            
            await manager.send_update(update_type, update_data)
            self.logger.info(f"已发送手术更新通知: {update_type}, ID: {surgery_id}")
            
        except Exception as e:
            self.logger.error(f"发送手术更新通知失败: {e}")
    
    async def notify_statistics_update(self, date_str: Optional[str] = None):
        """
        通知统计数据更新
        
        Args:
            date_str: 日期字符串
        """
        try:
            update_data = {
                "date": date_str or datetime.now().date().isoformat(),
                "timestamp": datetime.now().isoformat()
            }
            
            await manager.send_update("statistics", update_data)
            self.logger.info(f"已发送统计更新通知: {date_str}")
            
        except Exception as e:
            self.logger.error(f"发送统计更新通知失败: {e}")
    
    async def notify_new_surgery(self, surgery_data: Dict[str, Any]):
        """
        通知新增手术
        
        Args:
            surgery_data: 手术数据
        """
        try:
            update_data = {
                "surgery": surgery_data,
                "timestamp": datetime.now().isoformat()
            }
            
            await manager.send_update("new_surgery", update_data)
            self.logger.info(f"已发送新增手术通知: {surgery_data.get('patient_name')}")
            
        except Exception as e:
            self.logger.error(f"发送新增手术通知失败: {e}")
    
    async def notify_system_alert(self, alert_type: str, message: str, level: str = "warning"):
        """
        发送系统警报
        
        Args:
            alert_type: 警报类型
            message: 警报消息
            level: 警报级别
        """
        try:
            await manager.send_notification(alert_type, message, level)
            self.logger.info(f"已发送系统警报: {alert_type} - {message}")
            
        except Exception as e:
            self.logger.error(f"发送系统警报失败: {e}")
    
    async def notify_equipment_status(self, equipment_data: Dict[str, Any]):
        """
        通知设备状态更新
        
        Args:
            equipment_data: 设备状态数据
        """
        try:
            update_data = {
                "equipment": equipment_data,
                "timestamp": datetime.now().isoformat()
            }
            
            await manager.send_update("equipment_status", update_data)
            self.logger.info("已发送设备状态更新通知")
            
        except Exception as e:
            self.logger.error(f"发送设备状态更新通知失败: {e}")
    
    def notify_surgery_update_sync(self, surgery_id: int, update_type: str = "surgery_status"):
        """
        同步方式通知手术数据更新（用于非异步环境）
        
        Args:
            surgery_id: 手术ID
            update_type: 更新类型
        """
        try:
            # 在新的事件循环中运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.notify_surgery_update(surgery_id, update_type))
            loop.close()
            
        except Exception as e:
            self.logger.error(f"同步发送手术更新通知失败: {e}")
    
    def notify_statistics_update_sync(self, date_str: Optional[str] = None):
        """
        同步方式通知统计数据更新
        
        Args:
            date_str: 日期字符串
        """
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.notify_statistics_update(date_str))
            loop.close()
            
        except Exception as e:
            self.logger.error(f"同步发送统计更新通知失败: {e}")

# 全局通知器实例
notifier = DataUpdateNotifier()
