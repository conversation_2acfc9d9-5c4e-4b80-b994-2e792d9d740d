/**
 * 医疗图标库 - 专为麻醉科交班系统设计
 * 使用SVG图标，支持自定义颜色和大小
 */

class MedicalIcons {
    constructor() {
        this.defaultSize = 24;
        this.defaultColor = '#ffffff';
    }

    /**
     * 生成SVG图标
     * @param {string} path - SVG路径
     * @param {number} size - 图标大小
     * @param {string} color - 图标颜色
     * @param {string} viewBox - 视图框
     * @returns {string} SVG字符串
     */
    createSVG(path, size = this.defaultSize, color = this.defaultColor, viewBox = '0 0 24 24') {
        return `<svg width="${size}" height="${size}" viewBox="${viewBox}" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="${path}" fill="${color}"/>
        </svg>`;
    }

    /**
     * 医生图标 - Health Icons
     */
    doctor(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM12 7C13.1 7 14 7.9 14 9V11H16V13H14V22H10V13H8V11H10V9C10 7.9 10.9 7 12 7ZM12 8.5C11.7 8.5 11.5 8.7 11.5 9V10.5H12.5V9C12.5 8.7 12.3 8.5 12 8.5Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 护士图标 - Health Icons
     */
    nurse(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM12 7C13.1 7 14 7.9 14 9V11H16V13H14V22H10V13H8V11H10V9C10 7.9 10.9 7 12 7ZM11 9.5H13V10.5H11V9.5ZM11.5 8C11.2 8 11 8.2 11 8.5S11.2 9 11.5 9S12 8.8 12 8.5S11.8 8 11.5 8ZM12.5 8C12.2 8 12 8.2 12 8.5S12.2 9 12.5 9S13 8.8 13 8.5S12.8 8 12.5 8Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 手术图标 - Health Icons (Surgical Department)
     */
    surgery(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM19 7H5C4.4 7 4 7.4 4 8V16C4 16.6 4.4 17 5 17H19C19.6 17 20 16.6 20 16V8C20 7.4 19.6 7 19 7ZM18 15H6V9H18V15ZM8 11H16V13H8V11ZM10 19H14V21H10V19Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 急诊图标 - Health Icons (Emergency)
     */
    emergency(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M12 2L15.09 8.26L22 9L15.09 9.74L12 16L8.91 9.74L2 9L8.91 8.26L12 2ZM11 7H13V11H17V13H13V17H11V13H7V11H11V7Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 心率图标
     */
    heartRate(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M12.8 5.4C11.8 4.5 10.2 4.5 9.2 5.4L12 8.2L14.8 5.4C13.8 4.5 12.2 4.5 11.2 6.4ZM3.5 13H6L8.5 7L11.5 17L14 11H20.5V13H15L13.5 16L10.5 6L8 12H3.5V13Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 药物图标
     */
    medication(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M6 3H18C19.1 3 20 3.9 20 5V19C20 20.1 19.1 21 18 21H6C4.9 21 4 20.1 4 19V5C4 3.9 4.9 3 6 3ZM6 5V19H18V5H6ZM8 7H16V9H8V7ZM8 11H16V13H8V11ZM8 15H13V17H8V15Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 监护图标
     */
    monitor(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M20 3H4C2.9 3 2 3.9 2 5V15C2 16.1 2.9 17 4 17H9L8 19V20H16V19L15 17H20C21.1 17 22 16.1 22 15V5C22 3.9 21.1 3 20 3ZM20 15H4V5H20V15ZM6 7H18V13H6V7ZM8 9V11H16V9H8Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 麻醉图标
     */
    anesthesia(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M12 2C13.1 2 14 2.9 14 4V6H16C17.1 6 18 6.9 18 8V10C18 11.1 17.1 12 16 12H14V14C14 15.1 13.1 16 12 16S10 15.1 10 14V12H8C6.9 12 6 11.1 6 10V8C6 6.9 6.9 6 8 6H10V4C10 2.9 10.9 2 12 2ZM12 4V6H10V8H12V10H14V8H16V10H14V12H12V14H10V12H8V10H10V8H8V6H10V4H12ZM12 18C13.1 18 14 18.9 14 20S13.1 22 12 22S10 21.1 10 20S10.9 18 12 18Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 病床图标
     */
    bed(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M20 9V6H4V9H2V15H4V12H20V15H22V9H20ZM6 8H18V10H6V8ZM7 4C7.55 4 8 4.45 8 5S7.55 6 7 6S6 5.55 6 5S6.45 4 7 4ZM4 17H20V19H4V17Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 时钟图标
     */
    clock(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM12 20C7.59 20 4 16.41 4 12S7.59 4 12 4S20 7.59 20 12S16.41 20 12 20ZM12.5 7H11V13L16.25 16.15L17 14.92L12.5 12.25V7Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 统计图标
     */
    statistics(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M5 9.2H7V19H5V9.2ZM10.6 5H12.4V19H10.6V5ZM16.2 13H18V19H16.2V13ZM2 21H22V19H2V21Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 警告图标
     */
    warning(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M1 21H23L12 2L1 21ZM13 18H11V16H13V18ZM13 14H11V10H13V14Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 成功图标
     */
    success(size = this.defaultSize, color = this.defaultColor) {
        const path = 'M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z';
        return this.createSVG(path, size, color);
    }

    /**
     * 获取图标HTML
     * @param {string} iconName - 图标名称
     * @param {number} size - 图标大小
     * @param {string} color - 图标颜色
     * @returns {string} 图标HTML
     */
    getIcon(iconName, size = this.defaultSize, color = this.defaultColor) {
        if (typeof this[iconName] === 'function') {
            return this[iconName](size, color);
        }
        console.warn(`图标 "${iconName}" 不存在`);
        return '';
    }

    /**
     * 获取所有可用图标名称
     * @returns {Array} 图标名称数组
     */
    getAvailableIcons() {
        return [
            'doctor', 'nurse', 'surgery', 'emergency', 'heartRate',
            'medication', 'monitor', 'anesthesia', 'bed', 'clock',
            'statistics', 'warning', 'success'
        ];
    }
}

// 创建全局实例
window.medicalIcons = new MedicalIcons();

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MedicalIcons;
}

console.log('✅ 医疗图标库加载完成');
console.log('📋 可用图标:', window.medicalIcons.getAvailableIcons());
