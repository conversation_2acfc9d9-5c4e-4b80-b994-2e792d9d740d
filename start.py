#!/usr/bin/env python3
"""
麻醉科交班大屏系统 - 快速启动脚本
现代化模块化组件系统 v2.0.0
"""

import os
import sys
import webbrowser
import time
from pathlib import Path

def main():
    print("🏥 麻醉科交班大屏系统 v2.0.0")
    print("=" * 50)
    print("🎨 现代化模块化组件系统")
    print("🌈 5个工作日主题自动切换")
    print("🧩 可复用卡片组件库")
    print("📱 完全响应式设计")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return
    
    # 检查虚拟环境
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  警告: 建议在虚拟环境中运行")
        print("   创建虚拟环境: python -m venv venv")
        print("   激活虚拟环境: venv\\Scripts\\activate (Windows) 或 source venv/bin/activate (Linux/Mac)")
        print()
    
    # 检查依赖
    try:
        import fastapi
        import uvicorn
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("   安装依赖: pip install -r requirements.txt")
        return
    
    # 启动服务器
    print("\n🚀 启动服务器...")
    print("   端口: 8001")
    print("   主题: 根据星期几自动切换")
    print("   模式: 手动控制模式")
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)
        print("\n🌐 正在打开浏览器...")
        print("   主系统: http://localhost:8001")
        print("   主题演示: http://localhost:8001/static/demo-themes.html")
        
        try:
            webbrowser.open('http://localhost:8001')
        except Exception as e:
            print(f"   无法自动打开浏览器: {e}")
            print("   请手动访问: http://localhost:8001")
    
    # 在后台线程中打开浏览器
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动主应用
    try:
        from src.main import app
        import uvicorn
        
        print("\n📊 系统功能:")
        print("   • 5个工作日主题 (星期一到五)")
        print("   • 统计卡片 + 人员卡片 + 图表组件")
        print("   • 手动页面切换控制")
        print("   • 键盘快捷键控制")
        print("   • 响应式设计")

        print("\n⌨️  快捷键:")
        print("   • ← → : 切换页面")
        print("   • R : 刷新数据")
        print("   • C : 控制面板")
        print("   • H : 帮助信息")
        
        print("\n🎯 页面类型:")
        print("   1. 概览页面 - 值班人员 + 今日统计")
        print("   2. 今日统计 - 详细手术数据")
        print("   3. 昨日回顾 - 昨日统计回顾")
        print("   4. 手术列表 - 详细安排信息")
        print("   5. 科室分布 - 各科室统计")
        
        print("\n" + "=" * 50)
        print("🎉 系统启动成功! 按 Ctrl+C 停止服务")
        print("=" * 50)
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n\n👋 系统已停止")
        print("感谢使用麻醉科交班大屏系统!")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("请检查配置和依赖是否正确安装")

if __name__ == "__main__":
    main()
