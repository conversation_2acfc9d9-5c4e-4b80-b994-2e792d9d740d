"""
统计分析服务
"""
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging

from src.database.connection import db_connection
from src.models.statistics import Statistics

logger = logging.getLogger(__name__)

class StatisticsService:
    """统计分析服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_daily_statistics(self, target_date: date) -> Statistics:
        """
        获取指定日期的统计数据
        
        Args:
            target_date: 目标日期
            
        Returns:
            Statistics: 统计数据对象
        """
        try:
            date_str = target_date.isoformat()
            
            with db_connection.get_db_context() as conn:
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_surgeries,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                        SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled,
                        AVG(CASE WHEN end_time IS NOT NULL AND status = 'completed'
                            THEN (julianday(end_time) - julianday(start_time)) * 24 * 60 
                            ELSE NULL END) as avg_duration
                    FROM surgeries 
                    WHERE DATE(start_time) = ?
                """, (date_str,))
                
                result = cursor.fetchone()
                
                total = result[0] or 0
                completed = result[1] or 0
                cancelled = result[2] or 0
                in_progress = result[3] or 0
                scheduled = result[4] or 0
                avg_duration = result[5] or 0.0
                
                # 急诊手术数量（这里简化处理，可以根据备注或其他字段判断）
                cursor = conn.execute("""
                    SELECT COUNT(*) FROM surgeries 
                    WHERE DATE(start_time) = ? AND (
                        notes LIKE '%急诊%' OR notes LIKE '%紧急%' OR notes LIKE '%emergency%'
                    )
                """, (date_str,))
                emergency_count = cursor.fetchone()[0] or 0
                
                statistics = Statistics(
                    date=target_date,
                    total_surgeries=total,
                    completed_surgeries=completed,
                    cancelled_surgeries=cancelled,
                    emergency_surgeries=emergency_count,
                    average_duration=round(avg_duration, 2) if avg_duration else 0.0,
                    created_at=datetime.now()
                )
                
                self.logger.info(f"获取{date_str}统计数据成功")
                return statistics
                
        except Exception as e:
            self.logger.error(f"获取统计数据失败: {e}")
            # 返回空统计数据
            return Statistics(
                date=target_date,
                total_surgeries=0,
                completed_surgeries=0,
                cancelled_surgeries=0,
                emergency_surgeries=0,
                average_duration=0.0,
                created_at=datetime.now()
            )
    
    def get_yesterday_statistics(self) -> Statistics:
        """获取昨日统计数据"""
        yesterday = date.today() - timedelta(days=1)
        return self.get_daily_statistics(yesterday)
    
    def get_today_statistics(self) -> Statistics:
        """获取今日统计数据"""
        today = date.today()
        return self.get_daily_statistics(today)
    
    def get_weekly_statistics(self, end_date: Optional[date] = None) -> List[Statistics]:
        """
        获取周统计数据
        
        Args:
            end_date: 结束日期，默认为今天
            
        Returns:
            List[Statistics]: 最近7天的统计数据列表
        """
        if end_date is None:
            end_date = date.today()
        
        statistics_list = []
        
        for i in range(7):
            target_date = end_date - timedelta(days=i)
            stats = self.get_daily_statistics(target_date)
            statistics_list.append(stats)
        
        # 按日期正序排列（最早的在前）
        statistics_list.reverse()
        
        self.logger.info("获取周统计数据成功")
        return statistics_list
    
    def get_monthly_statistics(self, year: int, month: int) -> List[Statistics]:
        """
        获取月统计数据
        
        Args:
            year: 年份
            month: 月份
            
        Returns:
            List[Statistics]: 月统计数据列表
        """
        statistics_list = []
        
        # 获取该月的第一天和最后一天
        first_day = date(year, month, 1)
        if month == 12:
            last_day = date(year + 1, 1, 1) - timedelta(days=1)
        else:
            last_day = date(year, month + 1, 1) - timedelta(days=1)
        
        current_date = first_day
        while current_date <= last_day:
            stats = self.get_daily_statistics(current_date)
            statistics_list.append(stats)
            current_date += timedelta(days=1)
        
        self.logger.info(f"获取{year}年{month}月统计数据成功")
        return statistics_list
    
    def get_surgery_distribution_by_type(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        获取手术类型分布统计
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: 手术类型分布数据
        """
        try:
            with db_connection.get_db_context() as conn:
                cursor = conn.execute("""
                    SELECT 
                        surgery_type,
                        COUNT(*) as count,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count,
                        AVG(CASE WHEN end_time IS NOT NULL AND status = 'completed'
                            THEN (julianday(end_time) - julianday(start_time)) * 24 * 60 
                            ELSE NULL END) as avg_duration
                    FROM surgeries 
                    WHERE DATE(start_time) BETWEEN ? AND ?
                    GROUP BY surgery_type
                    ORDER BY count DESC
                """, (start_date.isoformat(), end_date.isoformat()))
                
                distribution = []
                for row in cursor.fetchall():
                    distribution.append({
                        "surgery_type": row[0],
                        "count": row[1],
                        "completed_count": row[2] or 0,
                        "completion_rate": round((row[2] or 0) / row[1] * 100, 2) if row[1] > 0 else 0,
                        "average_duration": round(row[3], 2) if row[3] else 0.0
                    })
                
                self.logger.info(f"获取手术类型分布统计成功，共{len(distribution)}种类型")
                return distribution
                
        except Exception as e:
            self.logger.error(f"获取手术类型分布失败: {e}")
            return []
    
    def get_surgeon_performance(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        获取医生绩效统计
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: 医生绩效数据
        """
        try:
            with db_connection.get_db_context() as conn:
                cursor = conn.execute("""
                    SELECT 
                        surgeon,
                        COUNT(*) as total_surgeries,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_surgeries,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_surgeries,
                        AVG(CASE WHEN end_time IS NOT NULL AND status = 'completed'
                            THEN (julianday(end_time) - julianday(start_time)) * 24 * 60 
                            ELSE NULL END) as avg_duration
                    FROM surgeries 
                    WHERE DATE(start_time) BETWEEN ? AND ?
                    GROUP BY surgeon
                    ORDER BY total_surgeries DESC
                """, (start_date.isoformat(), end_date.isoformat()))
                
                performance = []
                for row in cursor.fetchall():
                    total = row[1]
                    completed = row[2] or 0
                    cancelled = row[3] or 0
                    
                    performance.append({
                        "surgeon": row[0],
                        "total_surgeries": total,
                        "completed_surgeries": completed,
                        "cancelled_surgeries": cancelled,
                        "completion_rate": round(completed / total * 100, 2) if total > 0 else 0,
                        "cancellation_rate": round(cancelled / total * 100, 2) if total > 0 else 0,
                        "average_duration": round(row[4], 2) if row[4] else 0.0
                    })
                
                self.logger.info(f"获取医生绩效统计成功，共{len(performance)}位医生")
                return performance
                
        except Exception as e:
            self.logger.error(f"获取医生绩效统计失败: {e}")
            return []
    
    def get_room_utilization(self, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """
        获取手术间利用率统计
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: 手术间利用率数据
        """
        try:
            with db_connection.get_db_context() as conn:
                cursor = conn.execute("""
                    SELECT 
                        room_number,
                        COUNT(*) as total_surgeries,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_surgeries,
                        SUM(CASE WHEN end_time IS NOT NULL AND status = 'completed'
                            THEN (julianday(end_time) - julianday(start_time)) * 24 * 60 
                            ELSE 0 END) as total_duration
                    FROM surgeries 
                    WHERE DATE(start_time) BETWEEN ? AND ?
                    GROUP BY room_number
                    ORDER BY total_surgeries DESC
                """, (start_date.isoformat(), end_date.isoformat()))
                
                utilization = []
                for row in cursor.fetchall():
                    total = row[1]
                    completed = row[2] or 0
                    total_duration = row[3] or 0
                    
                    # 计算工作日数
                    work_days = (end_date - start_date).days + 1
                    # 假设每天工作8小时
                    available_minutes = work_days * 8 * 60
                    utilization_rate = (total_duration / available_minutes * 100) if available_minutes > 0 else 0
                    
                    utilization.append({
                        "room_number": row[0],
                        "total_surgeries": total,
                        "completed_surgeries": completed,
                        "total_duration_minutes": round(total_duration, 2),
                        "utilization_rate": round(utilization_rate, 2),
                        "average_surgeries_per_day": round(total / work_days, 2) if work_days > 0 else 0
                    })
                
                self.logger.info(f"获取手术间利用率统计成功，共{len(utilization)}个手术间")
                return utilization
                
        except Exception as e:
            self.logger.error(f"获取手术间利用率统计失败: {e}")
            return []
    
    def get_trend_analysis(self, days: int = 30) -> Dict[str, Any]:
        """
        获取趋势分析数据
        
        Args:
            days: 分析天数
            
        Returns:
            Dict[str, Any]: 趋势分析数据
        """
        try:
            end_date = date.today()
            start_date = end_date - timedelta(days=days-1)
            
            # 获取每日统计数据
            daily_stats = []
            current_date = start_date
            while current_date <= end_date:
                stats = self.get_daily_statistics(current_date)
                daily_stats.append(stats)
                current_date += timedelta(days=1)
            
            # 计算趋势指标
            total_surgeries = sum(s.total_surgeries for s in daily_stats)
            total_completed = sum(s.completed_surgeries for s in daily_stats)
            total_cancelled = sum(s.cancelled_surgeries for s in daily_stats)
            
            # 计算平均值
            avg_daily_surgeries = total_surgeries / days if days > 0 else 0
            overall_completion_rate = (total_completed / total_surgeries * 100) if total_surgeries > 0 else 0
            overall_cancellation_rate = (total_cancelled / total_surgeries * 100) if total_surgeries > 0 else 0
            
            # 计算最近7天与前7天的对比
            if days >= 14:
                recent_7_days = daily_stats[-7:]
                previous_7_days = daily_stats[-14:-7]
                
                recent_total = sum(s.total_surgeries for s in recent_7_days)
                previous_total = sum(s.total_surgeries for s in previous_7_days)
                
                surgery_trend = ((recent_total - previous_total) / previous_total * 100) if previous_total > 0 else 0
            else:
                surgery_trend = 0
            
            trend_data = {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "summary": {
                    "total_surgeries": total_surgeries,
                    "total_completed": total_completed,
                    "total_cancelled": total_cancelled,
                    "average_daily_surgeries": round(avg_daily_surgeries, 2),
                    "overall_completion_rate": round(overall_completion_rate, 2),
                    "overall_cancellation_rate": round(overall_cancellation_rate, 2)
                },
                "trends": {
                    "surgery_count_trend": round(surgery_trend, 2)
                },
                "daily_data": [s.to_dict() for s in daily_stats]
            }
            
            self.logger.info(f"获取{days}天趋势分析数据成功")
            return trend_data
            
        except Exception as e:
            self.logger.error(f"获取趋势分析数据失败: {e}")
            return {
                "period": {"start_date": "", "end_date": "", "days": 0},
                "summary": {},
                "trends": {},
                "daily_data": []
            }
