"""
手术数据模型
"""
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from enum import Enum

class SurgeryStatus(str, Enum):
    """手术状态枚举"""
    SCHEDULED = "scheduled"      # 已安排
    IN_PROGRESS = "in_progress"  # 进行中
    COMPLETED = "completed"      # 已完成
    CANCELLED = "cancelled"      # 已取消

@dataclass
class Surgery:
    """手术信息模型"""
    id: Optional[int] = None
    department: str = ""  # 科室
    patient_name: str = ""  # 患者姓名
    age: int = 0  # 年龄
    bed_number: str = ""  # 床号
    surgery_type: str = ""  # 手术方式
    anesthesia_type: str = ""  # 麻醉方式
    allergy_history: str = ""  # 过敏史
    special_situation: str = ""  # 特殊情况
    surgery_date: Optional[date] = None  # 手术日期
    is_emergency: bool = False  # 是否急诊
    status: SurgeryStatus = SurgeryStatus.SCHEDULED
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'department': self.department,
            'patient_name': self.patient_name,
            'age': self.age,
            'bed_number': self.bed_number,
            'surgery_type': self.surgery_type,
            'anesthesia_type': self.anesthesia_type,
            'allergy_history': self.allergy_history,
            'special_situation': self.special_situation,
            'surgery_date': self.surgery_date.isoformat() if self.surgery_date else None,
            'is_emergency': self.is_emergency,
            'status': self.status.value,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Surgery':
        """从字典创建Surgery对象"""
        return cls(
            id=data.get('id'),
            patient_name=data.get('patient_name', ''),
            patient_id=data.get('patient_id', ''),
            surgery_type=data.get('surgery_type', ''),
            anesthesia_type=data.get('anesthesia_type', ''),
            surgeon=data.get('surgeon', ''),
            anesthesiologist=data.get('anesthesiologist', ''),
            room_number=data.get('room_number', ''),
            start_time=datetime.fromisoformat(data['start_time']) if data.get('start_time') else None,
            end_time=datetime.fromisoformat(data['end_time']) if data.get('end_time') else None,
            status=SurgeryStatus(data.get('status', SurgeryStatus.SCHEDULED)),
            notes=data.get('notes'),
            created_at=datetime.fromisoformat(data['created_at']) if data.get('created_at') else None,
            updated_at=datetime.fromisoformat(data['updated_at']) if data.get('updated_at') else None,
        )
    
    @classmethod
    def from_row(cls, row) -> 'Surgery':
        """从数据库行创建Surgery对象"""
        return cls(
            id=row['id'],
            department=row['department'],
            patient_name=row['patient_name'],
            age=row['age'],
            bed_number=row['bed_number'],
            surgery_type=row['surgery_type'],
            anesthesia_type=row['anesthesia_type'],
            allergy_history=row['allergy_history'],
            special_situation=row['special_situation'],
            surgery_date=date.fromisoformat(row['surgery_date']) if row['surgery_date'] else None,
            is_emergency=bool(row['is_emergency']),
            status=SurgeryStatus(row['status']),
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None,
        )
    
    def validate(self) -> List[str]:
        """验证数据有效性"""
        errors = []
        
        if not self.patient_name.strip():
            errors.append("患者姓名不能为空")
        
        if not self.patient_id.strip():
            errors.append("患者ID不能为空")
        
        if not self.surgery_type.strip():
            errors.append("手术类型不能为空")
        
        if not self.anesthesia_type.strip():
            errors.append("麻醉方式不能为空")
        
        if not self.surgeon.strip():
            errors.append("主刀医生不能为空")
        
        if not self.anesthesiologist.strip():
            errors.append("麻醉医生不能为空")
        
        if not self.room_number.strip():
            errors.append("手术间号不能为空")
        
        if not self.start_time:
            errors.append("开始时间不能为空")
        
        if self.end_time and self.start_time and self.end_time < self.start_time:
            errors.append("结束时间不能早于开始时间")
        
        return errors
    
    def is_valid(self) -> bool:
        """检查数据是否有效"""
        return len(self.validate()) == 0
    
    def duration_minutes(self) -> Optional[int]:
        """计算手术时长（分钟）"""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            return int(delta.total_seconds() / 60)
        return None
