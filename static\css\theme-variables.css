/* 统一主题变量管理系统 */

:root {
    /* ===== 基础设计令牌 ===== */
    
    /* 字体大小 - PPT设计规范 */
    --font-size-xs: 18px;      /* 辅助信息 */
    --font-size-sm: 20px;      /* 小标签 */
    --font-size-base: 22px;    /* 正文基础字号 */
    --font-size-lg: 26px;      /* 重要信息 */
    --font-size-xl: 30px;      /* 数据标题 */
    --font-size-2xl: 36px;     /* 卡片标题 */
    --font-size-3xl: 42px;     /* 页面副标题 */
    --font-size-4xl: 48px;     /* 页面主标题 */
    --font-size-5xl: 60px;     /* 大屏标题 */
    --font-size-6xl: 72px;     /* 超大标题 */
    
    /* 间距 */
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 32px;
    --spacing-xl: 48px;
    --spacing-2xl: 64px;
    
    /* 圆角 */
    --border-radius-xs: 4px;
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --border-radius-2xl: 32px;
    
    /* 阴影 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
    
    /* 过渡动画 */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* ===== 主题变量默认值 ===== */
    /* 这些变量会被 ThemeSystem 动态覆盖 */
    
    /* 主色调 */
    --theme-primary: #5470c6;
    --theme-secondary: #91cc75;
    --theme-accent: #fac858;
    --theme-tertiary: #ee6666;
    --theme-quaternary: #73c0de;
    
    /* 背景色 */
    --theme-background: linear-gradient(135deg, #7c9cc9 0%, #8fb3d3 50%, #a2c4dc 100%);
    --theme-header-bg: rgba(84, 112, 198, 0.85);
    --theme-content-bg: rgba(240, 244, 248, 0.95);
    --theme-status-bg: rgba(84, 112, 198, 0.80);
    
    /* 卡片背景 */
    --theme-card-bg: rgba(255, 255, 255, 0.98);
    --theme-card-gradient: linear-gradient(135deg, rgba(84, 112, 198, 0.08) 0%, rgba(145, 204, 117, 0.05) 100%);

    /* 统计卡片专用渐变色 */
    --theme-primary-card-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --theme-warning-card-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --theme-success-card-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --theme-info-card-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --theme-secondary-card-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --theme-accent-card-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

    /* 人员卡片专用渐变色 */
    --theme-doctor-card-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --theme-nurse-card-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --theme-staff-card-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* 文字颜色 */
    --theme-text-primary: #2c3e50;
    --theme-text-secondary: #7f8c8d;
    --theme-text-light: #ffffff;
    --theme-text-muted: rgba(255, 255, 255, 0.85);
    
    /* 状态颜色 */
    --theme-success: #91cc75;
    --theme-warning: #fac858;
    --theme-error: #ee6666;
    --theme-info: #73c0de;
    
    /* 效果 */
    --theme-shadow: 0 8px 32px rgba(84, 112, 198, 0.15);
    --theme-glass: rgba(84, 112, 198, 0.85);
    --theme-border: rgba(255, 255, 255, 0.18);
    
    /* 边框和分割线 */
    --theme-border-light: rgba(240, 244, 248, 0.6);
    --theme-divider: rgba(145, 204, 117, 0.3);
}

/* ===== 兼容性映射 ===== */
/* 将旧的变量名映射到新的主题变量 */

:root {
    /* main.css 兼容性 */
    --primary-color: var(--theme-primary);
    --primary-light: var(--theme-secondary);
    --primary-dark: var(--theme-tertiary);
    
    --success-color: var(--theme-success);
    --warning-color: var(--theme-warning);
    --error-color: var(--theme-error);
    --info-color: var(--theme-info);
    
    --background-color: var(--theme-content-bg);
    --surface-color: var(--theme-card-bg);
    --text-primary: var(--theme-text-primary);
    --text-secondary: var(--theme-text-secondary);
    --divider-color: var(--theme-divider);
    
    /* modern-layout.css 兼容性 */
    --base-font-size: var(--font-size-xl);
    --small-font-size: var(--font-size-base);
    --medium-font-size: var(--font-size-2xl);
    --large-font-size: var(--font-size-4xl);
    --xl-font-size: var(--font-size-5xl);
    --xxl-font-size: 48px;
    --huge-font-size: 64px;

    /* 高度设置 */
    --header-height: 120px;
    --status-height: 60px;
}

/* ===== 主题特定样式 ===== */

/* 星期一 - 科技蓝 */
.theme-monday {
    --theme-gradient: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
}

/* 星期二 - 复古棕 */
.theme-tuesday {
    --theme-gradient: linear-gradient(135deg, #92400e 0%, #d97706 50%, #f59e0b 100%);
}

/* 星期三 - 清新灰 */
.theme-wednesday {
    --theme-gradient: linear-gradient(135deg, #374151 0%, #6b7280 50%, #9ca3af 100%);
}

/* 星期四 - 马卡龙 */
.theme-thursday {
    --theme-gradient: linear-gradient(135deg, #0891b2 0%, #06b6d4 50%, #67e8f9 100%);
}

/* 星期五 - 罗马红 */
.theme-friday {
    --theme-gradient: linear-gradient(135deg, #b91c1c 0%, #dc2626 50%, #f87171 100%);
}

/* ===== 全局样式增强 ===== */

/* 确保所有文字都使用主题颜色 */
* {
    color: inherit;
}

/* 标题元素默认使用主题文字颜色 */
h1, h2, h3, h4, h5, h6 {
    color: var(--theme-text-light);
}

/* 卡片元素使用主题背景 */
.card, .stat-card, .modern-card {
    background: var(--theme-card-bg);
    border: 1px solid var(--theme-border);
    box-shadow: var(--theme-shadow);
}

/* 按钮使用主题颜色 */
.btn-primary {
    background-color: var(--theme-primary);
    border-color: var(--theme-primary);
}

.btn-success {
    background-color: var(--theme-success);
    border-color: var(--theme-success);
}

.btn-warning {
    background-color: var(--theme-warning);
    border-color: var(--theme-warning);
}

.btn-error {
    background-color: var(--theme-error);
    border-color: var(--theme-error);
}

/* 状态指示器 */
.status-success { color: var(--theme-success); }
.status-warning { color: var(--theme-warning); }
.status-error { color: var(--theme-error); }
.status-info { color: var(--theme-info); }

/* 图标颜色 */
.icon-primary { color: var(--theme-primary); }
.icon-secondary { color: var(--theme-secondary); }
.icon-accent { color: var(--theme-accent); }

/* 边框颜色 */
.border-theme { border-color: var(--theme-border); }
.border-primary { border-color: var(--theme-primary); }

/* 背景颜色工具类 */
.bg-theme-primary { background-color: var(--theme-primary); }
.bg-theme-secondary { background-color: var(--theme-secondary); }
.bg-theme-card { background: var(--theme-card-bg); }
.bg-theme-glass { background: var(--theme-glass); }

/* 文字颜色工具类 */
.text-theme-primary { color: var(--theme-text-primary); }
.text-theme-secondary { color: var(--theme-text-secondary); }
.text-theme-light { color: var(--theme-text-light); }
.text-theme-muted { color: var(--theme-text-muted); }

/* 阴影工具类 */
.shadow-theme { box-shadow: var(--theme-shadow); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* 圆角工具类 */
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }

/* 间距工具类 */
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
