/**
 * 现代化页面管理器
 * 基于模块化组件系统的页面管理
 */

class ModernPageManager {
    constructor(config = {}) {
        this.config = {
            autoPlay: false, // 移除自动播放
            interval: 10000,
            showThemeSelector: false, // 不显示主题选择器
            enableKeyboard: true,
            ...config
        };

        this.currentPageIndex = 0;
        this.pages = [];
        this.isPlaying = false; // 默认不自动播放
        this.intervalId = null;
        
        this.dutyDoctor = '张医生';
        this.dutyNurse1 = '李护士';
        this.dutyNurse2 = '王护士';
        
        this.todayStats = null;
        this.yesterdayStats = null;
        this.surgeries = [];

        this.init();
    }

    async init() {
        try {
            // 确保主题系统和组件库已加载
            if (!window.themeSystem || !window.cardComponents) {
                console.error('主题系统或组件库未加载');
                return;
            }

            // 加载数据
            await this.loadData();
            
            // 生成页面
            this.generatePages();
            
            // 设置事件监听
            this.setupEventListeners();
            
            // 显示第一页
            this.showPage(0);

            // 初始化状态显示
            this.updateThemeDisplay(window.themeSystem.getCurrentTheme().name);
            this.updateAutoPlayStatus();

            // 不启动自动播放
            // if (this.isPlaying) {
            //     this.startAutoPlay();
            // }

            console.log('现代化页面管理器初始化完成');
        } catch (error) {
            console.error('页面管理器初始化失败:', error);
        }
    }

    async loadData() {
        try {
            // 加载今日统计
            const todayResponse = await fetch('/api/stats/today');
            this.todayStats = await todayResponse.json();

            // 加载昨日统计
            const yesterdayResponse = await fetch('/api/stats/yesterday');
            this.yesterdayStats = await yesterdayResponse.json();

            // 加载手术列表
            const surgeriesResponse = await fetch('/api/surgeries/today');
            this.surgeries = await surgeriesResponse.json();

        } catch (error) {
            console.error('数据加载失败:', error);
            // 使用模拟数据
            this.todayStats = { total_surgeries: 12, emergency_count: 3 };
            this.yesterdayStats = { total_surgeries: 15, emergency_count: 2 };
            this.surgeries = [];
        }
    }

    generatePages() {
        this.pages = [
            this.generateOverviewPage(),
            this.generateTodayStatsPage(),
            this.generateYesterdayStatsPage(),
            this.generateSurgeriesPage(),
            this.generateDepartmentPage()
        ];
    }

    generateOverviewPage() {
        const emergencyCount = this.todayStats?.emergency_count || 0;
        const totalSurgeries = this.todayStats?.total_surgeries || 0;
        const scheduledSurgeries = totalSurgeries - emergencyCount;

        // 创建值班人员卡片 - 使用专用的卡片渐变色，与标题栏区分
        const dutyCards = [
            window.cardComponents.createPersonCard({
                name: this.dutyDoctor,
                role: '值班医生',
                icon: window.medicalIcons?.getIcon('doctor', 24, '#ffffff') || '👨‍⚕️',
                status: 'active',
                gradient: 'var(--theme-doctor-card-gradient)'
            }),
            window.cardComponents.createPersonCard({
                name: this.dutyNurse1,
                role: '值班护士',
                icon: window.medicalIcons?.getIcon('nurse', 24, '#ffffff') || '👩‍⚕️',
                status: 'active',
                gradient: 'var(--theme-nurse-card-gradient)'
            }),
            window.cardComponents.createPersonCard({
                name: this.dutyNurse2,
                role: '值班护士',
                icon: window.medicalIcons?.getIcon('nurse', 24, '#ffffff') || '👩‍⚕️',
                status: 'active',
                gradient: 'var(--theme-staff-card-gradient)'
            })
        ];

        // 创建统计卡片 - 使用 ECharts 主题色
        const statCards = [
            window.cardComponents.createStatCard({
                title: '总手术量',
                value: totalSurgeries,
                icon: window.medicalIcons?.getIcon('surgery', 24, '#ffffff') || '🏥',
                theme: 'primary',
                showTrend: true,
                trendValue: 5
            }),
            window.cardComponents.createStatCard({
                title: '急诊手术',
                value: emergencyCount,
                icon: window.medicalIcons?.getIcon('emergency', 24, '#ffffff') || '🚨',
                theme: 'error',
                showTrend: true,
                trendValue: -2
            }),
            window.cardComponents.createStatCard({
                title: '择期手术',
                value: scheduledSurgeries,
                icon: window.medicalIcons?.getIcon('clock', 24, '#ffffff') || '⏰',
                theme: 'success',
                showTrend: true,
                trendValue: 8
            })
        ];

        const dutyGrid = window.cardComponents.createGridContainer({
            columns: 3,
            gap: '24px',
            className: 'duty-grid',
            children: dutyCards
        });

        const statsGrid = window.cardComponents.createGridContainer({
            columns: 3,
            gap: '24px',
            className: 'stats-grid',
            children: statCards
        });

        // 创建特色卡片 - 使用当前主题的渐变色
        const currentTheme = window.themeSystem.getCurrentTheme();
        const featureCard = window.cardComponents.createFeatureCard({
            title: '智能交班助手',
            description: '智能分析手术数据，自动生成交班报告，提升医护人员工作效率。',
            buttonText: '立即体验',
            icon: '🏥',
            gradient: `linear-gradient(135deg, ${currentTheme.primary} 0%, ${currentTheme.secondary} 100%)`
        });

        return {
            title: '今日交班概览',
            subtitle: 'Daily Handover Overview',
            content: `
                <div class="overview-section">
                    <h2 class="section-title">值班人员</h2>
                    ${dutyGrid}
                </div>
                <div class="overview-section">
                    <h2 class="section-title">今日概览</h2>
                    ${statsGrid}
                </div>
                <div class="overview-section">
                    <h2 class="section-title">智能功能</h2>
                    <div style="display: flex; justify-content: center; margin-top: 24px;">
                        ${featureCard}
                    </div>
                </div>
            `
        };
    }

    generateTodayStatsPage() {
        const emergencyCount = this.todayStats?.emergency_count || 0;
        const totalSurgeries = this.todayStats?.total_surgeries || 0;
        const scheduledSurgeries = totalSurgeries - emergencyCount;

        const statCards = [
            window.cardComponents.createStatCard({
                title: '总手术量',
                value: totalSurgeries,
                icon: window.medicalIcons?.getIcon('surgery', 24, '#ffffff') || '🏥',
                theme: 'primary',
                size: 'large'
            }),
            window.cardComponents.createStatCard({
                title: '急诊手术',
                value: emergencyCount,
                icon: window.medicalIcons?.getIcon('emergency', 24, '#ffffff') || '🚨',
                theme: 'warning',
                size: 'large'
            }),
            window.cardComponents.createStatCard({
                title: '择期手术',
                value: scheduledSurgeries,
                icon: window.medicalIcons?.getIcon('clock', 24, '#ffffff') || '⏰',
                theme: 'success',
                size: 'large'
            })
        ];

        const chartCard = window.cardComponents.createChartCard({
            title: '今日手术趋势',
            chartId: 'todayTrendChart',
            height: '300px'
        });

        const statsGrid = window.cardComponents.createGridContainer({
            columns: 3,
            gap: '32px',
            children: statCards
        });

        return {
            title: '今日手术统计',
            subtitle: 'Today Surgery Statistics',
            content: `
                ${statsGrid}
                <div style="margin-top: 32px;">
                    ${chartCard}
                </div>
            `
        };
    }

    generateYesterdayStatsPage() {
        const emergencyCount = this.yesterdayStats?.emergency_count || 0;
        const totalSurgeries = this.yesterdayStats?.total_surgeries || 0;
        const scheduledSurgeries = totalSurgeries - emergencyCount;

        const statCards = [
            window.cardComponents.createStatCard({
                title: '总手术量',
                value: totalSurgeries,
                icon: window.medicalIcons?.getIcon('surgery', 24, '#ffffff') || '🏥',
                theme: 'primary',
                size: 'large'
            }),
            window.cardComponents.createStatCard({
                title: '急诊手术',
                value: emergencyCount,
                icon: window.medicalIcons?.getIcon('emergency', 24, '#ffffff') || '🚨',
                theme: 'warning',
                size: 'large'
            }),
            window.cardComponents.createStatCard({
                title: '择期手术',
                value: scheduledSurgeries,
                icon: window.medicalIcons?.getIcon('clock', 24, '#ffffff') || '⏰',
                theme: 'success',
                size: 'large'
            })
        ];

        const statsGrid = window.cardComponents.createGridContainer({
            columns: 3,
            gap: '32px',
            children: statCards
        });

        return {
            title: '昨日手术统计',
            subtitle: 'Yesterday Surgery Statistics',
            content: statsGrid
        };
    }

    generateSurgeriesPage() {
        const surgeryItems = this.surgeries.slice(0, 10).map(surgery => ({
            title: surgery.patient_name || '患者姓名',
            subtitle: `${surgery.surgery_type || '手术类型'} - ${surgery.department || '科室'}`,
            value: surgery.status || '进行中',
            status: surgery.is_emergency ? 'warning' : 'success',
            icon: surgery.is_emergency ? '🚨' : '⏰'
        }));

        const listCard = window.cardComponents.createListCard({
            title: '今日手术列表',
            items: surgeryItems,
            maxHeight: '600px'
        });

        return {
            title: '手术安排详情',
            subtitle: 'Surgery Schedule Details',
            content: listCard
        };
    }

    generateDepartmentPage() {
        // 模拟科室数据
        const departments = [
            { name: '心胸外科', count: 5, icon: '❤️' },
            { name: '神经外科', count: 3, icon: '🧠' },
            { name: '骨科', count: 4, icon: '🦴' },
            { name: '普外科', count: 6, icon: '🏥' },
            { name: '妇产科', count: 2, icon: '👶' }
        ];

        const departmentCards = departments.map(dept =>
            window.cardComponents.createStatCard({
                title: dept.name,
                value: dept.count,
                icon: dept.icon,
                theme: 'primary',
                size: 'medium'
            })
        );

        const grid = window.cardComponents.createGridContainer({
            columns: 3,
            gap: '24px',
            children: departmentCards
        });

        return {
            title: '科室分布统计',
            subtitle: 'Department Distribution',
            content: grid
        };
    }

    showPage(index) {
        if (index < 0 || index >= this.pages.length) return;

        this.currentPageIndex = index;
        const page = this.pages[index];

        // 更新标题栏
        const titleElement = document.querySelector('.header-title');
        const subtitleElement = document.querySelector('.header-subtitle');
        if (titleElement) titleElement.textContent = page.title;
        if (subtitleElement) titleElement.textContent = page.subtitle;

        // 更新内容区
        const contentContainer = document.querySelector('.content-area');
        if (contentContainer) {
            contentContainer.innerHTML = page.content;
        }

        // 更新时间和页面指示器
        this.updateTime();
        this.updatePageIndicator();
    }

    updatePageIndicator() {
        const indicator = document.getElementById('pageIndicator');
        if (indicator) {
            indicator.textContent = `${this.currentPageIndex + 1} / ${this.pages.length}`;
        }
    }

    nextPage() {
        const nextIndex = (this.currentPageIndex + 1) % this.pages.length;
        this.showPage(nextIndex);
    }

    prevPage() {
        const prevIndex = (this.currentPageIndex - 1 + this.pages.length) % this.pages.length;
        this.showPage(prevIndex);
    }

    startAutoPlay() {
        this.stopAutoPlay();
        this.intervalId = setInterval(() => {
            this.nextPage();
        }, this.config.interval);
    }

    stopAutoPlay() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    toggleAutoPlay() {
        this.isPlaying = !this.isPlaying;
        if (this.isPlaying) {
            this.startAutoPlay();
        } else {
            this.stopAutoPlay();
        }
        this.updateAutoPlayStatus();
    }

    updateTime() {
        const timeElement = document.querySelector('.header-time');
        if (timeElement) {
            const now = new Date();
            timeElement.textContent = now.toLocaleString('zh-CN');
        }
    }

    updateThemeDisplay(themeName) {
        const themeNameElement = document.getElementById('currentThemeName');
        if (themeNameElement && themeName) {
            themeNameElement.textContent = themeName;
        }
    }

    updateAutoPlayStatus() {
        const statusElement = document.getElementById('autoPlayStatus');
        if (statusElement) {
            statusElement.textContent = this.isPlaying ? '自动播放' : '已暂停';
        }
    }



    setupEventListeners() {
        if (!this.config.enableKeyboard) return;

        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                    this.prevPage();
                    break;
                case 'ArrowRight':
                    this.nextPage();
                    break;
                case ' ':
                case 'p':
                case 'P':
                    e.preventDefault();
                    this.toggleAutoPlay();
                    break;
                case 'r':
                case 'R':
                    this.refresh();
                    break;
            }
        });

        // 监听主题变更事件
        window.addEventListener('themeChanged', (e) => {
            this.generatePages();
            this.showPage(this.currentPageIndex);
            this.updateThemeDisplay(e.detail.config.name);
        });

        // 定时更新时间
        setInterval(() => {
            this.updateTime();
        }, 1000);
    }

    async refresh() {
        await this.loadData();
        this.generatePages();
        this.showPage(this.currentPageIndex);
    }
}

// 全局实例
window.modernPageManager = null;
