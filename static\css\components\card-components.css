/**
 * 卡片组件样式库
 * 为所有卡片组件提供统一的样式
 */

/* ===== 病人信息卡片样式 ===== */
.modern-patient-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 2vh 2vw;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
}

.modern-patient-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.modern-patient-card[data-risk="high"] {
    border-left: 4px solid var(--theme-error);
}

.modern-patient-card[data-risk="medium"] {
    border-left: 4px solid var(--theme-warning);
}

.modern-patient-card[data-risk="low"] {
    border-left: 4px solid var(--theme-success);
}

/* 病人卡片头部 */
.patient-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5vh;
    padding-bottom: 1vh;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.patient-basic-info .patient-name {
    font-size: 1.8vw;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 0.5vh 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.patient-meta {
    display: flex;
    gap: 1vw;
    font-size: 1vw;
    color: rgba(255, 255, 255, 0.8);
}

.patient-risk-badge {
    padding: 0.5vh 1vw;
    border-radius: 0.5vw;
    font-size: 0.9vw;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 病人卡片主体 */
.patient-card-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2vw;
}

.section-title {
    font-size: 1.3vw;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 1vh 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.subsection-title {
    font-size: 1.1vw;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin: 1vh 0 0.5vh 0;
    display: flex;
    align-items: center;
    gap: 0.5vw;
}

/* 信息网格 */
.patient-info-grid {
    display: grid;
    gap: 0.8vh;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5vh 0;
}

.info-label {
    font-size: 1vw;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.info-value {
    font-size: 1vw;
    color: #ffffff;
    font-weight: 600;
    text-align: right;
}

/* 生命体征网格 */
.vitals-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5vh 1vw;
}

.vital-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3vh 0;
}

.vital-label {
    font-size: 0.9vw;
    color: rgba(255, 255, 255, 0.7);
}

.vital-value {
    font-size: 0.9vw;
    color: #ffffff;
    font-weight: 600;
}

/* 过敏史样式 */
.allergies-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5vh 0.5vw;
}

.allergy-tag {
    background: var(--theme-error);
    color: #ffffff;
    padding: 0.3vh 0.8vw;
    border-radius: 0.4vw;
    font-size: 0.8vw;
    font-weight: 600;
}

/* 用药情况样式 */
.medications-list {
    display: flex;
    flex-direction: column;
    gap: 0.5vh;
}

.medication-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5vh 0.8vw;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0.4vw;
    font-size: 0.9vw;
}

.med-name {
    color: #ffffff;
    font-weight: 600;
}

.med-dosage {
    color: rgba(255, 255, 255, 0.8);
}

.med-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8vw;
}

/* 特殊注意事项样式 */
.special-notes-content {
    background: rgba(255, 255, 255, 0.05);
    padding: 1vh 1vw;
    border-radius: 0.5vw;
    border-left: 3px solid var(--theme-warning);
    font-size: 1vw;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
}

/* 病人卡片操作按钮 */
.patient-card-actions {
    display: flex;
    gap: 1vw;
    margin-top: 1.5vh;
    padding-top: 1vh;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.patient-action-btn {
    flex: 1;
    padding: 0.8vh 1vw;
    border: none;
    border-radius: 0.5vw;
    font-size: 0.9vw;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.patient-action-btn.primary {
    background: var(--theme-primary);
    color: #ffffff;
}

.patient-action-btn.secondary {
    background: var(--theme-secondary);
    color: #ffffff;
}

.patient-action-btn.warning {
    background: var(--theme-warning);
    color: #ffffff;
}

.patient-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* ===== 手术安排卡片样式 ===== */
.modern-surgery-schedule-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 1.5vh 1.5vw;
    transition: transform 0.3s ease;
    overflow: hidden;
}

.modern-surgery-schedule-card:hover {
    transform: translateY(-2px);
}

.modern-surgery-schedule-card[data-priority="emergency"] {
    border-left: 4px solid var(--theme-error);
    animation: pulse 2s infinite;
}

.modern-surgery-schedule-card[data-priority="urgent"] {
    border-left: 4px solid var(--theme-warning);
}

.surgery-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1vh;
}

.surgery-time-info .scheduled-time {
    font-size: 1.4vw;
    font-weight: 700;
    color: #ffffff;
}

.surgery-time-info .estimated-duration {
    font-size: 0.9vw;
    color: rgba(255, 255, 255, 0.7);
}

.surgery-badges {
    display: flex;
    gap: 0.5vw;
}

.priority-badge, .status-badge {
    padding: 0.3vh 0.8vw;
    border-radius: 0.4vw;
    font-size: 0.8vw;
    font-weight: 600;
    color: #ffffff;
}

.surgery-main-info .patient-name {
    font-size: 1.3vw;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 0.5vh 0;
}

.surgery-main-info .surgery-name {
    font-size: 1vw;
    color: rgba(255, 255, 255, 0.8);
}

.surgery-team-info {
    display: flex;
    flex-direction: column;
    gap: 0.3vh;
    margin-top: 1vh;
}

.team-member {
    display: flex;
    justify-content: space-between;
    font-size: 0.9vw;
}

.member-role {
    color: rgba(255, 255, 255, 0.7);
}

.member-name {
    color: #ffffff;
    font-weight: 600;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(245, 87, 108, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(245, 87, 108, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(245, 87, 108, 0);
    }
}

/* ===== 通知卡片样式 ===== */
.modern-notification-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    padding: 1.5vh 1.5vw;
    transition: all 0.3s ease;
    position: relative;
}

.modern-notification-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.modern-notification-card[data-read="false"] {
    border-left: 4px solid var(--theme-primary);
}

.modern-notification-card[data-important="true"] {
    border-top: 3px solid var(--theme-warning);
}

.notification-card-header {
    display: flex;
    align-items: flex-start;
    gap: 1vw;
    margin-bottom: 1vh;
}

.notification-icon {
    font-size: 1.5vw;
    flex-shrink: 0;
}

.notification-meta {
    flex: 1;
}

.notification-title {
    font-size: 1.2vw;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 0.3vh 0;
}

.notification-timestamp {
    font-size: 0.8vw;
    color: rgba(255, 255, 255, 0.6);
}

.important-indicator {
    background: var(--theme-warning);
    color: #ffffff;
    padding: 0.2vh 0.6vw;
    border-radius: 0.3vw;
    font-size: 0.7vw;
    font-weight: 600;
}

.unread-indicator {
    width: 0.6vw;
    height: 0.6vw;
    background: var(--theme-primary);
    border-radius: 50%;
    position: absolute;
    top: 1vh;
    right: 1vw;
}

.notification-message {
    font-size: 1vw;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
    margin-bottom: 0.5vh;
}

.notification-author {
    font-size: 0.8vw;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
}

.notification-card-actions {
    display: flex;
    gap: 0.8vw;
    margin-top: 1vh;
    padding-top: 1vh;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.notification-action-btn {
    padding: 0.5vh 1vw;
    border: none;
    border-radius: 0.4vw;
    font-size: 0.8vw;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-action-btn.default {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.notification-action-btn.primary {
    background: var(--theme-primary);
    color: #ffffff;
}

.notification-action-btn:hover {
    transform: translateY(-1px);
}

/* ===== 设备状态卡片样式 ===== */
.modern-device-status-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    padding: 1.5vh 1.5vw;
    transition: all 0.3s ease;
}

.modern-device-status-card:hover {
    transform: translateY(-2px);
}

.modern-device-status-card[data-status="error"] {
    border-left: 4px solid var(--theme-error);
}

.modern-device-status-card[data-status="warning"] {
    border-left: 4px solid var(--theme-warning);
}

.modern-device-status-card[data-status="normal"] {
    border-left: 4px solid var(--theme-success);
}

.device-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1vh;
}

.device-name {
    font-size: 1.3vw;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 0.3vh 0;
}

.device-type {
    font-size: 0.9vw;
    color: rgba(255, 255, 255, 0.7);
}

.device-status {
    display: flex;
    align-items: center;
    gap: 0.5vw;
    font-size: 1vw;
    font-weight: 600;
}

.device-details {
    display: flex;
    flex-direction: column;
    gap: 0.5vh;
}

.device-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3vh 0;
}

.detail-label {
    font-size: 0.9vw;
    color: rgba(255, 255, 255, 0.7);
}

.detail-value {
    font-size: 0.9vw;
    color: #ffffff;
    font-weight: 600;
}

.detail-value.connected {
    color: var(--theme-success);
}

.battery-indicator {
    display: flex;
    align-items: center;
    gap: 0.5vw;
}

.battery-level {
    height: 0.4vh;
    background: var(--theme-success);
    border-radius: 0.2vh;
    transition: width 0.3s ease;
}

.battery-percentage {
    font-size: 0.8vw;
    color: #ffffff;
}

.device-alerts {
    margin-top: 1vh;
    padding-top: 1vh;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.alerts-title {
    font-size: 1vw;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 0.5vh 0;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 0.3vh;
}

.alert-item {
    display: flex;
    justify-content: space-between;
    padding: 0.3vh 0.8vw;
    border-radius: 0.3vw;
    font-size: 0.8vw;
}

.alert-item.warning {
    background: rgba(255, 193, 7, 0.2);
    color: var(--theme-warning);
}

.alert-item.error {
    background: rgba(220, 53, 69, 0.2);
    color: var(--theme-error);
}

.alert-time {
    color: rgba(255, 255, 255, 0.6);
}

/* ===== 快速操作卡片样式 ===== */
.modern-quick-action-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    padding: 1.5vh 1.5vw;
    overflow: hidden;
}

.quick-action-title {
    font-size: 1.3vw;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 1vh 0;
}

.quick-actions-container.grid {
    display: grid;
    gap: 1vw;
}

.quick-actions-container.list {
    display: flex;
    flex-direction: column;
    gap: 0.8vh;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 1.5vh 1vw;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.8vw;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    min-height: 8vh;
}

.quick-action-btn:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.quick-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-icon {
    font-size: 2vw;
    margin-bottom: 0.5vh;
}

.action-label {
    font-size: 1vw;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.3vh;
}

.action-description {
    font-size: 0.8vw;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.2;
}

/* ===== 时间轴卡片样式 ===== */
.modern-timeline-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    padding: 1.5vh 1.5vw;
    overflow: hidden;
}

.timeline-title {
    font-size: 1.3vw;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 1vh 0;
}

.timeline-card-body {
    overflow-y: auto;
}

.timeline-container {
    position: relative;
}

.timeline-event {
    display: flex;
    gap: 1vw;
    margin-bottom: 1.5vh;
    position: relative;
}

.timeline-marker {
    position: relative;
    flex-shrink: 0;
}

.timeline-dot {
    width: 0.8vw;
    height: 0.8vw;
    background: var(--theme-primary);
    border-radius: 50%;
    border: 2px solid #ffffff;
    position: relative;
    z-index: 2;
}

.timeline-line {
    position: absolute;
    left: 50%;
    top: 0.8vw;
    width: 2px;
    height: 2vh;
    background: rgba(255, 255, 255, 0.3);
    transform: translateX(-50%);
}

.timeline-content {
    flex: 1;
    padding-bottom: 0.5vh;
}

.timeline-time {
    font-size: 0.8vw;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 0.3vh;
}

.timeline-event .timeline-title {
    font-size: 1vw;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 0.3vh 0;
}

.timeline-description {
    font-size: 0.9vw;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    margin-bottom: 0.3vh;
}

.timeline-author {
    font-size: 0.8vw;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
}

.timeline-event.warning .timeline-dot {
    background: var(--theme-warning);
}

.timeline-event.error .timeline-dot {
    background: var(--theme-error);
}

.timeline-event.success .timeline-dot {
    background: var(--theme-success);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
    .patient-card-body {
        grid-template-columns: 1fr;
        gap: 1.5vh;
    }

    .vitals-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions-container.grid {
        grid-template-columns: 1fr !important;
    }
}

@media (max-width: 768px) {
    .modern-patient-card,
    .modern-surgery-schedule-card,
    .modern-notification-card,
    .modern-device-status-card,
    .modern-quick-action-card,
    .modern-timeline-card {
        padding: 1vh 1.5vw;
    }

    .patient-basic-info .patient-name {
        font-size: 2.5vw;
    }

    .section-title {
        font-size: 2vw;
    }

    .action-icon {
        font-size: 3vw;
    }
}
