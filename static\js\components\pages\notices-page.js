/**
 * 通知页组件
 * 显示科室通知和公告
 */

class NoticesPage extends BasePage {
    constructor(options = {}) {
        super({
            autoRefresh: true,
            refreshInterval: 300000, // 5分钟刷新一次
            ...options
        });
    }
    
    /**
     * 加载页面数据
     */
    async loadData() {
        try {
            const notices = await this.getNotices();
            
            this.data = {
                title: '通知公告',
                subtitle: '最新消息',
                notices,
                totalCount: notices.length
            };
            
            console.log('通知页数据加载完成:', this.data);
        } catch (error) {
            console.error('通知页数据加载失败:', error);
            this.data = this.getDefaultData();
        }
    }
    
    /**
     * 获取通知数据
     */
    async getNotices() {
        try {
            // 这里应该调用实际的API
            // const response = await fetch('/api/notices');
            // return await response.json();
            
            // 模拟数据
            return this.getDefaultNotices();
        } catch (error) {
            console.error('获取通知数据失败:', error);
            return this.getDefaultNotices();
        }
    }
    
    /**
     * 获取默认通知数据
     */
    getDefaultNotices() {
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const dayBeforeYesterday = new Date(today);
        dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);
        
        return [
            {
                id: 1,
                type: '重要通知',
                title: '新型麻醉药物使用规范',
                content: '请各位医师注意新引进的麻醉药物使用规范，严格按照剂量标准执行，确保患者安全。详细使用指南请查看科室内网文档。',
                date: this.formatDate(today),
                priority: 'high',
                isImportant: true,
                author: '科室主任',
                status: 'active'
            },
            {
                id: 2,
                type: '科室通知',
                title: '本周科室会议安排',
                content: '本周四下午3点在会议室召开科室例会，请全体医护人员准时参加。会议内容包括：病例讨论、新技术培训、安全事故分析。',
                date: this.formatDate(yesterday),
                priority: 'normal',
                isImportant: false,
                author: '护士长',
                status: 'active'
            },
            {
                id: 3,
                type: '培训通知',
                title: '急救技能培训',
                content: '下周一开始为期三天的急救技能培训，请相关人员做好准备。培训内容包括心肺复苏、气道管理、药物过敏处理等。',
                date: this.formatDate(dayBeforeYesterday),
                priority: 'normal',
                isImportant: false,
                author: '培训科',
                status: 'active'
            },
            {
                id: 4,
                type: '设备通知',
                title: '麻醉机维护保养',
                content: '本周末将对3号手术室麻醉机进行例行维护保养，预计停机4小时。请提前调整手术安排，如有紧急情况请联系设备科。',
                date: this.formatDate(dayBeforeYesterday),
                priority: 'low',
                isImportant: false,
                author: '设备科',
                status: 'active'
            }
        ];
    }
    
    /**
     * 格式化日期
     */
    formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    /**
     * 获取默认数据
     */
    getDefaultData() {
        const notices = this.getDefaultNotices();
        return {
            title: '通知公告',
            subtitle: '最新消息',
            notices,
            totalCount: notices.length
        };
    }
    
    /**
     * 获取页面模板
     */
    getTemplate() {
        return `
            <div class="notices-page">
                <div class="page-header">
                    <h2>{{title}}</h2>
                    <div class="date-range">{{subtitle}} ({{totalCount}}条)</div>
                </div>
                
                <div class="notices-list">
                    {{#each notices}}
                    <div class="notice-item {{classIf this.isImportant 'important'}} {{this.priority}}">
                        <div class="notice-header">
                            <div class="notice-meta">
                                <span class="notice-type">{{this.type}}</span>
                                {{#if this.isImportant}}
                                <span class="important-badge">重要</span>
                                {{/if}}
                            </div>
                            <span class="notice-date">{{this.date}}</span>
                        </div>
                        
                        <div class="notice-title">{{this.title}}</div>
                        
                        <div class="notice-content">{{this.content}}</div>
                        
                        <div class="notice-footer">
                            <span class="notice-author">发布人: {{this.author}}</span>
                            <span class="notice-status {{this.status}}">
                                {{#if this.status === 'active'}}有效{{/if}}
                                {{#if this.status === 'expired'}}已过期{{/if}}
                                {{#if this.status === 'draft'}}草稿{{/if}}
                            </span>
                        </div>
                    </div>
                    {{/each}}
                </div>
                
                {{#if notices.length === 0}}
                <div class="empty-state">
                    <div class="empty-icon">📢</div>
                    <div class="empty-text">暂无通知公告</div>
                </div>
                {{/if}}
            </div>
        `;
    }
    
    /**
     * 渲染页面
     */
    async render() {
        if (!this.isInitialized) {
            await this.init();
        }
        
        const template = this.getTemplate();
        return window.templateEngine.render(template, this.data);
    }
    
    /**
     * 获取页面标题
     */
    getTitle() {
        return '通知公告';
    }
    
    /**
     * 获取页面类型
     */
    getType() {
        return 'notices';
    }
    
    /**
     * 页面激活时调用
     */
    onActivate() {
        super.onActivate();
        // 可以在这里添加特定的激活逻辑，比如标记通知为已读
    }
    
    /**
     * 处理键盘事件
     */
    onKeyDown(event) {
        switch (event.key) {
            case 'r':
            case 'R':
                this.refresh();
                break;
            default:
                super.onKeyDown(event);
        }
    }
    
    /**
     * 标记通知为已读
     */
    async markAsRead(noticeId) {
        try {
            // 这里应该调用API标记通知为已读
            // await fetch(`/api/notices/${noticeId}/read`, { method: 'POST' });
            console.log(`标记通知 ${noticeId} 为已读`);
        } catch (error) {
            console.error('标记通知已读失败:', error);
        }
    }
    
    /**
     * 获取未读通知数量
     */
    getUnreadCount() {
        return this.data.notices ? this.data.notices.filter(notice => !notice.isRead).length : 0;
    }
}

// 全局导出
window.NoticesPage = NoticesPage;
