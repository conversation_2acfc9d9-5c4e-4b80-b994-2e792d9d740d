/**
 * 简洁页面管理器
 */
class SimplePageManager {
    constructor() {
        this.currentPageIndex = 0;
        this.pages = [];
        this.autoPlayInterval = null;
        this.autoPlayDelay = 10000; // 10秒
        this.isAutoPlaying = true;
        
        // 基础数据
        this.hospitalName = '示例医院';
        this.departmentName = '麻醉科';
        this.dutyDoctor = '张医生';
        this.dutyNurse = '李护士';
        
        this.init();
    }
    
    async init() {
        console.log('初始化简洁页面管理器...');
        
        try {
            // 获取数据
            await this.loadData();
            
            // 生成页面
            this.generatePages();
            
            // 显示第一页
            this.showPage(0);
            
            // 开始自动播放
            this.startAutoPlay();
            
            // 绑定键盘事件
            this.bindKeyboardEvents();
            
            console.log('页面管理器初始化完成');
        } catch (error) {
            console.error('页面管理器初始化失败:', error);
            this.showErrorPage();
        }
    }
    
    async loadData() {
        try {
            // 获取今日统计
            const todayResponse = await fetch('/api/stats/today');
            this.todayStats = await todayResponse.json();
            
            // 获取昨日统计
            const yesterdayResponse = await fetch('/api/stats/yesterday');
            this.yesterdayStats = await yesterdayResponse.json();
            
            // 获取今日手术列表
            const surgeriesResponse = await fetch('/api/surgeries/today');
            this.todaySurgeries = await surgeriesResponse.json();
            
            // 获取特殊病例
            const specialResponse = await fetch('/api/surgeries/special');
            this.specialCases = await specialResponse.json();
            
            console.log('数据加载完成');
        } catch (error) {
            console.error('数据加载失败:', error);
            // 使用默认数据
            this.todayStats = { total_surgeries: 3 };
            this.yesterdayStats = { total_surgeries: 5 };
            this.todaySurgeries = [];
            this.specialCases = [];
        }
    }
    
    generatePages() {
        this.pages = [
            {
                id: 'cover',
                title: '封面',
                generator: () => this.generateCoverPage()
            },
            {
                id: 'yesterday-stats',
                title: '昨日统计',
                generator: () => this.generateYesterdayStatsPage()
            },
            {
                id: 'today-stats',
                title: '今日统计',
                generator: () => this.generateTodayStatsPage()
            }
        ];
        
        // 添加特殊病例页面
        if (this.specialCases && this.specialCases.length > 0) {
            this.specialCases.forEach((caseData, index) => {
                this.pages.push({
                    id: `special-case-${index}`,
                    title: `特殊病例 ${index + 1}`,
                    generator: () => this.generatePatientPage(caseData)
                });
            });
        }
        
        // 添加通知页面
        this.pages.push({
            id: 'notices',
            title: '通知事项',
            generator: () => this.generateNoticesPage()
        });
        
        console.log(`生成了 ${this.pages.length} 个页面`);
    }
    
    generateCoverPage() {
        const currentDate = new Date();
        const dateStr = currentDate.toLocaleDateString('zh-CN');
        const emergencyCount = this.getEmergencyCount();
        
        return `
            <div class="page-container cover-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - ${dateStr}</div>
                </div>
                <div class="page-content">
                    <div class="cover-title">
                        <h2>交班大屏</h2>
                    </div>
                    
                    <div class="duty-info">
                        <div class="duty-item">
                            <div class="duty-label">值班医生</div>
                            <div class="duty-name">${this.dutyDoctor}</div>
                        </div>
                        <div class="duty-item">
                            <div class="duty-label">值班护士</div>
                            <div class="duty-name">${this.dutyNurse}</div>
                        </div>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-number">${this.todayStats?.total_surgeries || 0}</div>
                            <div class="stat-label">总手术量</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-number">${emergencyCount}</div>
                            <div class="stat-label">急诊手术</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-number">${(this.todayStats?.total_surgeries || 0) - emergencyCount}</div>
                            <div class="stat-label">择期手术</div>
                        </div>
                    </div>
                </div>
                <div class="page-footer">
                    <div class="current-time">${currentDate.toLocaleString('zh-CN')}</div>
                    <div class="page-number">1 / ${this.pages.length}</div>
                </div>
            </div>
        `;
    }
    
    generateYesterdayStatsPage() {
        const anesthesiaStats = this.getYesterdayAnesthesiaStats();
        const emergencyCount = this.getYesterdayEmergencyCount();
        
        return `
            <div class="page-container stats-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - 昨日手术统计</div>
                </div>
                <div class="page-content">
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-number">${this.yesterdayStats?.total_surgeries || 0}</div>
                            <div class="stat-label">总手术量</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-number">${emergencyCount}</div>
                            <div class="stat-label">急诊手术</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-number">${(this.yesterdayStats?.total_surgeries || 0) - emergencyCount}</div>
                            <div class="stat-label">择期手术</div>
                        </div>
                    </div>
                    
                    <div class="stats-detail">
                        <div class="stats-left">
                            <div class="stats-title">麻醉方式统计</div>
                            <div class="stats-items">
                                ${Object.entries(anesthesiaStats).map(([type, count]) => `
                                    <div class="stats-item">
                                        <span class="stats-item-name">${type}</span>
                                        <span class="stats-item-count">${count}例</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="stats-right">
                            <canvas id="yesterdayChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="page-footer">
                    <div class="current-time">${new Date().toLocaleString('zh-CN')}</div>
                    <div class="page-number">2 / ${this.pages.length}</div>
                </div>
            </div>
        `;
    }
    
    generateTodayStatsPage() {
        const departmentStats = this.getTodayDepartmentStats();
        const anesthesiaStats = this.getTodayAnesthesiaStats();
        const emergencyCount = this.getEmergencyCount();
        
        return `
            <div class="page-container stats-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - 今日手术统计</div>
                </div>
                <div class="page-content">
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-number">${this.todayStats?.total_surgeries || 0}</div>
                            <div class="stat-label">总手术量</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-number">${emergencyCount}</div>
                            <div class="stat-label">急诊手术</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-number">${(this.todayStats?.total_surgeries || 0) - emergencyCount}</div>
                            <div class="stat-label">择期手术</div>
                        </div>
                    </div>
                    
                    <div class="stats-detail">
                        <div class="stats-left">
                            <div class="stats-title">科室分布</div>
                            <div class="stats-items">
                                ${Object.entries(departmentStats).map(([dept, count]) => `
                                    <div class="stats-item">
                                        <span class="stats-item-name">${dept}</span>
                                        <span class="stats-item-count">${count}例</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="stats-right">
                            <canvas id="todayChart"></canvas>
                        </div>
                    </div>
                </div>
                <div class="page-footer">
                    <div class="current-time">${new Date().toLocaleString('zh-CN')}</div>
                    <div class="page-number">3 / ${this.pages.length}</div>
                </div>
            </div>
        `;
    }
    
    generatePatientPage(patient) {
        const pageIndex = this.currentPageIndex + 1;
        
        return `
            <div class="page-container patient-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - 特殊病例</div>
                </div>
                <div class="page-content">
                    <div class="patient-info">
                        <div class="patient-header">
                            <h2>${patient.patient_name || '患者姓名'}</h2>
                            ${patient.is_emergency ? '<div class="emergency-badge">急诊</div>' : ''}
                        </div>
                        <div class="patient-details">
                            <div class="detail-row">
                                <span class="detail-label">手术名称：</span>
                                <span class="detail-value">${patient.surgery_name || '未知'}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">科室：</span>
                                <span class="detail-value">${patient.department || '未知'}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">麻醉方式：</span>
                                <span class="detail-value">${patient.anesthesia_type || '未知'}</span>
                            </div>
                            ${patient.allergies ? `
                                <div class="detail-row highlight">
                                    <span class="detail-label">过敏史：</span>
                                    <span class="detail-value allergy">${patient.allergies}</span>
                                </div>
                            ` : ''}
                            ${patient.special_notes ? `
                                <div class="detail-row highlight">
                                    <span class="detail-label">特殊注意：</span>
                                    <span class="detail-value special">${patient.special_notes}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
                <div class="page-footer">
                    <div class="current-time">${new Date().toLocaleString('zh-CN')}</div>
                    <div class="page-number">${pageIndex} / ${this.pages.length}</div>
                </div>
            </div>
        `;
    }
    
    generateNoticesPage() {
        const notices = [
            {
                title: '重要提醒',
                content: '请各位医护人员注意手术安全，严格执行三查七对制度。'
            },
            {
                title: '设备维护',
                content: '麻醉机定期维护已完成，设备运行正常。'
            },
            {
                title: '培训通知',
                content: '下周三下午2点进行急救技能培训，请准时参加。'
            }
        ];
        
        return `
            <div class="page-container notices-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - 通知事项</div>
                </div>
                <div class="page-content">
                    <div class="notices-list">
                        ${notices.map(notice => `
                            <div class="notice-item">
                                <div class="notice-title">${notice.title}</div>
                                <div class="notice-content">${notice.content}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="page-footer">
                    <div class="current-time">${new Date().toLocaleString('zh-CN')}</div>
                    <div class="page-number">${this.pages.length} / ${this.pages.length}</div>
                </div>
            </div>
        `;
    }
    
    // 辅助方法
    getEmergencyCount() {
        if (!this.todaySurgeries) return 0;
        return this.todaySurgeries.filter(s => s.is_emergency).length;
    }
    
    getYesterdayEmergencyCount() {
        return 1; // 模拟数据
    }
    
    getYesterdayAnesthesiaStats() {
        return {
            '全身麻醉': 3,
            '局部麻醉': 1,
            '椎管内麻醉': 1
        };
    }
    
    getTodayDepartmentStats() {
        return {
            '普外科': 1,
            '骨科': 1,
            '心外科': 1
        };
    }
    
    getTodayAnesthesiaStats() {
        return {
            '全身麻醉': 2,
            '局部麻醉': 1
        };
    }
    
    // 页面控制方法
    showPage(index) {
        if (index < 0 || index >= this.pages.length) return;
        
        this.currentPageIndex = index;
        const page = this.pages[index];
        const container = document.getElementById('pageContainer');
        
        if (container && page) {
            container.innerHTML = page.generator();
            
            // 更新页面指示器
            this.updatePageIndicator();
            
            // 初始化图表
            setTimeout(() => {
                this.initCharts();
            }, 100);
        }
    }
    
    nextPage() {
        const nextIndex = (this.currentPageIndex + 1) % this.pages.length;
        this.showPage(nextIndex);
    }
    
    prevPage() {
        const prevIndex = (this.currentPageIndex - 1 + this.pages.length) % this.pages.length;
        this.showPage(prevIndex);
    }
    
    startAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
        }
        
        this.autoPlayInterval = setInterval(() => {
            if (this.isAutoPlaying) {
                this.nextPage();
            }
        }, this.autoPlayDelay);
    }
    
    toggleAutoPlay() {
        this.isAutoPlaying = !this.isAutoPlaying;
        console.log('自动播放:', this.isAutoPlaying ? '开启' : '暂停');
    }
    
    updatePageIndicator() {
        const indicator = document.getElementById('pageIndicator');
        if (indicator) {
            indicator.textContent = `${this.currentPageIndex + 1} / ${this.pages.length}`;
        }
    }
    
    initCharts() {
        // 简单的图表初始化
        const canvas = document.querySelector('canvas');
        if (canvas && window.Chart) {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['全身麻醉', '局部麻醉', '椎管内麻醉'],
                    datasets: [{
                        data: [3, 1, 1],
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: 'white',
                                font: { size: 16 }
                            }
                        }
                    }
                }
            });
        }
    }
    
    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                    this.prevPage();
                    break;
                case 'ArrowRight':
                    this.nextPage();
                    break;
                case 'p':
                case 'P':
                    this.toggleAutoPlay();
                    break;
                case 'r':
                case 'R':
                    this.refresh();
                    break;
            }
        });
    }
    
    async refresh() {
        console.log('刷新数据...');
        await this.loadData();
        this.generatePages();
        this.showPage(this.currentPageIndex);
    }
    
    showErrorPage() {
        const container = document.getElementById('pageContainer');
        if (container) {
            container.innerHTML = `
                <div class="page-container">
                    <div class="page-content" style="justify-content: center; align-items: center;">
                        <div style="text-align: center;">
                            <h1>加载失败</h1>
                            <p>请检查网络连接或刷新页面重试</p>
                            <button onclick="location.reload()" style="background: #007bff; color: white; border: none; padding: 2vh 4vw; border-radius: 10px; cursor: pointer; font-size: 2vw;">刷新页面</button>
                        </div>
                    </div>
                </div>
            `;
        }
    }
}

// 全局实例
window.pageManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.pageManager = new SimplePageManager();
});
