"""
数据库初始化脚本
"""
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.database.connection import db_connection
from config.settings import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库"""
    try:
        logger.info("开始初始化数据库...")
        
        # 测试数据库连接
        if not db_connection.test_connection():
            logger.error("数据库连接测试失败")
            return False
        
        # 执行数据库结构脚本
        schema_path = Path(__file__).parent / "schema.sql"
        if not schema_path.exists():
            logger.error(f"数据库结构文件不存在: {schema_path}")
            return False
        
        db_connection.execute_script(str(schema_path))
        logger.info("数据库结构创建成功")
        
        # 验证表是否创建成功
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = ['surgeries', 'statistics', 'configurations', 'upload_records']
            missing_tables = set(expected_tables) - set(tables)
            
            if missing_tables:
                logger.error(f"以下表创建失败: {missing_tables}")
                return False
            
            logger.info(f"数据库表创建成功: {tables}")
        
        logger.info("数据库初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False

def reset_database():
    """重置数据库（删除所有表并重新创建）"""
    try:
        logger.warning("开始重置数据库...")
        
        with db_connection.get_db_context() as conn:
            # 获取所有表名
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            # 删除所有表
            for table in tables:
                conn.execute(f"DROP TABLE IF EXISTS {table}")
                logger.info(f"删除表: {table}")
            
            conn.commit()
        
        # 重新初始化
        return init_database()
        
    except Exception as e:
        logger.error(f"数据库重置失败: {e}")
        return False

def check_database_status():
    """检查数据库状态"""
    try:
        logger.info("检查数据库状态...")
        
        with db_connection.get_db_context() as conn:
            # 检查表是否存在
            cursor = conn.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            logger.info(f"现有表: {tables}")
            
            # 检查每个表的记录数
            for table in tables:
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                logger.info(f"表 {table} 记录数: {count}")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库状态检查失败: {e}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库管理工具")
    parser.add_argument("--reset", action="store_true", help="重置数据库")
    parser.add_argument("--check", action="store_true", help="检查数据库状态")
    
    args = parser.parse_args()
    
    if args.reset:
        success = reset_database()
    elif args.check:
        success = check_database_status()
    else:
        success = init_database()
    
    if success:
        logger.info("操作完成")
        sys.exit(0)
    else:
        logger.error("操作失败")
        sys.exit(1)
