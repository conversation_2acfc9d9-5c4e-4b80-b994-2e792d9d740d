/**
 * 现代化卡片组件库 - 重构版本 v2.0
 * 提供统一的卡片组件接口和样式
 * 更新时间: 2024-08-04
 */

class CardComponents {
    constructor() {
        this.defaultConfig = {
            borderRadius: '24px',
            padding: '32px',
            shadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
        };
    }

    /**
     * 创建统计卡片
     * @param {Object} config - 卡片配置
     * @returns {string} HTML字符串
     */
    createStatCard(config) {
        const {
            title,
            value,
            icon,
            theme = 'primary',
            gradient = null,
            className = ''
        } = config;

        const themeGradient = gradient || 'linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-secondary) 100%)';

        return `
            <div class="modern-stat-card ${className}" style="background: ${themeGradient};">
                <div class="stat-card-content">
                    <div class="stat-icon">${icon}</div>
                    <div class="stat-info">
                        <div class="stat-value">${value}</div>
                        <div class="stat-title">${title}</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建人员卡片
     * @param {Object} config - 卡片配置
     * @returns {string} HTML字符串
     */
    createPersonCard(config) {
        const {
            name,
            role,
            avatar = '👤',
            theme = 'primary',
            gradient = null,
            className = ''
        } = config;

        const themeGradient = gradient || 'linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-secondary) 100%)';

        return `
            <div class="modern-person-card ${className}" style="background: ${themeGradient};">
                <div class="person-card-content">
                    <div class="person-avatar">${avatar}</div>
                    <div class="person-info">
                        <div class="person-name">${name}</div>
                        <div class="person-role">${role}</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建病人信息卡片 - 左侧基础信息，右侧特殊情况交班
     * @param {Object} config - 卡片配置
     * @returns {string} HTML字符串
     */
    createPatientCard(config) {
        const {
            // 基础信息
            name = '患者姓名',
            age = '',
            gender = '',
            bedNumber = '',
            hospitalNumber = '',
            diagnosis = '',
            surgeryName = '',
            surgeonName = '',
            anesthesiaMethod = '',
            
            // 特殊情况交班
            allergies = [],
            specialNotes = '',
            riskLevel = 'low', // low, medium, high
            vitals = {},
            medications = [],
            
            // 样式配置
            className = '',
            showActions = false
        } = config;

        const riskColors = {
            low: 'var(--theme-success)',
            medium: 'var(--theme-warning)', 
            high: 'var(--theme-error)'
        };

        const riskLabels = {
            low: '低风险',
            medium: '中风险',
            high: '高风险'
        };

        return `
            <div class="modern-patient-card ${className}" data-risk="${riskLevel}">
                <div class="patient-card-header">
                    <div class="patient-basic-info">
                        <h3 class="patient-name">${name}</h3>
                        <div class="patient-meta">
                            ${age ? `<span class="patient-age">${age}岁</span>` : ''}
                            ${gender ? `<span class="patient-gender">${gender}</span>` : ''}
                            ${bedNumber ? `<span class="patient-bed">床号: ${bedNumber}</span>` : ''}
                        </div>
                    </div>
                    <div class="patient-risk-badge" style="background: ${riskColors[riskLevel]};">
                        ${riskLabels[riskLevel]}
                    </div>
                </div>
                
                <div class="patient-card-body">
                    <!-- 左侧：基础信息 -->
                    <div class="patient-basic-section">
                        <h4 class="section-title">基础信息</h4>
                        <div class="patient-info-grid">
                            ${hospitalNumber ? `
                                <div class="info-item">
                                    <span class="info-label">住院号</span>
                                    <span class="info-value">${hospitalNumber}</span>
                                </div>
                            ` : ''}
                            ${diagnosis ? `
                                <div class="info-item">
                                    <span class="info-label">诊断</span>
                                    <span class="info-value">${diagnosis}</span>
                                </div>
                            ` : ''}
                            ${surgeryName ? `
                                <div class="info-item">
                                    <span class="info-label">手术名称</span>
                                    <span class="info-value">${surgeryName}</span>
                                </div>
                            ` : ''}
                            ${surgeonName ? `
                                <div class="info-item">
                                    <span class="info-label">主刀医生</span>
                                    <span class="info-value">${surgeonName}</span>
                                </div>
                            ` : ''}
                            ${anesthesiaMethod ? `
                                <div class="info-item">
                                    <span class="info-label">麻醉方式</span>
                                    <span class="info-value">${anesthesiaMethod}</span>
                                </div>
                            ` : ''}
                        </div>
                        
                        ${Object.keys(vitals).length > 0 ? `
                            <div class="vitals-section">
                                <h5 class="subsection-title">生命体征</h5>
                                <div class="vitals-grid">
                                    ${vitals.bloodPressure ? `
                                        <div class="vital-item">
                                            <span class="vital-label">血压</span>
                                            <span class="vital-value">${vitals.bloodPressure}</span>
                                        </div>
                                    ` : ''}
                                    ${vitals.heartRate ? `
                                        <div class="vital-item">
                                            <span class="vital-label">心率</span>
                                            <span class="vital-value">${vitals.heartRate}</span>
                                        </div>
                                    ` : ''}
                                    ${vitals.temperature ? `
                                        <div class="vital-item">
                                            <span class="vital-label">体温</span>
                                            <span class="vital-value">${vitals.temperature}</span>
                                        </div>
                                    ` : ''}
                                    ${vitals.oxygenSaturation ? `
                                        <div class="vital-item">
                                            <span class="vital-label">血氧</span>
                                            <span class="vital-value">${vitals.oxygenSaturation}</span>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    
                    <!-- 右侧：特殊情况交班 -->
                    <div class="patient-special-section">
                        <h4 class="section-title">特殊情况交班</h4>
                        
                        ${allergies.length > 0 ? `
                            <div class="allergies-section">
                                <h5 class="subsection-title">
                                    <span class="warning-icon">⚠️</span>
                                    过敏史
                                </h5>
                                <div class="allergies-list">
                                    ${allergies.map(allergy => `
                                        <span class="allergy-tag">${allergy}</span>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                        
                        ${medications.length > 0 ? `
                            <div class="medications-section">
                                <h5 class="subsection-title">
                                    <span class="med-icon">💊</span>
                                    用药情况
                                </h5>
                                <div class="medications-list">
                                    ${medications.map(med => `
                                        <div class="medication-item">
                                            <span class="med-name">${med.name}</span>
                                            <span class="med-dosage">${med.dosage || ''}</span>
                                            ${med.time ? `<span class="med-time">${med.time}</span>` : ''}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                        
                        ${specialNotes ? `
                            <div class="special-notes-section">
                                <h5 class="subsection-title">
                                    <span class="notes-icon">📝</span>
                                    特殊注意事项
                                </h5>
                                <div class="special-notes-content">
                                    ${specialNotes}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                ${showActions ? `
                    <div class="patient-card-actions">
                        <button class="patient-action-btn primary">查看详情</button>
                        <button class="patient-action-btn secondary">编辑信息</button>
                        <button class="patient-action-btn warning">标记风险</button>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 创建手术安排卡片
     * @param {Object} config - 卡片配置
     * @returns {string} HTML字符串
     */
    createSurgeryScheduleCard(config) {
        const {
            patientName = '',
            surgeryName = '',
            surgeonName = '',
            anesthesiologist = '',
            operatingRoom = '',
            scheduledTime = '',
            estimatedDuration = '',
            priority = 'routine',
            status = 'scheduled',
            className = ''
        } = config;

        const priorityColors = {
            emergency: 'var(--theme-error)',
            urgent: 'var(--theme-warning)',
            routine: 'var(--theme-info)'
        };

        const priorityLabels = {
            emergency: '急诊',
            urgent: '紧急',
            routine: '常规'
        };

        const statusColors = {
            scheduled: 'var(--theme-info)',
            'in-progress': 'var(--theme-warning)',
            completed: 'var(--theme-success)',
            cancelled: 'var(--theme-error)'
        };

        const statusLabels = {
            scheduled: '已安排',
            'in-progress': '进行中',
            completed: '已完成',
            cancelled: '已取消'
        };

        return `
            <div class="modern-surgery-schedule-card ${className}" data-priority="${priority}" data-status="${status}">
                <div class="surgery-card-header">
                    <div class="surgery-time-info">
                        <div class="scheduled-time">${scheduledTime}</div>
                        ${estimatedDuration ? `<div class="estimated-duration">预计 ${estimatedDuration}</div>` : ''}
                    </div>
                    <div class="surgery-badges">
                        <span class="priority-badge" style="background: ${priorityColors[priority]};">
                            ${priorityLabels[priority]}
                        </span>
                        <span class="status-badge" style="background: ${statusColors[status]};">
                            ${statusLabels[status]}
                        </span>
                    </div>
                </div>
                
                <div class="surgery-card-body">
                    <div class="surgery-main-info">
                        <h4 class="patient-name">${patientName}</h4>
                        <div class="surgery-name">${surgeryName}</div>
                    </div>
                    
                    <div class="surgery-team-info">
                        <div class="team-member">
                            <span class="member-role">主刀医生</span>
                            <span class="member-name">${surgeonName}</span>
                        </div>
                        ${anesthesiologist ? `
                            <div class="team-member">
                                <span class="member-role">麻醉医生</span>
                                <span class="member-name">${anesthesiologist}</span>
                            </div>
                        ` : ''}
                        ${operatingRoom ? `
                            <div class="team-member">
                                <span class="member-role">手术室</span>
                                <span class="member-name">${operatingRoom}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建通知卡片
     * @param {Object} config - 卡片配置
     * @returns {string} HTML字符串
     */
    createNotificationCard(config) {
        const {
            title = '',
            message = '',
            type = 'info', // info, warning, error, success
            timestamp = '',
            author = '',
            isRead = false,
            isImportant = false,
            actions = [],
            className = ''
        } = config;

        const typeIcons = {
            info: '📢',
            warning: '⚠️',
            error: '❌',
            success: '✅'
        };

        const typeColors = {
            info: 'var(--theme-info)',
            warning: 'var(--theme-warning)',
            error: 'var(--theme-error)',
            success: 'var(--theme-success)'
        };

        return `
            <div class="modern-notification-card ${className}"
                 data-type="${type}"
                 data-read="${isRead}"
                 data-important="${isImportant}">
                <div class="notification-card-header">
                    <div class="notification-icon" style="color: ${typeColors[type]};">
                        ${typeIcons[type]}
                    </div>
                    <div class="notification-meta">
                        <div class="notification-title">${title}</div>
                        <div class="notification-timestamp">${timestamp}</div>
                    </div>
                    ${isImportant ? `<div class="important-indicator">重要</div>` : ''}
                    ${!isRead ? `<div class="unread-indicator"></div>` : ''}
                </div>

                <div class="notification-card-body">
                    <div class="notification-message">${message}</div>
                    ${author ? `<div class="notification-author">发布者: ${author}</div>` : ''}
                </div>

                ${actions.length > 0 ? `
                    <div class="notification-card-actions">
                        ${actions.map(action => `
                            <button class="notification-action-btn ${action.type || 'default'}"
                                    onclick="${action.onClick || ''}">
                                ${action.label}
                            </button>
                        `).join('')}
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 创建设备状态卡片
     * @param {Object} config - 卡片配置
     * @returns {string} HTML字符串
     */
    createDeviceStatusCard(config) {
        const {
            deviceName = '',
            deviceType = '',
            status = 'normal', // normal, warning, error, maintenance
            location = '',
            lastCheck = '',
            batteryLevel = null,
            connectionStatus = 'connected',
            alerts = [],
            className = ''
        } = config;

        const statusColors = {
            normal: 'var(--theme-success)',
            warning: 'var(--theme-warning)',
            error: 'var(--theme-error)',
            maintenance: 'var(--theme-info)'
        };

        const statusLabels = {
            normal: '正常',
            warning: '警告',
            error: '故障',
            maintenance: '维护中'
        };

        const statusIcons = {
            normal: '✅',
            warning: '⚠️',
            error: '❌',
            maintenance: '🔧'
        };

        return `
            <div class="modern-device-status-card ${className}" data-status="${status}">
                <div class="device-card-header">
                    <div class="device-info">
                        <h4 class="device-name">${deviceName}</h4>
                        <div class="device-type">${deviceType}</div>
                    </div>
                    <div class="device-status" style="color: ${statusColors[status]};">
                        <span class="status-icon">${statusIcons[status]}</span>
                        <span class="status-label">${statusLabels[status]}</span>
                    </div>
                </div>

                <div class="device-card-body">
                    <div class="device-details">
                        ${location ? `
                            <div class="device-detail-item">
                                <span class="detail-label">位置</span>
                                <span class="detail-value">${location}</span>
                            </div>
                        ` : ''}
                        ${lastCheck ? `
                            <div class="device-detail-item">
                                <span class="detail-label">最后检查</span>
                                <span class="detail-value">${lastCheck}</span>
                            </div>
                        ` : ''}
                        <div class="device-detail-item">
                            <span class="detail-label">连接状态</span>
                            <span class="detail-value ${connectionStatus}">${connectionStatus === 'connected' ? '已连接' : '未连接'}</span>
                        </div>
                        ${batteryLevel !== null ? `
                            <div class="device-detail-item">
                                <span class="detail-label">电池电量</span>
                                <div class="battery-indicator">
                                    <div class="battery-level" style="width: ${batteryLevel}%"></div>
                                    <span class="battery-percentage">${batteryLevel}%</span>
                                </div>
                            </div>
                        ` : ''}
                    </div>

                    ${alerts.length > 0 ? `
                        <div class="device-alerts">
                            <h5 class="alerts-title">警报信息</h5>
                            <div class="alerts-list">
                                ${alerts.map(alert => `
                                    <div class="alert-item ${alert.level}">
                                        <span class="alert-time">${alert.time}</span>
                                        <span class="alert-message">${alert.message}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * 创建快速操作卡片
     * @param {Object} config - 卡片配置
     * @returns {string} HTML字符串
     */
    createQuickActionCard(config) {
        const {
            title = '快速操作',
            actions = [],
            layout = 'grid', // grid, list
            columns = 2,
            className = ''
        } = config;

        return `
            <div class="modern-quick-action-card ${className}">
                <div class="quick-action-header">
                    <h4 class="quick-action-title">${title}</h4>
                </div>

                <div class="quick-action-body">
                    <div class="quick-actions-container ${layout}"
                         ${layout === 'grid' ? `style="grid-template-columns: repeat(${columns}, 1fr);"` : ''}>
                        ${actions.map(action => `
                            <button class="quick-action-btn ${action.type || 'default'}"
                                    onclick="${action.onClick || ''}"
                                    ${action.disabled ? 'disabled' : ''}>
                                <div class="action-icon">${action.icon || '⚡'}</div>
                                <div class="action-label">${action.label}</div>
                                ${action.description ? `<div class="action-description">${action.description}</div>` : ''}
                            </button>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建时间轴卡片
     * @param {Object} config - 卡片配置
     * @returns {string} HTML字符串
     */
    createTimelineCard(config) {
        const {
            title = '时间轴',
            events = [],
            showTime = true,
            maxHeight = '400px',
            className = ''
        } = config;

        return `
            <div class="modern-timeline-card ${className}">
                <div class="timeline-card-header">
                    <h4 class="timeline-title">${title}</h4>
                </div>

                <div class="timeline-card-body" style="max-height: ${maxHeight};">
                    <div class="timeline-container">
                        ${events.map((event, index) => `
                            <div class="timeline-event ${event.type || 'default'}" data-index="${index}">
                                <div class="timeline-marker">
                                    <div class="timeline-dot"></div>
                                    ${index < events.length - 1 ? '<div class="timeline-line"></div>' : ''}
                                </div>
                                <div class="timeline-content">
                                    ${showTime && event.time ? `<div class="timeline-time">${event.time}</div>` : ''}
                                    <div class="timeline-title">${event.title}</div>
                                    ${event.description ? `<div class="timeline-description">${event.description}</div>` : ''}
                                    ${event.author ? `<div class="timeline-author">by ${event.author}</div>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
}

// 全局实例
window.cardComponents = new CardComponents();

// 调试信息
console.log('CardComponents v2.0 已加载');
console.log('可用方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(window.cardComponents))
    .filter(name => typeof window.cardComponents[name] === 'function' && name !== 'constructor'));
