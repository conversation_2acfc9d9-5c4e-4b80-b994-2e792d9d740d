/**
 * 早交班页面样式 - PPT全屏模式
 * 固定比例布局，适配所有屏幕尺寸
 */

/* 早交班页面主容器 - 固定内容，禁止滚动 */
.title-page-simple {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    left: 0;
}

/* 人员卡片容器 - 固定16:9比例 */
.duty-cards-full {
    /* 固定16:9比例的容器 */
    width: 80vw;
    height: 60vh;
    max-width: 1600px;
    max-height: 900px;
    aspect-ratio: 16/9;
    
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 3vw;
    align-items: stretch;
    padding: 4vh 2vw;
    box-sizing: border-box;
}

/* 人员卡片样式 - PPT全屏模式 */
.duty-cards-full .person-card {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 3vh 2vw;
    border-radius: 1.5vw;
    box-shadow: 0 1vw 3vw rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    min-height: 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.duty-cards-full .person-card:hover {
    transform: translateY(-0.5vh);
    box-shadow: 0 1.5vw 4vw rgba(0, 0, 0, 0.15);
}

/* 人员卡片内容样式 - 使用视口单位确保固定比例 */
.duty-cards-full .person-card .person-icon {
    font-size: 4vw;
    margin-bottom: 2vh;
    opacity: 0.9;
}

.duty-cards-full .person-card .person-name {
    font-size: 3vw;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1vh;
    text-shadow: 0 0.3vw 0.6vw rgba(0, 0, 0, 0.3);
    line-height: 1.1;
}

.duty-cards-full .person-card .person-role {
    font-size: 1.8vw;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 0.2vw 0.4vw rgba(0, 0, 0, 0.3);
}

/* 确保在所有屏幕尺寸下保持相同的视觉效果 */
@media (max-aspect-ratio: 16/9) {
    .duty-cards-full {
        width: 90vw;
        height: calc(90vw * 9 / 16);
    }
}

@media (min-aspect-ratio: 16/9) {
    .duty-cards-full {
        width: calc(90vh * 16 / 9);
        height: 90vh;
    }
}
