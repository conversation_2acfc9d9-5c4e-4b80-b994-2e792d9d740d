"""
Excel文件解析服务
"""
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging

from src.models.surgery import Surgery, SurgeryStatus
from src.utils.validators import validate_surgery_data
from src.utils.formatters import format_datetime, clean_string

logger = logging.getLogger(__name__)

class ExcelParser:
    """Excel文件解析器"""
    
    # 支持的列名映射（中文 -> 英文字段名）
    COLUMN_MAPPING = {
        '科室': 'department',
        '患者姓名': 'patient_name',
        '病人姓名': 'patient_name',
        '姓名': 'patient_name',
        '年龄': 'age',
        '床号': 'bed_number',
        '床位': 'bed_number',
        '床位号': 'bed_number',
        '手术方式': 'surgery_type',
        '手术类型': 'surgery_type',
        '手术名称': 'surgery_type',
        '麻醉方式': 'anesthesia_type',
        '麻醉类型': 'anesthesia_type',
        '过敏史': 'allergy_history',
        '过敏情况': 'allergy_history',
        '特殊情况': 'special_situation',
        '特殊病情': 'special_situation',
        '备注': 'special_situation',
        '手术日期': 'surgery_date',
        '日期': 'surgery_date',
        '是否急诊': 'is_emergency',
        '急诊': 'is_emergency',
        '状态': 'status',
        '手术状态': 'status'
    }
    
    # 状态映射
    STATUS_MAPPING = {
        '已安排': SurgeryStatus.SCHEDULED,
        '安排': SurgeryStatus.SCHEDULED,
        '计划': SurgeryStatus.SCHEDULED,
        '进行中': SurgeryStatus.IN_PROGRESS,
        '正在进行': SurgeryStatus.IN_PROGRESS,
        '手术中': SurgeryStatus.IN_PROGRESS,
        '已完成': SurgeryStatus.COMPLETED,
        '完成': SurgeryStatus.COMPLETED,
        '结束': SurgeryStatus.COMPLETED,
        '已取消': SurgeryStatus.CANCELLED,
        '取消': SurgeryStatus.CANCELLED,
        'cancelled': SurgeryStatus.CANCELLED,
        'completed': SurgeryStatus.COMPLETED,
        'in_progress': SurgeryStatus.IN_PROGRESS,
        'scheduled': SurgeryStatus.SCHEDULED
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_excel_file(self, file_path: str) -> Tuple[List[Surgery], List[str]]:
        """
        解析Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            Tuple[List[Surgery], List[str]]: (解析成功的手术数据列表, 错误信息列表)
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            self.logger.info(f"开始解析Excel文件: {file_path}")
            
            # 读取Excel文件
            df = self._read_excel_file(file_path)
            
            # 数据清洗
            df = self._clean_dataframe(df)
            
            # 列名映射
            df = self._map_columns(df)
            
            # 验证必需列
            missing_columns = self._validate_required_columns(df)
            if missing_columns:
                raise ValueError(f"缺少必需的列: {', '.join(missing_columns)}")
            
            # 解析数据
            surgeries, errors = self._parse_surgery_data(df)
            
            self.logger.info(f"Excel解析完成，成功: {len(surgeries)}条，错误: {len(errors)}条")
            return surgeries, errors
            
        except Exception as e:
            self.logger.error(f"Excel解析失败: {e}")
            return [], [f"文件解析失败: {str(e)}"]
    
    def _read_excel_file(self, file_path: Path) -> pd.DataFrame:
        """读取Excel文件"""
        try:
            # 尝试不同的引擎读取
            if file_path.suffix.lower() == '.xlsx':
                df = pd.read_excel(file_path, engine='openpyxl')
            else:
                df = pd.read_excel(file_path, engine='xlrd')
            
            self.logger.info(f"成功读取Excel文件，共{len(df)}行数据")
            return df
            
        except Exception as e:
            self.logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗DataFrame数据"""
        # 删除完全空白的行
        df = df.dropna(how='all')
        
        # 删除完全空白的列
        df = df.dropna(axis=1, how='all')
        
        # 清理列名（去除空格、换行符等）
        df.columns = [clean_string(str(col)) for col in df.columns]
        
        # 填充空值
        df = df.fillna('')
        
        self.logger.info(f"数据清洗完成，剩余{len(df)}行数据")
        return df
    
    def _map_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """映射列名"""
        # 创建新的列名映射
        column_mapping = {}
        for col in df.columns:
            clean_col = clean_string(str(col))
            if clean_col in self.COLUMN_MAPPING:
                column_mapping[col] = self.COLUMN_MAPPING[clean_col]
        
        # 重命名列
        df = df.rename(columns=column_mapping)
        
        self.logger.info(f"列名映射完成: {column_mapping}")
        return df
    
    def _validate_required_columns(self, df: pd.DataFrame) -> List[str]:
        """验证必需的列是否存在"""
        required_columns = [
            'department', 'patient_name', 'age', 'bed_number',
            'surgery_type', 'anesthesia_type', 'surgery_date'
        ]

        missing_columns = []
        for col in required_columns:
            if col not in df.columns:
                missing_columns.append(col)

        return missing_columns
    
    def _parse_surgery_data(self, df: pd.DataFrame) -> Tuple[List[Surgery], List[str]]:
        """解析手术数据"""
        surgeries = []
        errors = []
        
        for index, row in df.iterrows():
            try:
                # 解析单行数据
                surgery = self._parse_single_row(row, index + 1)
                if surgery:
                    surgeries.append(surgery)
                    
            except Exception as e:
                error_msg = f"第{index + 1}行数据解析失败: {str(e)}"
                errors.append(error_msg)
                self.logger.warning(error_msg)
        
        return surgeries, errors
    
    def _parse_single_row(self, row: pd.Series, row_number: int) -> Optional[Surgery]:
        """解析单行数据"""
        try:
            # 基础字段
            department = clean_string(str(row.get('department', '')))
            patient_name = clean_string(str(row.get('patient_name', '')))
            age = self._parse_age(row.get('age'), f"第{row_number}行年龄")
            bed_number = clean_string(str(row.get('bed_number', '')))
            surgery_type = clean_string(str(row.get('surgery_type', '')))
            anesthesia_type = clean_string(str(row.get('anesthesia_type', '')))
            allergy_history = clean_string(str(row.get('allergy_history', '')))
            special_situation = clean_string(str(row.get('special_situation', '')))

            # 检查必需字段
            if not all([department, patient_name, bed_number, surgery_type, anesthesia_type]):
                raise ValueError("缺少必需字段")

            # 解析手术日期
            surgery_date = self._parse_date(row.get('surgery_date'), f"第{row_number}行手术日期")

            # 解析是否急诊
            is_emergency = self._parse_emergency(row.get('is_emergency', ''))

            # 解析状态
            status = self._parse_status(row.get('status', ''))

            # 创建Surgery对象
            surgery = Surgery(
                department=department,
                patient_name=patient_name,
                age=age,
                bed_number=bed_number,
                surgery_type=surgery_type,
                anesthesia_type=anesthesia_type,
                allergy_history=allergy_history,
                special_situation=special_situation,
                surgery_date=surgery_date,
                is_emergency=is_emergency,
                status=status
            )

            # 验证数据
            validation_errors = surgery.validate()
            if validation_errors:
                raise ValueError(f"数据验证失败: {', '.join(validation_errors)}")

            return surgery

        except Exception as e:
            raise ValueError(f"解析失败: {str(e)}")
    
    def _parse_age(self, value: Any, field_name: str) -> int:
        """解析年龄"""
        if pd.isna(value) or value == '' or value is None:
            raise ValueError(f"{field_name}不能为空")

        try:
            age = int(float(value))
            if age <= 0 or age > 150:
                raise ValueError(f"{field_name}必须在1-150之间")
            return age
        except (ValueError, TypeError):
            raise ValueError(f"{field_name}格式错误，必须是有效数字")

    def _parse_date(self, value: Any, field_name: str) -> date:
        """解析日期"""
        if pd.isna(value) or value == '' or value is None:
            raise ValueError(f"{field_name}不能为空")

        try:
            # 如果已经是date对象
            if isinstance(value, date):
                return value

            # 如果是datetime对象
            if isinstance(value, datetime):
                return value.date()

            # 如果是pandas的Timestamp
            if hasattr(value, 'to_pydatetime'):
                return value.to_pydatetime().date()

            # 字符串解析
            if isinstance(value, str):
                dt = format_datetime(value)
                return dt.date() if dt else None

            # 其他类型尝试转换
            return pd.to_datetime(value).date()

        except Exception as e:
            raise ValueError(f"{field_name}格式错误: {str(e)}")

    def _parse_emergency(self, value: Any) -> bool:
        """解析是否急诊"""
        if pd.isna(value) or value == '' or value is None:
            return False

        value_str = clean_string(str(value)).lower()

        # 判断是否为急诊
        emergency_keywords = ['是', 'true', '1', '急诊', 'emergency', 'urgent']
        return any(keyword in value_str for keyword in emergency_keywords)
    
    def _parse_status(self, value: Any) -> SurgeryStatus:
        """解析状态"""
        if pd.isna(value) or value == '' or value is None:
            return SurgeryStatus.SCHEDULED
        
        status_str = clean_string(str(value)).lower()
        
        # 直接匹配
        for key, status in self.STATUS_MAPPING.items():
            if key.lower() == status_str:
                return status
        
        # 模糊匹配
        if '完成' in status_str or 'complete' in status_str:
            return SurgeryStatus.COMPLETED
        elif '进行' in status_str or 'progress' in status_str:
            return SurgeryStatus.IN_PROGRESS
        elif '取消' in status_str or 'cancel' in status_str:
            return SurgeryStatus.CANCELLED
        else:
            return SurgeryStatus.SCHEDULED
    
    def get_sample_template(self) -> Dict[str, Any]:
        """获取Excel模板示例"""
        return {
            "columns": [
                "科室", "患者姓名", "年龄", "床号", "手术方式",
                "麻醉方式", "过敏史", "特殊情况", "手术日期", "是否急诊", "状态"
            ],
            "sample_data": [
                {
                    "科室": "普外科",
                    "患者姓名": "张三",
                    "年龄": 45,
                    "床号": "15床",
                    "手术方式": "阑尾切除术",
                    "麻醉方式": "全身麻醉",
                    "过敏史": "",
                    "特殊情况": "",
                    "手术日期": "2025-08-03",
                    "是否急诊": "否",
                    "状态": "已完成"
                },
                {
                    "科室": "肝胆外科",
                    "患者姓名": "李四",
                    "年龄": 52,
                    "床号": "8床",
                    "手术方式": "胆囊切除术",
                    "麻醉方式": "全身麻醉",
                    "过敏史": "青霉素过敏",
                    "特殊情况": "",
                    "手术日期": "2025-08-03",
                    "是否急诊": "否",
                    "状态": "进行中"
                }
            ],
            "status_options": ["已安排", "进行中", "已完成", "已取消"],
            "emergency_options": ["是", "否"],
            "notes": [
                "1. 科室、患者姓名、年龄、床号、手术方式、麻醉方式、手术日期为必填字段",
                "2. 年龄必须是1-150之间的数字",
                "3. 手术日期格式: YYYY-MM-DD",
                "4. 是否急诊: 是/否",
                "5. 状态可选值: 已安排、进行中、已完成、已取消",
                "6. 文件格式支持: .xlsx 和 .xls",
                "7. 文件大小限制: 10MB"
            ]
        }
