<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麻醉科交班大屏</title>

    <!-- CSS样式 -->
    <!-- 统一主题变量系统 - 必须最先加载 -->
    <link rel="stylesheet" href="/static/css/theme-variables.css">
    <link rel="stylesheet" href="/static/css/fullscreen-layout.css">
    <link rel="stylesheet" href="/static/css/modern-components.css">

    <!-- 组件样式 -->
    <link rel="stylesheet" href="/static/css/components/card-components.css">

    <!-- 页面专用样式 - 按需加载 -->
    <link rel="stylesheet" href="/static/css/pages/title-page.css">
    <link rel="stylesheet" href="/static/css/pages/statistics-page.css">
    <link rel="stylesheet" href="/static/css/pages/today-stats-page.css">
    <link rel="stylesheet" href="/static/css/pages/surgery-list-page.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        /* 控制面板样式 - 使用主题变量 */
        .control-panel {
            position: fixed;
            bottom: var(--spacing-lg);
            right: var(--spacing-lg);
            background: var(--theme-glass);
            color: var(--theme-text-light);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
            z-index: 9999;
            transition: var(--transition-base);
            backdrop-filter: blur(20px);
            border: 1px solid var(--theme-border);
        }

        .control-panel.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .control-panel button {
            background: var(--theme-primary);
            color: var(--theme-text-light);
            border: none;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            font-size: var(--font-size-xs);
            transition: var(--transition-fast);
        }

        .control-panel button:hover {
            background: var(--theme-secondary);
        }

        .control-item {
            display: flex;
            align-items: center;
        }

        /* 全屏样式 */
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-family: 'Microsoft YaHei', sans-serif;
        }

        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }

        /* 键盘提示 - 使用主题变量 */
        .keyboard-hint {
            position: fixed;
            top: var(--spacing-lg);
            left: var(--spacing-lg);
            background: var(--theme-glass);
            color: var(--theme-text-light);
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-xs);
            z-index: 9998;
            opacity: 0;
            transition: var(--transition-base);
            backdrop-filter: blur(20px);
            border: 1px solid var(--theme-border);
        }

        .keyboard-hint.show {
            opacity: 1;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 三段式布局容器 -->
    <div class="fullscreen-container">
        <!-- 标题栏 -->
        <header class="header-section">
            <div class="header-left">
                <h1 class="header-title">正在加载...</h1>
            </div>
            <div class="header-right">
                <div class="header-time">--:--:-- -- -- --</div>
                <div class="theme-indicator">
                    <span class="theme-name" id="currentThemeName">自动主题</span>
                </div>
            </div>
        </header>

        <!-- 内容区 -->
        <main class="content-area">
            <!-- 页面内容将由JavaScript动态生成 -->
            <div class="loading-container">
                <div class="loading-content">
                    <h2>正在加载交班大屏...</h2>
                    <div class="loading-spinner"></div>
                    <p>5种模块化页面模板</p>
                    <div id="debug-info" style="margin-top: 20px; color: white; font-size: 14px;">
                        <p>调试信息:</p>
                        <p id="theme-status">主题系统: 未加载</p>
                        <p id="page-manager-status">页面管理器: 未加载</p>
                        <p id="error-info"></p>
                    </div>
                </div>
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="status-section">
            <div class="status-left">
                <span class="hospital-info">{{ hospital_name }} - {{ department_name }}</span>
            </div>
            <div class="status-center">
                <span class="page-indicator" id="pageIndicator">1 / 5</span>
            </div>
            <div class="status-right">
                <span class="manual-mode-status" id="manualModeStatus">手动模式</span>
                <span class="theme-status" id="themeStatus">主题自动切换</span>
            </div>
        </footer>
    </div>

    <!-- 控制面板（隐藏，按键显示） -->
    <div id="controlPanel" class="control-panel hidden">
        <div class="control-item">
            <button onclick="window.handoverPageManager?.prevPage()">上一页</button>
        </div>
        <div class="control-item">
            <button onclick="window.handoverPageManager?.nextPage()">下一页</button>
        </div>

        <div class="control-item">
            <button onclick="window.handoverPageManager?.refresh()">刷新数据</button>
        </div>
        <div class="control-item">
            <span>手动控制模式</span>
        </div>
    </div>

    <!-- 键盘提示 -->
    <div id="keyboardHint" class="keyboard-hint">
        <div>🎨 现代化交班大屏</div>
        <div>← → 切换页面</div>
        <div>R 刷新数据</div>
        <div>C 显示/隐藏控制面板</div>
        <div>主题自动切换 (星期一到五)</div>
    </div>

    <!-- JavaScript组件 -->
    <script>
        console.log('开始加载JavaScript组件...');
    </script>
    <!-- 工具函数 -->
    <script src="/static/js/utils/datetime-utils.js"></script>

    <!-- 基础组件 -->
    <script src="/static/js/components/template/template-engine.js"></script>
    <script src="/static/js/components/pages/base-page.js"></script>

    <!-- 页面组件 -->
    <script src="/static/js/components/pages/title-page.js"></script>
    <script src="/static/js/components/pages/statistics-page.js"></script>
    <script src="/static/js/components/pages/patients-page.js"></script>
    <script src="/static/js/components/pages/notices-page.js"></script>

    <!-- UI组件库 -->
    <script src="/static/js/components/ui/card-components.js"></script>
    <script src="/static/js/utils/medical-icons.js"></script>

    <!-- 系统组件 -->
    <script src="/static/js/components/ui/theme-system.js"></script>
    <script src="/static/js/components/ui/handover-page-manager-new.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
        console.log('所有组件已加载');
    </script>

    <!-- 主脚本 -->
    <script>
        // 控制面板显示/隐藏
        let controlPanelVisible = false;

        document.addEventListener('keydown', (e) => {
            if (e.key === 'c' || e.key === 'C') {
                controlPanelVisible = !controlPanelVisible;
                const panel = document.getElementById('controlPanel');
                if (controlPanelVisible) {
                    panel.classList.remove('hidden');
                } else {
                    panel.classList.add('hidden');
                }
            }

            if (e.key === 'h' || e.key === 'H') {
                const hint = document.getElementById('keyboardHint');
                hint.classList.toggle('show');
                setTimeout(() => {
                    hint.classList.remove('show');
                }, 3000);
            }
        });

        // 初始化现代化页面管理器
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('页面加载完成，初始化现代化页面管理器...');

            // 更新调试信息
            const updateDebugInfo = (id, text) => {
                const element = document.getElementById(id);
                if (element) element.textContent = text;
            };

            try {
                // 等待一下确保所有脚本都加载完成
                await new Promise(resolve => setTimeout(resolve, 500));

                // 检查主题系统
                if (window.ThemeSystem) {
                    updateDebugInfo('theme-status', '主题系统: 已加载');
                    console.log('主题系统已加载');
                } else {
                    updateDebugInfo('theme-status', '主题系统: 未找到');
                    console.error('主题系统未找到');
                }

                // 检查页面管理器
                if (window.HandoverPageManager) {
                    updateDebugInfo('page-manager-status', '页面管理器: 已加载');
                    console.log('页面管理器已加载');

                    // 直接创建页面管理器实例
                    if (!window.handoverPageManager) {
                        console.log('创建页面管理器实例...');
                        window.handoverPageManager = new HandoverPageManager({
                            enableKeyboard: true
                        });
                        updateDebugInfo('page-manager-status', '页面管理器: 已创建实例');
                    }
                } else {
                    updateDebugInfo('page-manager-status', '页面管理器: 未找到');
                    console.error('页面管理器未找到');
                }

                // 初始化UI组件库
                if (window.CardComponents) {
                    window.cardComponents = new CardComponents();
                    updateDebugInfo('card-components-status', '卡片组件库: 已初始化');
                    console.log('卡片组件库已初始化');
                } else {
                    updateDebugInfo('card-components-status', '卡片组件库: 未找到');
                    console.error('卡片组件库未找到');
                }

                // 等待图标库加载
                if (window.MedicalIcons) {
                    window.medicalIcons = new MedicalIcons();
                    updateDebugInfo('medical-icons-status', '医疗图标库: 已初始化');
                    console.log('医疗图标库已初始化');
                } else {
                    updateDebugInfo('medical-icons-status', '医疗图标库: 未找到');
                    console.error('医疗图标库未找到');
                }

                console.log('现代化页面管理器初始化完成');

                // 测试：直接显示内容
                setTimeout(() => {
                    if (window.handoverPageManager) {
                        console.log('页面管理器实例存在，应该已经显示内容');
                        updateDebugInfo('error-info', '页面管理器正常运行');
                    } else {
                        console.log('页面管理器实例不存在，手动显示测试内容');
                        const contentArea = document.querySelector('.content-area');
                        if (contentArea) {
                            contentArea.innerHTML = `
                                <div style="text-align: center; color: white; padding: 50px;">
                                    <h1>麻醉科交班大屏</h1>
                                    <p>系统正在运行中...</p>
                                    <p>如果您看到这个消息，说明页面基本功能正常</p>
                                </div>
                            `;
                        }
                        updateDebugInfo('error-info', '显示了备用内容');
                    }
                }, 2000);

            } catch (error) {
                console.error('页面管理器初始化失败:', error);
                updateDebugInfo('error-info', `错误: ${error.message}`);
            }
        });

        // 显示键盘提示
        setTimeout(() => {
            const hint = document.getElementById('keyboardHint');
            hint.classList.add('show');
            setTimeout(() => {
                hint.classList.remove('show');
            }, 5000);
        }, 2000);
    </script>
</body>
</html>