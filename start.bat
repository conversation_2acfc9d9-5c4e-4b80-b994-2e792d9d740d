@echo off
chcp 65001 >nul
title 麻醉科交班大屏系统 v2.0.0

echo.
echo 🏥 麻醉科交班大屏系统 v2.0.0
echo ==================================================
echo 🎨 现代化模块化组件系统
echo 🌈 5个工作日主题自动切换  
echo 🧩 可复用卡片组件库
echo 📱 完全响应式设计
echo ==================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo    请先安装Python 3.8或更高版本
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv\" (
    echo ⚠️  未找到虚拟环境，正在创建...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

REM 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ 激活虚拟环境失败
    pause
    exit /b 1
)

REM 检查并安装依赖
echo 📦 检查依赖...
pip show fastapi >nul 2>&1
if errorlevel 1 (
    echo 📥 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 安装依赖失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖检查通过
)

echo.
echo 🚀 启动现代化交班大屏系统...
echo    端口: 8001
echo    主题: 根据星期几自动切换
echo    模式: 现代化组件系统
echo.
echo 📊 系统功能:
echo    • 5个工作日主题 (星期一到五)
echo    • 统计卡片 + 人员卡片 + 图表组件  
echo    • 自动页面轮播 (10秒间隔)
echo    • 键盘快捷键控制
echo    • 响应式设计
echo.
echo ⌨️  快捷键:
echo    • ← → : 切换页面
echo    • P/空格 : 暂停/播放
echo    • R : 刷新数据
echo    • C : 控制面板
echo    • H : 帮助信息
echo.
echo 🎯 页面类型:
echo    1. 概览页面 - 值班人员 + 今日统计
echo    2. 今日统计 - 详细手术数据
echo    3. 昨日回顾 - 昨日统计回顾
echo    4. 手术列表 - 详细安排信息
echo    5. 科室分布 - 各科室统计
echo.
echo ==================================================
echo 🎉 系统启动成功! 按 Ctrl+C 停止服务
echo 🌐 浏览器将自动打开: http://localhost:8001
echo ==================================================
echo.

REM 延迟3秒后打开浏览器
timeout /t 3 /nobreak >nul
start http://localhost:8001

REM 启动Python应用
python start.py

echo.
echo 👋 系统已停止
echo 感谢使用麻醉科交班大屏系统!
pause
