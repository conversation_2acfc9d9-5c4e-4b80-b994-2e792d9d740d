/* 组件专用样式 */

/* 头部组件 */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    padding: var(--spacing-lg) 0;
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-title {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
}

.header-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: var(--spacing-xs) 0 0 0;
}

.header-info {
    text-align: right;
    font-size: 14px;
}

.header-time {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.header-date {
    opacity: 0.9;
}

/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.dashboard-item {
    min-height: 200px;
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    margin: var(--spacing-md) 0;
}

.chart-canvas {
    max-height: 100%;
    width: 100% !important;
    height: auto !important;
}

/* 手术列表 */
.surgery-list {
    max-height: 400px;
    overflow-y: auto;
}

.surgery-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--divider-color);
    transition: background-color 0.3s ease;
}

.surgery-item:hover {
    background-color: var(--theme-content-bg);
}

.surgery-item:last-child {
    border-bottom: none;
}

.surgery-time {
    flex: 0 0 80px;
    font-weight: 600;
    color: var(--primary-color);
}

.surgery-info {
    flex: 1;
    margin-left: var(--spacing-md);
}

.surgery-patient {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.surgery-type {
    color: var(--text-secondary);
    font-size: 13px;
    margin-bottom: var(--spacing-xs);
}

.surgery-doctor {
    font-size: 12px;
    color: var(--text-secondary);
}

.surgery-room {
    flex: 0 0 80px;
    text-align: center;
    font-weight: 600;
    color: var(--primary-color);
}

.surgery-status {
    flex: 0 0 80px;
    text-align: center;
}

/* 统计概览 */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-item {
    background: var(--surface-color);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    font-size: 24px;
    margin-bottom: var(--spacing-sm);
}

.stat-icon.scheduled { color: var(--info-color); }
.stat-icon.in-progress { color: var(--warning-color); }
.stat-icon.completed { color: var(--success-color); }
.stat-icon.cancelled { color: var(--error-color); }

/* 重要事项 */
.important-items {
    max-height: 300px;
    overflow-y: auto;
}

.important-item {
    display: flex;
    align-items: flex-start;
    padding: var(--spacing-md);
    border-left: 4px solid var(--info-color);
    margin-bottom: var(--spacing-sm);
    background-color: var(--theme-content-bg);
    border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
}

.important-item.high {
    border-left-color: var(--error-color);
    background-color: var(--theme-error);
}

.important-item.medium {
    border-left-color: var(--warning-color);
    background-color: var(--theme-warning);
}

.important-item.low {
    border-left-color: var(--info-color);
    background-color: var(--theme-info);
}

.important-icon {
    flex: 0 0 24px;
    margin-right: var(--spacing-sm);
    margin-top: 2px;
}

.important-content {
    flex: 1;
}

.important-title {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.important-description {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.important-time {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 设备状态 */
.equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.equipment-item {
    text-align: center;
    padding: var(--spacing-md);
    border: 1px solid var(--divider-color);
    border-radius: var(--border-radius-md);
    transition: border-color 0.3s ease;
}

.equipment-item:hover {
    border-color: var(--primary-color);
}

.equipment-name {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.equipment-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.equipment-count {
    font-size: 18px;
    font-weight: 600;
}

.equipment-available {
    color: var(--success-color);
}

.equipment-maintenance {
    color: var(--warning-color);
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background-color: var(--divider-color);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin: var(--spacing-sm) 0;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-bar.success {
    background-color: var(--success-color);
}

.progress-bar.warning {
    background-color: var(--warning-color);
}

.progress-bar.danger {
    background-color: var(--error-color);
}

/* 刷新指示器 */
.refresh-indicator {
    position: fixed;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--surface-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    font-size: 12px;
    color: var(--text-secondary);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.refresh-indicator.show {
    opacity: 1;
}

/* 连接状态指示器 */
.connection-status {
    position: fixed;
    top: var(--spacing-md);
    right: 200px;
    background: var(--surface-color);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    font-size: 12px;
    z-index: 1000;
    transition: all 0.3s ease;
    border-left: 4px solid var(--error-color);
}

.connection-status.connected {
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.connection-status.disconnected {
    border-left-color: var(--error-color);
    color: var(--error-color);
}

.connection-status::before {
    content: "●";
    margin-right: var(--spacing-xs);
    animation: pulse 2s infinite;
}

.connection-status.connected::before {
    color: var(--success-color);
}

.connection-status.disconnected::before {
    color: var(--error-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--divider-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.modal-close:hover {
    background-color: var(--divider-color);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--divider-color);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .header-info {
        text-align: center;
        margin-top: var(--spacing-md);
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .surgery-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .surgery-time,
    .surgery-room,
    .surgery-status {
        flex: none;
        margin-bottom: var(--spacing-xs);
    }
    
    .equipment-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-overview {
        grid-template-columns: 1fr;
    }
    
    .container {
        padding: var(--spacing-sm);
    }
}
