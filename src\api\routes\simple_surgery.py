"""
简化的手术API路由 - 用于测试分页功能
"""
import logging
from datetime import datetime, date
from typing import List
from fastapi import APIRouter, HTTPException

from src.database.connection import db_connection
from src.models.surgery import Surgery

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/surgeries/today")
async def get_today_surgeries():
    """获取今日手术安排"""
    try:
        today = date.today()
        date_str = today.isoformat()
        
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT * FROM surgeries 
                WHERE surgery_date = ? 
                ORDER BY department, bed_number
            """, (date_str,))
            
            surgeries = []
            for row in cursor.fetchall():
                # 手动构建字典
                surgery_dict = {
                    'id': row[0],
                    'department': row[1],
                    'patient_name': row[2],
                    'age': row[3],
                    'bed_number': row[4],
                    'surgery_type': row[5],
                    'anesthesia_type': row[6],
                    'allergy_history': row[7] or '',
                    'special_situation': row[8] or '',
                    'surgery_date': row[9],
                    'is_emergency': bool(row[10]),
                    'status': row[11],
                    'created_at': row[12],
                    'updated_at': row[13]
                }
                surgeries.append(surgery_dict)
            
            logger.info(f"获取今日手术成功，共{len(surgeries)}条")
            return surgeries
            
    except Exception as e:
        logger.error(f"获取今日手术失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取今日手术失败: {str(e)}")

@router.get("/statistics/simple-today")
async def get_simple_today_statistics():
    """获取简化的今日统计"""
    try:
        today = date.today()
        date_str = today.isoformat()
        
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_surgeries,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_surgeries,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_surgeries,
                    SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled_surgeries,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_surgeries,
                    SUM(CASE WHEN is_emergency = 1 THEN 1 ELSE 0 END) as emergency_surgeries
                FROM surgeries 
                WHERE surgery_date = ?
            """, (date_str,))
            
            result = cursor.fetchone()
            
            stats = {
                'date': date_str,
                'total_surgeries': result[0] or 0,
                'completed_surgeries': result[1] or 0,
                'in_progress_surgeries': result[2] or 0,
                'scheduled_surgeries': result[3] or 0,
                'cancelled_surgeries': result[4] or 0,
                'emergency_surgeries': result[5] or 0
            }
            
            # 计算完成率
            if stats['total_surgeries'] > 0:
                stats['completion_rate'] = round((stats['completed_surgeries'] / stats['total_surgeries']) * 100, 1)
            else:
                stats['completion_rate'] = 0
            
            logger.info(f"获取今日统计成功: {stats}")
            return stats
            
    except Exception as e:
        logger.error(f"获取今日统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取今日统计失败: {str(e)}")

@router.get("/statistics/simple-yesterday")
async def get_simple_yesterday_statistics():
    """获取简化的昨日统计"""
    try:
        from datetime import timedelta
        yesterday = date.today() - timedelta(days=1)
        date_str = yesterday.isoformat()
        
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_surgeries,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_surgeries,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_surgeries,
                    SUM(CASE WHEN is_emergency = 1 THEN 1 ELSE 0 END) as emergency_surgeries
                FROM surgeries 
                WHERE surgery_date = ?
            """, (date_str,))
            
            result = cursor.fetchone()
            
            stats = {
                'date': date_str,
                'total_surgeries': result[0] or 0,
                'completed_surgeries': result[1] or 0,
                'cancelled_surgeries': result[2] or 0,
                'emergency_surgeries': result[3] or 0
            }
            
            # 计算完成率
            if stats['total_surgeries'] > 0:
                stats['completion_rate'] = round((stats['completed_surgeries'] / stats['total_surgeries']) * 100, 1)
            else:
                stats['completion_rate'] = 0
            
            logger.info(f"获取昨日统计成功: {stats}")
            return stats
            
    except Exception as e:
        logger.error(f"获取昨日统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取昨日统计失败: {str(e)}")

@router.get("/statistics/simple-handover")
async def get_simple_handover_summary():
    """获取简化的交班汇总"""
    try:
        today = date.today()
        
        # 获取今日和昨日统计
        today_stats = await get_simple_today_statistics()
        yesterday_stats = await get_simple_yesterday_statistics()
        
        # 获取特殊病例
        date_str = today.isoformat()
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT patient_name, department, allergy_history, special_situation, is_emergency
                FROM surgeries 
                WHERE surgery_date = ? AND (
                    allergy_history != '' OR 
                    special_situation != '' OR 
                    is_emergency = 1
                )
                ORDER BY is_emergency DESC, department
            """, (date_str,))
            
            special_cases = []
            for row in cursor.fetchall():
                special_cases.append({
                    'patient_name': row[0],
                    'department': row[1],
                    'allergy_history': row[2] or '',
                    'special_situation': row[3] or '',
                    'is_emergency': bool(row[4])
                })
        
        summary = {
            'date': today.isoformat(),
            'today_stats': today_stats,
            'yesterday_stats': yesterday_stats,
            'special_cases': special_cases,
            'special_cases_count': len(special_cases)
        }
        
        logger.info(f"获取交班汇总成功")
        return summary
        
    except Exception as e:
        logger.error(f"获取交班汇总失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取交班汇总失败: {str(e)}")
