/**
 * 今日手术统计页面样式 - PPT全屏模式
 * 保持原有的今日统计布局，但应用PPT全屏模式
 */

/* 今日统计页面主容器 - 固定内容，禁止滚动 */
.stats-page {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 2vh 2vw;
    position: fixed;
    top: 0;
    left: 0;
}

/* 页面标题区域 */
.page-header {
    text-align: center;
    margin-bottom: 3vh;
    color: #ffffff;
}

.page-header h2 {
    font-size: 3vw;
    font-weight: 700;
    margin: 0 0 1vh 0;
    text-shadow: 0 0.3vw 0.6vw rgba(0, 0, 0, 0.3);
}

.date-range {
    font-size: 1.5vw;
    font-weight: 500;
    opacity: 0.9;
    text-shadow: 0 0.2vw 0.4vw rgba(0, 0, 0, 0.3);
}

/* 统计卡片网格 - 固定尺寸 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2vw;
    width: 80vw;
    max-width: 1200px;
    margin-bottom: 3vh;
    height: 15vh; /* 固定高度 */
}

/* 今日统计卡片样式 */
.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1.5vw;
    padding: 3vh 2vw;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 1vw 3vw rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    align-items: center;
    gap: 2vw;
    min-height: 12vh;
}

.stat-card:hover {
    transform: translateY(-0.5vh);
    box-shadow: 0 1.5vw 4vw rgba(0, 0, 0, 0.15);
}

/* 统计卡片图标 */
.stat-icon {
    font-size: 3vw;
    opacity: 0.9;
    flex-shrink: 0;
}

/* 统计卡片内容 */
.stat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5vh;
}

.stat-number {
    font-size: 3.5vw;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 0.2vw 0.4vw rgba(0, 0, 0, 0.3);
    line-height: 1;
}

.stat-label {
    font-size: 1.3vw;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 0.1vw 0.2vw rgba(0, 0, 0, 0.3);
}

/* 主题色彩 */
.stat-card.primary {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

.stat-card.info {
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.8) 0%, rgba(56, 249, 215, 0.8) 100%);
}

.stat-card.success {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.8) 0%, rgba(0, 242, 254, 0.8) 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.8) 0%, rgba(245, 87, 108, 0.8) 100%);
}

/* 详细信息区域 - 固定高度 */
.detail-sections {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3vw;
    width: 80vw;
    max-width: 1200px;
    height: 50vh; /* 固定高度 */
    overflow: hidden; /* 禁止滚动 */
}

.detail-section {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1.5vw;
    padding: 2vh 2vw;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 1vw 3vw rgba(0, 0, 0, 0.1);
    overflow: hidden; /* 禁止内容溢出 */
    display: flex;
    flex-direction: column;
}

.detail-section h3 {
    color: #ffffff;
    font-size: 1.8vw;
    font-weight: 600;
    margin: 0 0 2vh 0;
    text-shadow: 0 0.2vw 0.4vw rgba(0, 0, 0, 0.3);
}

/* 科室网格 - 固定高度，自适应内容 */
.department-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5vh 2vw;
    flex: 1;
    overflow: hidden;
    max-height: 35vh; /* 限制最大高度 */
}

.dept-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1vh 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dept-name {
    font-size: 1.2vw;
    color: rgba(255, 255, 255, 0.9);
}

.dept-count {
    font-size: 1.2vw;
    font-weight: 600;
    color: #ffffff;
}

/* 麻醉方式图表 - 固定高度 */
.anesthesia-chart {
    display: flex;
    flex-direction: column;
    gap: 1vh;
    flex: 1;
    overflow: hidden;
    max-height: 35vh; /* 限制最大高度 */
}

.chart-item {
    display: flex;
    align-items: center;
    gap: 1vw;
}

.chart-bar {
    height: 1.5vh;
    background: linear-gradient(90deg, rgba(67, 233, 123, 0.8) 0%, rgba(56, 249, 215, 0.8) 100%);
    border-radius: 0.75vh;
    transition: width 0.5s ease;
}

.chart-label {
    font-size: 1.1vw;
    color: rgba(255, 255, 255, 0.9);
    white-space: nowrap;
}

/* 确保在所有屏幕尺寸下保持相同的视觉效果 */
@media (max-aspect-ratio: 16/9) {
    .stats-grid,
    .detail-sections {
        width: 90vw;
    }
}

@media (min-aspect-ratio: 16/9) {
    .stats-grid,
    .detail-sections {
        width: calc(90vh * 16 / 9 * 0.8);
    }
}
