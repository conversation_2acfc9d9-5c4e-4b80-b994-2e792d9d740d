"""
WebSocket连接管理器
"""
import json
import asyncio
import logging
from typing import Set, Dict, Any, Optional
from datetime import datetime
import weakref

from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)

class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 使用WeakSet自动清理断开的连接
        self.active_connections: Set[WebSocket] = set()
        self.connection_info: Dict[WebSocket, Dict[str, Any]] = {}
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.heartbeat_interval = 30  # 30秒心跳间隔
        
    async def connect(self, websocket: WebSocket, client_info: Dict[str, Any] = None):
        """
        接受WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
            client_info: 客户端信息
        """
        await websocket.accept()
        self.active_connections.add(websocket)
        
        # 记录连接信息
        self.connection_info[websocket] = {
            "connected_at": datetime.now(),
            "client_info": client_info or {},
            "last_heartbeat": datetime.now()
        }
        
        logger.info(f"WebSocket连接已建立，当前连接数: {len(self.active_connections)}")
        
        # 启动心跳任务（如果还没有启动）
        if self.heartbeat_task is None or self.heartbeat_task.done():
            self.heartbeat_task = asyncio.create_task(self.heartbeat_loop())
    
    def disconnect(self, websocket: WebSocket):
        """
        断开WebSocket连接
        
        Args:
            websocket: WebSocket连接对象
        """
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        
        if websocket in self.connection_info:
            del self.connection_info[websocket]
        
        logger.info(f"WebSocket连接已断开，当前连接数: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """
        发送个人消息
        
        Args:
            message: 消息内容
            websocket: 目标WebSocket连接
        """
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """
        广播消息给所有连接的客户端
        
        Args:
            message: 消息内容
        """
        if not self.active_connections:
            return
        
        # 创建发送任务列表
        tasks = []
        disconnected_connections = []
        
        for connection in self.active_connections.copy():
            try:
                task = asyncio.create_task(connection.send_text(message))
                tasks.append((connection, task))
            except Exception as e:
                logger.error(f"创建发送任务失败: {e}")
                disconnected_connections.append(connection)
        
        # 等待所有发送任务完成
        for connection, task in tasks:
            try:
                await task
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected_connections.append(connection)
        
        # 清理断开的连接
        for connection in disconnected_connections:
            self.disconnect(connection)
        
        logger.info(f"广播消息完成，发送给 {len(tasks)} 个连接")
    
    async def broadcast_json(self, data: Dict[str, Any]):
        """
        广播JSON数据
        
        Args:
            data: 要广播的数据
        """
        try:
            message = json.dumps(data, ensure_ascii=False, default=str)
            await self.broadcast(message)
        except Exception as e:
            logger.error(f"广播JSON数据失败: {e}")
    
    async def send_update(self, update_type: str, data: Any):
        """
        发送数据更新通知
        
        Args:
            update_type: 更新类型
            data: 更新数据
        """
        update_message = {
            "type": "update",
            "update_type": update_type,
            "data": data,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.broadcast_json(update_message)
    
    async def send_notification(self, notification_type: str, message: str, level: str = "info"):
        """
        发送通知消息
        
        Args:
            notification_type: 通知类型
            message: 通知消息
            level: 通知级别 (info, warning, error, success)
        """
        notification = {
            "type": "notification",
            "notification_type": notification_type,
            "message": message,
            "level": level,
            "timestamp": datetime.now().isoformat()
        }
        
        await self.broadcast_json(notification)
    
    async def heartbeat_loop(self):
        """心跳循环"""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                if not self.active_connections:
                    continue
                
                # 发送心跳消息
                heartbeat_message = {
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat(),
                    "server_time": datetime.now().strftime("%H:%M:%S")
                }
                
                await self.broadcast_json(heartbeat_message)
                
            except asyncio.CancelledError:
                logger.info("心跳任务已取消")
                break
            except Exception as e:
                logger.error(f"心跳循环错误: {e}")
    
    def get_connection_count(self) -> int:
        """获取当前连接数"""
        return len(self.active_connections)
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "total_connections": len(self.active_connections),
            "connections": [
                {
                    "connected_at": info["connected_at"].isoformat(),
                    "client_info": info["client_info"],
                    "last_heartbeat": info["last_heartbeat"].isoformat()
                }
                for info in self.connection_info.values()
            ]
        }
    
    async def handle_client_message(self, websocket: WebSocket, message: str):
        """
        处理客户端消息
        
        Args:
            websocket: WebSocket连接
            message: 客户端消息
        """
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            if message_type == "heartbeat":
                # 更新心跳时间
                if websocket in self.connection_info:
                    self.connection_info[websocket]["last_heartbeat"] = datetime.now()
                
                # 回复心跳
                response = {
                    "type": "heartbeat_response",
                    "timestamp": datetime.now().isoformat()
                }
                await self.send_personal_message(json.dumps(response), websocket)
            
            elif message_type == "subscribe":
                # 处理订阅请求
                subscription_type = data.get("subscription_type")
                logger.info(f"客户端订阅: {subscription_type}")
                
                # 发送当前数据
                await self.send_current_data(websocket, subscription_type)
            
            elif message_type == "ping":
                # 处理ping请求
                response = {
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }
                await self.send_personal_message(json.dumps(response), websocket)
            
            else:
                logger.warning(f"未知消息类型: {message_type}")
                
        except json.JSONDecodeError:
            logger.error(f"无效的JSON消息: {message}")
        except Exception as e:
            logger.error(f"处理客户端消息失败: {e}")
    
    async def send_current_data(self, websocket: WebSocket, data_type: str):
        """
        发送当前数据给指定客户端
        
        Args:
            websocket: WebSocket连接
            data_type: 数据类型
        """
        try:
            # 根据数据类型获取相应数据
            if data_type == "statistics":
                from src.services.statistics_service import StatisticsService
                stats_service = StatisticsService()
                today_stats = stats_service.get_today_statistics()
                
                response = {
                    "type": "current_data",
                    "data_type": data_type,
                    "data": today_stats.to_dict(),
                    "timestamp": datetime.now().isoformat()
                }
                
                await self.send_personal_message(json.dumps(response, default=str), websocket)
            
            elif data_type == "surgeries":
                from src.services.handover_service import HandoverService
                handover_service = HandoverService()
                surgeries = handover_service.get_surgeries_by_date(datetime.now().date())
                
                response = {
                    "type": "current_data",
                    "data_type": data_type,
                    "data": [s.to_dict() for s in surgeries],
                    "timestamp": datetime.now().isoformat()
                }
                
                await self.send_personal_message(json.dumps(response, default=str), websocket)
                
        except Exception as e:
            logger.error(f"发送当前数据失败: {e}")

# 全局连接管理器实例
manager = ConnectionManager()
