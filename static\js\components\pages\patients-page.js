/**
 * 患者页组件
 * 显示特殊病人信息
 */

class PatientsPage extends BasePage {
    constructor(options = {}) {
        super({
            autoRefresh: true,
            refreshInterval: 120000, // 2分钟刷新一次
            ...options
        });
    }
    
    /**
     * 加载页面数据
     */
    async loadData() {
        try {
            const patients = await this.getSpecialPatients();
            
            this.data = {
                title: '特殊病人信息',
                subtitle: '需要特别关注',
                patients,
                totalCount: patients.length
            };
            
            console.log('患者页数据加载完成:', this.data);
        } catch (error) {
            console.error('患者页数据加载失败:', error);
            this.data = this.getDefaultData();
        }
    }
    
    /**
     * 获取特殊患者数据
     */
    async getSpecialPatients() {
        try {
            const response = await fetch('/api/surgeries/special');
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            
            // 转换数据格式
            return data.map(patient => ({
                id: patient.id,
                name: patient.patient_name,
                age: this.extractAge(patient.patient_name), // 从姓名中提取年龄，实际应该有单独字段
                gender: this.extractGender(patient.patient_name), // 从姓名中提取性别，实际应该有单独字段
                department: patient.department,
                surgery: patient.surgery_name,
                anesthesia: patient.anesthesia_type,
                allergies: patient.allergies ? patient.allergies.split(',').map(a => a.trim()) : [],
                specialNotes: patient.special_notes,
                riskLevel: this.calculateRiskLevel(patient),
                isEmergency: patient.is_emergency
            }));
        } catch (error) {
            console.error('获取特殊患者数据失败:', error);
            return this.getDefaultPatients();
        }
    }
    
    /**
     * 提取年龄（临时方法，实际应该有专门的字段）
     */
    extractAge(name) {
        // 这里只是示例，实际应该从数据库字段获取
        return Math.floor(Math.random() * 60) + 20;
    }
    
    /**
     * 提取性别（临时方法，实际应该有专门的字段）
     */
    extractGender(name) {
        // 这里只是示例，实际应该从数据库字段获取
        return Math.random() > 0.5 ? '男' : '女';
    }
    
    /**
     * 计算风险等级
     */
    calculateRiskLevel(patient) {
        let riskScore = 0;
        
        // 急诊手术增加风险
        if (patient.is_emergency) riskScore += 2;
        
        // 有过敏史增加风险
        if (patient.allergies && patient.allergies.length > 0) riskScore += 1;
        
        // 有特殊注意事项增加风险
        if (patient.special_notes && patient.special_notes.length > 0) riskScore += 1;
        
        // 某些科室风险较高
        const highRiskDepts = ['心胸外科', '神经外科', '小儿外科'];
        if (highRiskDepts.includes(patient.department)) riskScore += 1;
        
        if (riskScore >= 3) return 'high';
        if (riskScore >= 2) return 'medium';
        return 'low';
    }
    
    /**
     * 获取默认患者数据
     */
    getDefaultPatients() {
        return [
            {
                id: 1,
                name: '张**',
                age: 65,
                gender: '男',
                department: '心胸外科',
                surgery: '冠状动脉搭桥术',
                anesthesia: '全身麻醉',
                allergies: ['青霉素', '碘造影剂'],
                specialNotes: '高血压、糖尿病史，术前血糖控制不佳',
                riskLevel: 'high',
                isEmergency: true
            },
            {
                id: 2,
                name: '李**',
                age: 3,
                gender: '女',
                department: '小儿外科',
                surgery: '先天性心脏病修补术',
                anesthesia: '全身麻醉',
                allergies: [],
                specialNotes: '低体重儿童，需特殊监护',
                riskLevel: 'high',
                isEmergency: false
            }
        ];
    }
    
    /**
     * 获取默认数据
     */
    getDefaultData() {
        const patients = this.getDefaultPatients();
        return {
            title: '特殊病人信息',
            subtitle: '需要特别关注',
            patients,
            totalCount: patients.length
        };
    }
    
    /**
     * 获取页面模板
     */
    getTemplate() {
        return `
            <div class="patients-page">
                <div class="page-header">
                    <h2>{{title}}</h2>
                    <div class="date-range">{{subtitle}} ({{totalCount}}例)</div>
                </div>
                
                <div class="patients-list">
                    {{#each patients}}
                    <div class="patient-card {{classIf this.riskLevel === 'high' 'high-risk'}}">
                        <div class="patient-header">
                            <span class="patient-name">{{this.name}}</span>
                            <div class="patient-badges">
                                {{#if this.isEmergency}}
                                <span class="badge emergency">急诊</span>
                                {{/if}}
                                <span class="risk-badge {{this.riskLevel}}">
                                    {{#if this.riskLevel === 'high'}}高风险{{/if}}
                                    {{#if this.riskLevel === 'medium'}}中风险{{/if}}
                                    {{#if this.riskLevel === 'low'}}低风险{{/if}}
                                </span>
                            </div>
                        </div>
                        
                        <div class="patient-info">
                            <div class="info-row">
                                <span class="label">年龄:</span>
                                <span class="value">{{this.age}}岁 {{this.gender}}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">科室:</span>
                                <span class="value">{{this.department}}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">手术:</span>
                                <span class="value">{{this.surgery}}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">麻醉:</span>
                                <span class="value">{{this.anesthesia}}</span>
                            </div>
                            {{#if this.allergies.length}}
                            <div class="info-row">
                                <span class="label">过敏:</span>
                                <span class="value allergies">
                                    {{#each this.allergies}}
                                    <span class="allergy-tag">{{this}}</span>
                                    {{/each}}
                                </span>
                            </div>
                            {{/if}}
                        </div>
                        
                        {{#if this.specialNotes}}
                        <div class="special-notes">
                            <strong>特殊注意:</strong> {{this.specialNotes}}
                        </div>
                        {{/if}}
                    </div>
                    {{/each}}
                </div>
                
                {{#if patients.length === 0}}
                <div class="empty-state">
                    <div class="empty-icon">👥</div>
                    <div class="empty-text">暂无特殊病人信息</div>
                </div>
                {{/if}}
            </div>
        `;
    }
    
    /**
     * 渲染页面
     */
    async render() {
        if (!this.isInitialized) {
            await this.init();
        }
        
        const template = this.getTemplate();
        return window.templateEngine.render(template, this.data);
    }
    
    /**
     * 获取页面标题
     */
    getTitle() {
        return '特殊病人信息';
    }
    
    /**
     * 获取页面类型
     */
    getType() {
        return 'patients';
    }
    
    /**
     * 页面激活时调用
     */
    onActivate() {
        super.onActivate();
        // 激活时立即刷新数据
        this.refresh();
    }
}

// 全局导出
window.PatientsPage = PatientsPage;
