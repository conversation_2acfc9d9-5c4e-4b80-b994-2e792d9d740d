"""
文件上传相关API路由
"""
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Optional
import logging

from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel

from config.settings import settings
from src.database.connection import db_connection
from src.services.data_processor import DataProcessor
from src.services.excel_parser import ExcelParser

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/upload", tags=["upload"])

class UploadResponse(BaseModel):
    """文件上传响应模型"""
    success: bool
    message: str
    filename: str
    file_size: int
    upload_id: int
    imported_count: int = 0
    errors: List[str] = []

class ProcessResponse(BaseModel):
    """文件处理响应模型"""
    success: bool
    message: str
    imported_count: int
    total_parsed: int
    errors: List[str]

class UploadRecord(BaseModel):
    """上传记录响应模型"""
    id: int
    filename: str
    original_filename: str
    file_size: int
    upload_time: str
    status: str
    imported_count: int
    error_message: Optional[str] = None

@router.post("/excel", response_model=UploadResponse)
async def upload_excel_file(file: UploadFile = File(...)):
    """上传Excel文件"""
    try:
        # 验证文件类型
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in settings.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件格式，仅支持: {', '.join(settings.ALLOWED_EXTENSIONS)}"
            )
        
        # 读取文件内容
        content = await file.read()
        file_size = len(content)
        
        # 验证文件大小
        if file_size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400, 
                detail=f"文件大小超过限制，最大允许 {settings.MAX_FILE_SIZE / 1024 / 1024:.1f}MB"
            )
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}_{file.filename}"
        file_path = settings.UPLOAD_DIR / unique_filename
        
        # 确保上传目录存在
        settings.UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        with open(file_path, "wb") as f:
            f.write(content)
        
        # 记录上传信息到数据库
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                INSERT INTO upload_records (
                    filename, original_filename, file_path, file_size, status
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                unique_filename,
                file.filename,
                str(file_path),
                file_size,
                "uploaded"
            ))
            
            upload_id = cursor.lastrowid
            conn.commit()
        
        logger.info(f"文件上传成功: {file.filename} -> {unique_filename}")

        return UploadResponse(
            success=True,
            message="文件上传成功",
            filename=unique_filename,
            file_size=file_size,
            upload_id=upload_id,
            imported_count=0,
            errors=[]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail="文件上传失败")

@router.get("/records", response_model=List[UploadRecord])
async def get_upload_records(limit: int = 20):
    """获取上传记录列表"""
    try:
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT id, filename, original_filename, file_size, 
                       upload_time, status, imported_count, error_message
                FROM upload_records 
                ORDER BY upload_time DESC 
                LIMIT ?
            """, (limit,))
            
            records = []
            for row in cursor.fetchall():
                records.append(UploadRecord(
                    id=row[0],
                    filename=row[1],
                    original_filename=row[2],
                    file_size=row[3],
                    upload_time=row[4],
                    status=row[5],
                    imported_count=row[6] or 0,
                    error_message=row[7]
                ))
            
            logger.info(f"获取上传记录成功，共{len(records)}条")
            return records
            
    except Exception as e:
        logger.error(f"获取上传记录失败: {e}")
        raise HTTPException(status_code=500, detail="获取上传记录失败")

@router.delete("/records/{record_id}")
async def delete_upload_record(record_id: int):
    """删除上传记录和文件"""
    try:
        with db_connection.get_db_context() as conn:
            # 获取文件信息
            cursor = conn.execute(
                "SELECT file_path FROM upload_records WHERE id = ?", 
                (record_id,)
            )
            row = cursor.fetchone()
            
            if not row:
                raise HTTPException(status_code=404, detail="上传记录不存在")
            
            file_path = Path(row[0])
            
            # 删除数据库记录
            cursor = conn.execute(
                "DELETE FROM upload_records WHERE id = ?", 
                (record_id,)
            )
            
            if cursor.rowcount == 0:
                raise HTTPException(status_code=404, detail="上传记录不存在")
            
            conn.commit()
            
            # 删除文件
            try:
                if file_path.exists():
                    file_path.unlink()
                    logger.info(f"删除文件成功: {file_path}")
            except Exception as e:
                logger.warning(f"删除文件失败: {e}")
            
            logger.info(f"删除上传记录成功: {record_id}")
            return {"success": True, "message": "删除成功"}
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除上传记录失败: {e}")
        raise HTTPException(status_code=500, detail="删除上传记录失败")

@router.get("/records/{record_id}", response_model=UploadRecord)
async def get_upload_record(record_id: int):
    """获取单个上传记录"""
    try:
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT id, filename, original_filename, file_size, 
                       upload_time, status, imported_count, error_message
                FROM upload_records 
                WHERE id = ?
            """, (record_id,))
            
            row = cursor.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="上传记录不存在")
            
            record = UploadRecord(
                id=row[0],
                filename=row[1],
                original_filename=row[2],
                file_size=row[3],
                upload_time=row[4],
                status=row[5],
                imported_count=row[6] or 0,
                error_message=row[7]
            )
            
            logger.info(f"获取上传记录成功: {record_id}")
            return record
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取上传记录失败: {e}")
        raise HTTPException(status_code=500, detail="获取上传记录失败")

@router.post("/process/{upload_id}", response_model=ProcessResponse)
async def process_uploaded_file(upload_id: int):
    """处理已上传的Excel文件"""
    try:
        # 获取上传记录
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT file_path, status FROM upload_records WHERE id = ?
            """, (upload_id,))

            row = cursor.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="上传记录不存在")

            file_path, status = row

            if status == "processing":
                raise HTTPException(status_code=400, detail="文件正在处理中")

            if status == "completed":
                raise HTTPException(status_code=400, detail="文件已经处理完成")

        # 检查文件是否存在
        if not Path(file_path).exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        # 处理文件
        processor = DataProcessor()
        result = processor.process_excel_file(file_path, upload_id)

        return ProcessResponse(
            success=result["success"],
            message=result["message"],
            imported_count=result["imported_count"],
            total_parsed=result.get("total_parsed", 0),
            errors=result["errors"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理文件失败: {e}")
        raise HTTPException(status_code=500, detail="处理文件失败")

@router.post("/upload-and-process", response_model=ProcessResponse)
async def upload_and_process_excel(file: UploadFile = File(...)):
    """上传并立即处理Excel文件"""
    try:
        # 先上传文件
        upload_response = await upload_excel_file(file)

        if not upload_response.success:
            return ProcessResponse(
                success=False,
                message=upload_response.message,
                imported_count=0,
                total_parsed=0,
                errors=[upload_response.message]
            )

        # 立即处理文件
        process_response = await process_uploaded_file(upload_response.upload_id)
        return process_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传并处理文件失败: {e}")
        raise HTTPException(status_code=500, detail="上传并处理文件失败")

@router.get("/validate/{upload_id}")
async def validate_excel_structure(upload_id: int):
    """验证Excel文件结构"""
    try:
        # 获取上传记录
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT file_path FROM upload_records WHERE id = ?
            """, (upload_id,))

            row = cursor.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="上传记录不存在")

            file_path = row[0]

        # 检查文件是否存在
        if not Path(file_path).exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        # 验证文件结构
        processor = DataProcessor()
        result = processor.validate_excel_structure(file_path)

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证文件结构失败: {e}")
        raise HTTPException(status_code=500, detail="验证文件结构失败")

@router.get("/template")
async def get_excel_template():
    """获取Excel模板信息"""
    try:
        parser = ExcelParser()
        template_info = parser.get_sample_template()

        return template_info

    except Exception as e:
        logger.error(f"获取模板信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取模板信息失败")

@router.get("/statistics")
async def get_import_statistics(days: int = 7):
    """获取导入统计信息"""
    try:
        processor = DataProcessor()
        stats = processor.get_import_statistics(days)

        return stats

    except Exception as e:
        logger.error(f"获取导入统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取导入统计失败")
