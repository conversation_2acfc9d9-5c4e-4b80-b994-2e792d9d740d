"""
数据格式化工具
"""
import re
from datetime import datetime, date
from typing import Any, Optional
import logging

logger = logging.getLogger(__name__)

def clean_string(value: Any) -> str:
    """
    清理字符串
    
    Args:
        value: 输入值
        
    Returns:
        str: 清理后的字符串
    """
    if value is None:
        return ""
    
    # 转换为字符串
    text = str(value).strip()
    
    # 移除控制字符和不可见字符
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    # 移除首尾空白
    text = text.strip()
    
    return text

def format_datetime(value: Any) -> Optional[datetime]:
    """
    格式化日期时间
    
    Args:
        value: 输入值
        
    Returns:
        Optional[datetime]: 格式化后的日期时间对象
    """
    if not value:
        return None
    
    # 如果已经是datetime对象
    if isinstance(value, datetime):
        return value
    
    # 如果是pandas的Timestamp
    if hasattr(value, 'to_pydatetime'):
        return value.to_pydatetime()
    
    # 字符串解析
    if isinstance(value, str):
        text = clean_string(value)
        if not text:
            return None
        
        # 尝试不同的日期时间格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y/%m/%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y/%m/%d %H:%M',
            '%Y-%m-%d',
            '%Y/%m/%d',
            '%m/%d/%Y %H:%M:%S',
            '%m/%d/%Y %H:%M',
            '%m/%d/%Y',
            '%d/%m/%Y %H:%M:%S',
            '%d/%m/%Y %H:%M',
            '%d/%m/%Y'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(text, fmt)
            except ValueError:
                continue
        
        # 如果所有格式都失败，尝试pandas的to_datetime
        try:
            import pandas as pd
            return pd.to_datetime(text).to_pydatetime()
        except Exception:
            pass
    
    # 其他类型尝试转换
    try:
        import pandas as pd
        return pd.to_datetime(value).to_pydatetime()
    except Exception:
        pass
    
    return None

def format_date(value: Any) -> Optional[date]:
    """
    格式化日期
    
    Args:
        value: 输入值
        
    Returns:
        Optional[date]: 格式化后的日期对象
    """
    dt = format_datetime(value)
    return dt.date() if dt else None

def format_time_duration(minutes: float) -> str:
    """
    格式化时间长度
    
    Args:
        minutes: 分钟数
        
    Returns:
        str: 格式化后的时间字符串
    """
    if not minutes or minutes <= 0:
        return "0分钟"
    
    hours = int(minutes // 60)
    mins = int(minutes % 60)
    
    if hours > 0:
        return f"{hours}小时{mins}分钟"
    else:
        return f"{mins}分钟"

def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 文件大小（字节）
        
    Returns:
        str: 格式化后的文件大小字符串
    """
    if size_bytes == 0:
        return "0 B"
    
    units = ['B', 'KB', 'MB', 'GB', 'TB']
    unit_index = 0
    size = float(size_bytes)
    
    while size >= 1024 and unit_index < len(units) - 1:
        size /= 1024
        unit_index += 1
    
    if unit_index == 0:
        return f"{int(size)} {units[unit_index]}"
    else:
        return f"{size:.1f} {units[unit_index]}"

def format_percentage(value: float, decimal_places: int = 1) -> str:
    """
    格式化百分比
    
    Args:
        value: 数值（0-100）
        decimal_places: 小数位数
        
    Returns:
        str: 格式化后的百分比字符串
    """
    if value is None:
        return "0%"
    
    return f"{value:.{decimal_places}f}%"

def format_number(value: Any, decimal_places: int = 2) -> str:
    """
    格式化数字
    
    Args:
        value: 数值
        decimal_places: 小数位数
        
    Returns:
        str: 格式化后的数字字符串
    """
    if value is None:
        return "0"
    
    try:
        num = float(value)
        if decimal_places == 0:
            return f"{int(num):,}"
        else:
            return f"{num:,.{decimal_places}f}"
    except (ValueError, TypeError):
        return str(value)

def format_phone_number(phone: str) -> str:
    """
    格式化电话号码
    
    Args:
        phone: 电话号码
        
    Returns:
        str: 格式化后的电话号码
    """
    if not phone:
        return ""
    
    # 移除所有非数字字符
    digits = re.sub(r'\D', '', phone)
    
    # 中国手机号码格式化
    if len(digits) == 11 and digits.startswith('1'):
        return f"{digits[:3]}-{digits[3:7]}-{digits[7:]}"
    
    return phone

def format_patient_id(patient_id: str) -> str:
    """
    格式化患者ID
    
    Args:
        patient_id: 患者ID
        
    Returns:
        str: 格式化后的患者ID
    """
    if not patient_id:
        return ""
    
    # 转换为大写并移除多余空格
    return clean_string(patient_id).upper()

def format_room_number(room_number: str) -> str:
    """
    格式化手术间号
    
    Args:
        room_number: 手术间号
        
    Returns:
        str: 格式化后的手术间号
    """
    if not room_number:
        return ""
    
    text = clean_string(room_number)
    
    # 如果只是数字，添加"号间"后缀
    if text.isdigit():
        return f"{text}号间"
    
    return text

def format_status_text(status: str) -> str:
    """
    格式化状态文本
    
    Args:
        status: 状态值
        
    Returns:
        str: 格式化后的状态文本
    """
    status_mapping = {
        'scheduled': '已安排',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
    }
    
    return status_mapping.get(status.lower(), status)

def truncate_text(text: str, max_length: int = 50, suffix: str = "...") -> str:
    """
    截断文本
    
    Args:
        text: 原始文本
        max_length: 最大长度
        suffix: 截断后缀
        
    Returns:
        str: 截断后的文本
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def format_error_message(error: Exception) -> str:
    """
    格式化错误信息
    
    Args:
        error: 异常对象
        
    Returns:
        str: 格式化后的错误信息
    """
    error_type = type(error).__name__
    error_message = str(error)
    
    # 常见错误的友好提示
    friendly_messages = {
        'FileNotFoundError': '文件不存在',
        'PermissionError': '文件权限不足',
        'ValueError': '数据格式错误',
        'KeyError': '缺少必需字段',
        'TypeError': '数据类型错误'
    }
    
    if error_type in friendly_messages:
        return f"{friendly_messages[error_type]}: {error_message}"
    
    return f"{error_type}: {error_message}"

def normalize_column_name(column_name: str) -> str:
    """
    标准化列名
    
    Args:
        column_name: 原始列名
        
    Returns:
        str: 标准化后的列名
    """
    if not column_name:
        return ""
    
    # 清理字符串
    text = clean_string(column_name)
    
    # 移除常见的前缀和后缀
    text = re.sub(r'^(第|序号|编号)', '', text)
    text = re.sub(r'(号|名称|信息)$', '', text)
    
    # 移除括号内容
    text = re.sub(r'\([^)]*\)', '', text)
    text = re.sub(r'\[[^\]]*\]', '', text)
    
    return text.strip()
