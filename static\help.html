<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作指南 - 麻醉科交班大屏</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 20px;
            padding: 40px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            font-size: 48px;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border-left: 5px solid #FFD700;
        }
        .section h2 {
            font-size: 32px;
            margin-bottom: 20px;
            color: #FFD700;
        }
        .section h3 {
            font-size: 24px;
            margin: 20px 0 10px 0;
            color: #87CEEB;
        }
        .key-combo {
            display: inline-block;
            background: #333;
            color: #FFD700;
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: bold;
            margin: 5px;
            border: 2px solid #555;
            font-family: monospace;
            font-size: 18px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            font-size: 18px;
            line-height: 1.6;
        }
        .feature-list li::before {
            content: "✓";
            color: #00FF7F;
            font-weight: bold;
            margin-right: 10px;
            font-size: 20px;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-left-color: #FFC107;
            border: 2px solid rgba(255, 193, 7, 0.5);
        }
        .info {
            background: rgba(30, 136, 229, 0.2);
            border-left-color: #1E88E5;
            border: 2px solid rgba(30, 136, 229, 0.5);
        }
        .success {
            background: rgba(67, 160, 71, 0.2);
            border-left-color: #43A047;
            border: 2px solid rgba(67, 160, 71, 0.5);
        }
        .back-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        .back-button:hover {
            background: #0056b3;
        }
        .page-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .page-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .page-number {
            font-size: 36px;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 10px;
        }
        .page-title {
            font-size: 20px;
            margin-bottom: 10px;
            color: #87CEEB;
        }
        .page-desc {
            font-size: 16px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <a href="/" class="back-button">返回大屏</a>
    
    <div class="container">
        <h1>🏥 麻醉科交班大屏操作指南</h1>
        
        <div class="section info">
            <h2>🎯 系统概述</h2>
            <p style="font-size: 20px; line-height: 1.6;">
                麻醉科交班大屏系统专为医院麻醉科设计，提供清晰的手术信息展示和交班数据汇总。
                系统采用1920x1080固定布局，支持自定义背景图片，确保在各种显示设备上都能完美呈现。
            </p>
        </div>

        <div class="section success">
            <h2>⌨️ 键盘控制</h2>
            <h3>基本操作</h3>
            <p>
                <span class="key-combo">← →</span> 切换页面（左右箭头键）<br>
                <span class="key-combo">P</span> 开启/关闭自动播放<br>
                <span class="key-combo">R</span> 刷新数据<br>
                <span class="key-combo">H</span> 显示控制台帮助信息
            </p>
            <h3>操作说明</h3>
            <ul class="feature-list">
                <li>系统默认关闭自动播放，需要手动切换页面</li>
                <li>按P键可以开启10秒自动切换模式</li>
                <li>按R键可以重新加载最新的手术数据</li>
                <li>按H键在浏览器控制台显示详细帮助</li>
            </ul>
        </div>

        <div class="section">
            <h2>📊 页面内容</h2>
            <div class="page-grid">
                <div class="page-card">
                    <div class="page-number">1</div>
                    <div class="page-title">封面页</div>
                    <div class="page-desc">显示值班医生、护士信息和今日手术基础统计</div>
                </div>
                <div class="page-card">
                    <div class="page-number">2</div>
                    <div class="page-title">昨日统计</div>
                    <div class="page-desc">昨日手术统计数据和麻醉方式分布</div>
                </div>
                <div class="page-card">
                    <div class="page-number">3</div>
                    <div class="page-title">今日统计</div>
                    <div class="page-desc">今日手术统计数据和科室分布情况</div>
                </div>
                <div class="page-card">
                    <div class="page-number">4+</div>
                    <div class="page-title">特殊病例</div>
                    <div class="page-desc">需要特别关注的患者信息和注意事项</div>
                </div>
                <div class="page-card">
                    <div class="page-number">末页</div>
                    <div class="page-title">通知事项</div>
                    <div class="page-desc">重要通知和注意事项</div>
                </div>
            </div>
        </div>

        <div class="section warning">
            <h2>🖼️ 背景图片配置</h2>
            <h3>图片要求</h3>
            <ul class="feature-list">
                <li>分辨率：1920x1080像素</li>
                <li>格式：PNG、JPG或WebP</li>
                <li>文件大小：建议小于5MB</li>
                <li>位置：static/images/background.png</li>
            </ul>
            <h3>设计建议</h3>
            <ul class="feature-list">
                <li>避免在中央1800x800区域放置重要图案</li>
                <li>确保与白色文字有足够对比度</li>
                <li>使用医疗相关的专业色调</li>
                <li>保持简洁，不要过于复杂</li>
            </ul>
        </div>

        <div class="section info">
            <h2>🔧 技术特性</h2>
            <ul class="feature-list">
                <li>固定1920x1080设计，等比例缩放适配各种屏幕</li>
                <li>内容区域1800x800，确保重要信息不被遮挡</li>
                <li>实时数据更新，支持手动刷新</li>
                <li>响应式设计，支持不同设备访问</li>
                <li>键盘快捷键操作，提高使用效率</li>
            </ul>
        </div>

        <div class="section success">
            <h2>💡 使用建议</h2>
            <ul class="feature-list">
                <li>建议在大屏显示器上全屏显示以获得最佳效果</li>
                <li>根据交班需要决定是否开启自动播放</li>
                <li>定期按R键刷新数据确保信息最新</li>
                <li>可以按F12打开开发者工具查看详细日志</li>
                <li>如遇问题可以刷新页面重新加载</li>
            </ul>
        </div>

        <div class="section warning">
            <h2>⚠️ 注意事项</h2>
            <ul class="feature-list">
                <li>请确保网络连接稳定以获取最新数据</li>
                <li>建议使用Chrome或Edge浏览器以获得最佳兼容性</li>
                <li>如果背景图片不显示，请检查文件路径和格式</li>
                <li>系统会自动处理API错误并显示模拟数据</li>
                <li>特殊病例信息仅供参考，请以实际医嘱为准</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: rgba(255, 255, 255, 0.1); border-radius: 10px;">
            <h3 style="color: #FFD700;">🏥 麻醉科交班大屏系统 v1.0</h3>
            <p style="opacity: 0.8;">专为医院麻醉科设计的专业信息展示系统</p>
        </div>
    </div>
</body>
</html>
