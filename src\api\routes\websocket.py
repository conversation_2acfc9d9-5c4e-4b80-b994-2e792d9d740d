"""
WebSocket路由
"""
import logging
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query
from typing import Optional

from src.services.websocket_manager import manager

logger = logging.getLogger(__name__)

router = APIRouter()

@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    client_id: Optional[str] = Query(None),
    client_type: Optional[str] = Query("dashboard")
):
    """
    WebSocket连接端点
    
    Args:
        websocket: WebSocket连接对象
        client_id: 客户端ID（可选）
        client_type: 客户端类型（dashboard, admin等）
    """
    client_info = {
        "client_id": client_id,
        "client_type": client_type,
        "user_agent": websocket.headers.get("user-agent", ""),
        "origin": websocket.headers.get("origin", "")
    }
    
    await manager.connect(websocket, client_info)
    
    try:
        # 发送欢迎消息
        welcome_message = {
            "type": "welcome",
            "message": "WebSocket连接已建立",
            "client_info": client_info,
            "server_info": {
                "version": "1.0.0",
                "features": ["real_time_updates", "notifications", "heartbeat"]
            }
        }
        
        import json
        await manager.send_personal_message(
            json.dumps(welcome_message, ensure_ascii=False),
            websocket
        )
        
        # 监听客户端消息
        while True:
            try:
                # 等待客户端消息
                message = await websocket.receive_text()
                
                # 处理客户端消息
                await manager.handle_client_message(websocket, message)
                
            except WebSocketDisconnect:
                logger.info("客户端主动断开WebSocket连接")
                break
            except Exception as e:
                logger.error(f"处理WebSocket消息时发生错误: {e}")
                # 发送错误消息给客户端
                error_message = {
                    "type": "error",
                    "message": "服务器处理消息时发生错误",
                    "error_code": "MESSAGE_PROCESSING_ERROR"
                }
                try:
                    await manager.send_personal_message(
                        json.dumps(error_message, ensure_ascii=False),
                        websocket
                    )
                except:
                    # 如果发送错误消息也失败，则断开连接
                    break
                
    except WebSocketDisconnect:
        logger.info("WebSocket连接断开")
    except Exception as e:
        logger.error(f"WebSocket连接发生未预期错误: {e}")
    finally:
        # 清理连接
        manager.disconnect(websocket)

@router.get("/ws/status")
async def websocket_status():
    """获取WebSocket连接状态"""
    try:
        connection_info = manager.get_connection_info()
        
        return {
            "status": "active",
            "connection_count": connection_info["total_connections"],
            "connections": connection_info["connections"],
            "heartbeat_interval": manager.heartbeat_interval
        }
        
    except Exception as e:
        logger.error(f"获取WebSocket状态失败: {e}")
        return {
            "status": "error",
            "error": str(e),
            "connection_count": 0
        }

@router.post("/ws/broadcast")
async def broadcast_message(message: dict):
    """
    广播消息到所有连接的客户端
    
    Args:
        message: 要广播的消息
    """
    try:
        await manager.broadcast_json(message)
        
        return {
            "success": True,
            "message": "消息广播成功",
            "recipient_count": manager.get_connection_count()
        }
        
    except Exception as e:
        logger.error(f"广播消息失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "recipient_count": 0
        }

@router.post("/ws/notify")
async def send_notification(
    notification_type: str,
    message: str,
    level: str = "info"
):
    """
    发送通知消息
    
    Args:
        notification_type: 通知类型
        message: 通知消息
        level: 通知级别
    """
    try:
        await manager.send_notification(notification_type, message, level)
        
        return {
            "success": True,
            "message": "通知发送成功",
            "recipient_count": manager.get_connection_count()
        }
        
    except Exception as e:
        logger.error(f"发送通知失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "recipient_count": 0
        }
