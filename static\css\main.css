/* 麻醉科交班大屏软件主样式 */
/* 注意：CSS变量现在统一在 theme-variables.css 中定义 */
    
    /* 阴影 */
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
}

/* 容器布局 */
.container {
    max-width: 1920px;
    margin: 0 auto;
    padding: var(--spacing-md);
}

.container-fluid {
    width: 100%;
    padding: var(--spacing-md);
}

/* 栅格系统 */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -var(--spacing-sm);
}

.col {
    flex: 1;
    padding: 0 var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* 卡片组件 */
.card {
    background: var(--surface-color);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-md);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--divider-color);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.card-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin: var(--spacing-xs) 0 0 0;
}

.card-body {
    padding: var(--spacing-md);
}

.card-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--divider-color);
    background-color: #fafafa;
    font-size: 12px;
    color: var(--text-secondary);
}

/* 按钮组件 */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    line-height: 1.5;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-danger {
    background-color: var(--error-color);
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 表格组件 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: var(--spacing-md);
}

.table th,
.table td {
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid var(--divider-color);
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--text-primary);
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table-striped tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.status-scheduled {
    background-color: #e3f2fd;
    color: var(--primary-color);
}

.status-in_progress {
    background-color: #fff3e0;
    color: var(--warning-color);
}

.status-completed {
    background-color: #e8f5e8;
    color: var(--success-color);
}

.status-cancelled {
    background-color: #ffebee;
    color: var(--error-color);
}

/* 统计卡片 */
.stat-card {
    text-align: center;
    padding: var(--spacing-lg);
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.stat-change {
    font-size: 12px;
    font-weight: 500;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

/* 加载状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--divider-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-sm);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error {
    background-color: #ffebee;
    color: var(--error-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--error-color);
    margin: var(--spacing-md) 0;
}

.success {
    background-color: #e8f5e8;
    color: var(--success-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--success-color);
    margin: var(--spacing-md) 0;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }
