<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ hospital_name }} - 数据管理</title>

    <!-- 统一主题变量系统 -->
    <link rel="stylesheet" href="/static/css/theme-variables.css">

    <style>
        /* 使用统一主题变量 */
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: var(--spacing-lg);
            background: var(--theme-background);
            color: var(--theme-text-primary);
        }
        .header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            padding: var(--spacing-lg);
            background: var(--theme-glass);
            color: var(--theme-text-light);
            border-radius: var(--border-radius-md);
            backdrop-filter: blur(20px);
            border: 1px solid var(--theme-border);
        }
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .upload-section {
            background: var(--theme-card-bg);
            padding: var(--spacing-xl);
            border-radius: var(--border-radius-md);
            box-shadow: var(--theme-shadow);
            margin-bottom: var(--spacing-xl);
            border: 1px solid var(--theme-border);
        }
        .upload-area {
            border: 2px dashed var(--theme-border);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-2xl);
            text-align: center;
            margin: var(--spacing-lg) 0;
            transition: var(--transition-base);
        }
        .upload-area:hover {
            border-color: var(--theme-primary);
        }
        .upload-area.dragover {
            border-color: var(--theme-primary);
            background-color: var(--theme-content-bg);
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background-color: var(--theme-primary);
            color: var(--theme-text-light);
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            font-size: var(--font-size-base);
            margin: var(--spacing-sm);
            transition: var(--transition-fast);
        }
        .upload-btn:hover {
            background-color: var(--theme-secondary);
        }
        .upload-btn:disabled {
            background-color: var(--theme-text-secondary);
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: var(--theme-content-bg);
            border-radius: var(--border-radius-md);
            overflow: hidden;
            margin: var(--spacing-lg) 0;
            display: none;
            border: 1px solid var(--theme-border);
        }
        .progress-bar {
            height: 100%;
            background-color: var(--theme-primary);
            width: 0%;
            transition: var(--transition-base);
        }
        .message {
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            margin: var(--spacing-sm) 0;
            display: none;
        }
        .message.success {
            background-color: var(--theme-success);
            color: var(--theme-text-light);
            border-left: 4px solid var(--theme-success);
            opacity: 0.9;
        }
        .message.error {
            background-color: var(--theme-error);
            color: var(--theme-text-light);
            border-left: 4px solid var(--theme-error);
            opacity: 0.9;
        }
        .nav-links {
            text-align: center;
            margin-top: 30px;
        }
        .nav-links a {
            color: var(--theme-primary);
            text-decoration: none;
            margin: 0 var(--spacing-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            border: 1px solid var(--theme-primary);
            border-radius: var(--border-radius-sm);
            transition: var(--transition-base);
        }
        .nav-links a:hover {
            background-color: var(--theme-primary);
            color: var(--theme-text-light);
        }
    </style>

    <!-- 主题系统 -->
    <script src="/static/js/components/ui/theme-system.js"></script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ hospital_name }} - 数据管理</h1>
            <p>{{ department_name }}手术数据上传</p>
        </div>
        
        <div class="upload-section">
            <h2>Excel文件上传</h2>
            <p>支持上传 .xlsx 和 .xls 格式的Excel文件，文件大小不超过10MB</p>
            
            <div class="upload-area" id="uploadArea">
                <p>拖拽Excel文件到此处，或点击选择文件</p>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
                <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls">
            </div>
            
            <div class="progress" id="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div class="message" id="message"></div>
            
            <button class="upload-btn" id="uploadBtn" onclick="uploadFile()" disabled>
                上传文件
            </button>
        </div>
        
        <div class="nav-links">
            <a href="/">返回主页</a>
            <a href="/api/upload/records" target="_blank">查看上传记录</a>
            <a href="/api/surgeries/today" target="_blank">今日手术</a>
        </div>
    </div>

    <script>
        let selectedFile = null;

        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });

        // 拖拽处理
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFileSelect(e.dataTransfer.files[0]);
        });

        function handleFileSelect(file) {
            if (!file) return;
            
            // 验证文件类型
            const allowedTypes = ['.xlsx', '.xls'];
            const fileExt = '.' + file.name.split('.').pop().toLowerCase();
            
            if (!allowedTypes.includes(fileExt)) {
                showMessage('error', '不支持的文件格式，请选择 .xlsx 或 .xls 文件');
                return;
            }
            
            // 验证文件大小 (10MB)
            if (file.size > 10 * 1024 * 1024) {
                showMessage('error', '文件大小超过10MB限制');
                return;
            }
            
            selectedFile = file;
            document.getElementById('uploadBtn').disabled = false;
            
            uploadArea.innerHTML = `
                <p>已选择文件: ${file.name}</p>
                <p>文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    重新选择
                </button>
            `;
            
            hideMessage();
        }

        async function uploadFile() {
            if (!selectedFile) {
                showMessage('error', '请先选择文件');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', selectedFile);
            
            const uploadBtn = document.getElementById('uploadBtn');
            const progress = document.getElementById('progress');
            const progressBar = document.getElementById('progressBar');
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            progress.style.display = 'block';
            
            try {
                const response = await fetch('/api/upload/excel', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showMessage('success', `文件上传成功！文件名: ${result.filename}`);
                    resetUploadArea();
                } else {
                    showMessage('error', result.detail || '上传失败');
                }
            } catch (error) {
                showMessage('error', '上传失败: ' + error.message);
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传文件';
                progress.style.display = 'none';
                progressBar.style.width = '0%';
            }
        }

        function showMessage(type, text) {
            const message = document.getElementById('message');
            message.className = `message ${type}`;
            message.textContent = text;
            message.style.display = 'block';
        }

        function hideMessage() {
            document.getElementById('message').style.display = 'none';
        }

        function resetUploadArea() {
            selectedFile = null;
            document.getElementById('uploadBtn').disabled = true;
            document.getElementById('fileInput').value = '';
            
            uploadArea.innerHTML = `
                <p>拖拽Excel文件到此处，或点击选择文件</p>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
            `;
        }
    </script>
</body>
</html>
