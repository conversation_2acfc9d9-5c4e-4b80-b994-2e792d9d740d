/**
 * 背景布局页面管理器
 */
class BackgroundPageManager {
    constructor() {
        this.currentPageIndex = 0;
        this.pages = [];
        this.autoPlayInterval = null;
        this.autoPlayDelay = 10000;
        this.isAutoPlaying = false; // 默认关闭自动播放

        // 基础数据
        this.dutyDoctor = '张医生';
        this.dutyNurse1 = '李护士';
        this.dutyNurse2 = '王护士';

        // 初始化数据为安全的默认值
        this.todayStats = { total_surgeries: 0 };
        this.yesterdayStats = { total_surgeries: 0 };
        this.todaySurgeries = [];
        this.specialCases = [];

        this.init();
    }
    
    async init() {
        console.log('初始化背景页面管理器...');
        
        try {
            await this.loadData();
            this.generatePages();
            this.showPage(0);
            // 移除自动启动自动播放
            // this.startAutoPlay();
            this.bindKeyboardEvents();
            this.startTimeUpdate();

            console.log('背景页面管理器初始化完成（自动播放已禁用）');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showErrorPage();
        }
    }
    
    async loadData() {
        try {
            // 尝试加载今日统计
            try {
                const todayResponse = await fetch('/api/stats/today');
                if (todayResponse.ok) {
                    this.todayStats = await todayResponse.json();
                } else {
                    throw new Error('今日统计API不可用');
                }
            } catch (e) {
                console.log('使用模拟今日统计数据');
                this.todayStats = { total_surgeries: 3 };
            }

            // 尝试加载昨日统计
            try {
                const yesterdayResponse = await fetch('/api/stats/yesterday');
                if (yesterdayResponse.ok) {
                    this.yesterdayStats = await yesterdayResponse.json();
                } else {
                    throw new Error('昨日统计API不可用');
                }
            } catch (e) {
                console.log('使用模拟昨日统计数据');
                this.yesterdayStats = { total_surgeries: 5 };
            }

            // 尝试加载今日手术列表
            try {
                const surgeriesResponse = await fetch('/api/surgeries/today');
                if (surgeriesResponse.ok) {
                    const surgeriesData = await surgeriesResponse.json();
                    this.todaySurgeries = Array.isArray(surgeriesData) ? surgeriesData : [];
                } else {
                    throw new Error('今日手术API不可用');
                }
            } catch (e) {
                console.log('使用模拟今日手术数据');
                this.todaySurgeries = [
                    { id: 1, patient_name: '张三', is_emergency: true, department: '普外科' },
                    { id: 2, patient_name: '李四', is_emergency: false, department: '骨科' },
                    { id: 3, patient_name: '王五', is_emergency: false, department: '心外科' }
                ];
            }

            // 尝试加载特殊病例
            try {
                const specialResponse = await fetch('/api/surgeries/special');
                if (specialResponse.ok) {
                    const specialData = await specialResponse.json();
                    this.specialCases = Array.isArray(specialData) ? specialData : [];
                } else {
                    throw new Error('特殊病例API不可用');
                }
            } catch (e) {
                console.log('使用模拟特殊病例数据');
                this.specialCases = [
                    {
                        id: 1,
                        patient_name: '赵六',
                        surgery_name: '心脏搭桥手术',
                        department: '心外科',
                        anesthesia_type: '全身麻醉',
                        is_emergency: true,
                        allergies: '青霉素过敏',
                        special_notes: '高血压，需密切监测'
                    }
                ];
            }

            console.log('数据加载完成', {
                todayStats: this.todayStats,
                todaySurgeries: this.todaySurgeries.length,
                specialCases: this.specialCases.length
            });
        } catch (error) {
            console.error('数据加载完全失败:', error);
            // 最后的备用数据
            this.todayStats = { total_surgeries: 3 };
            this.yesterdayStats = { total_surgeries: 5 };
            this.todaySurgeries = [];
            this.specialCases = [];
        }
    }
    
    generatePages() {
        this.pages = [
            {
                id: 'cover',
                title: '交班大屏',
                generator: () => this.generateCoverPage()
            },
            {
                id: 'yesterday-stats',
                title: '昨日统计',
                generator: () => this.generateYesterdayStatsPage()
            },
            {
                id: 'today-stats',
                title: '今日统计',
                generator: () => this.generateTodayStatsPage()
            }
        ];
        
        // 添加特殊病例页面
        if (this.specialCases && this.specialCases.length > 0) {
            this.specialCases.forEach((caseData, index) => {
                this.pages.push({
                    id: `special-case-${index}`,
                    title: `特殊病例 ${index + 1}`,
                    generator: () => this.generatePatientPage(caseData)
                });
            });
        }
        
        // 添加通知页面
        this.pages.push({
            id: 'notices',
            title: '通知事项',
            generator: () => this.generateNoticesPage()
        });
        
        console.log(`生成了 ${this.pages.length} 个页面`);
    }
    
    generateCoverPage() {
        const currentDate = new Date();
        const dateStr = currentDate.toLocaleDateString('zh-CN');
        const timeStr = currentDate.toLocaleTimeString('zh-CN');
        const emergencyCount = this.getEmergencyCount();

        return `
            <div class="page-container">
                <!-- 标题栏 -->
                <div class="header-bar">
                    <div class="header-left">
                        <div class="header-logo">麻</div>
                        <div class="header-title">
                            <h1>麻醉科交班大屏</h1>
                            <div class="header-subtitle">Anesthesia Handover Display</div>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="header-time" id="headerTime">
                            <span class="datetime-display">${dateStr} ${timeStr}</span>
                        </div>
                    </div>
                </div>

                <!-- 内容区 -->
                <div class="content-area">
                    <div class="main-content">
                        <div class="page-title">
                            <h2>今日交班概览</h2>
                            <div class="page-subtitle">Daily Handover Overview</div>
                        </div>

                        <div class="duty-section">
                            <div class="duty-item">
                                <div class="duty-content">
                                    <div class="duty-title">
                                        <div class="duty-icon">${window.medicalIcons?.getIcon('doctor', 20, '#2d3748') || ''}</div>
                                        值班医生
                                    </div>
                                    <div class="duty-name">${this.dutyDoctor}</div>
                                </div>
                            </div>
                            <div class="duty-item">
                                <div class="duty-content">
                                    <div class="duty-title">
                                        <div class="duty-icon">${window.medicalIcons?.getIcon('nurse', 20, '#2d3748') || ''}</div>
                                        值班护士
                                    </div>
                                    <div class="duty-name">${this.dutyNurse1}</div>
                                </div>
                            </div>
                            <div class="duty-item">
                                <div class="duty-content">
                                    <div class="duty-title">
                                        <div class="duty-icon">${window.medicalIcons?.getIcon('nurse', 20, '#2d3748') || ''}</div>
                                        值班护士
                                    </div>
                                    <div class="duty-name">${this.dutyNurse2}</div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card primary">
                                <div class="stat-content">
                                    <div class="stat-title">
                                        <div class="stat-icon">${window.medicalIcons?.getIcon('surgery', 24, '#ffffff') || ''}</div>
                                        总手术量
                                    </div>
                                    <div class="stat-number">${this.todayStats?.total_surgeries || 0}</div>
                                </div>
                            </div>
                            <div class="stat-card warning">
                                <div class="stat-content">
                                    <div class="stat-title">
                                        <div class="stat-icon">${window.medicalIcons?.getIcon('emergency', 24, '#ffffff') || ''}</div>
                                        急诊手术
                                    </div>
                                    <div class="stat-number">${emergencyCount}</div>
                                </div>
                            </div>
                            <div class="stat-card success">
                                <div class="stat-content">
                                    <div class="stat-title">
                                        <div class="stat-icon">${window.medicalIcons?.getIcon('clock', 24, '#ffffff') || ''}</div>
                                        择期手术
                                    </div>
                                    <div class="stat-number">${(this.todayStats?.total_surgeries || 0) - emergencyCount}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <div class="status-indicator">
                            <div class="status-dot"></div>
                            <div class="status-text">系统运行正常</div>
                        </div>
                    </div>
                    <div class="status-right">
                        <div class="page-nav">
                            <div class="page-number">1 / ${this.pages.length}</div>
                            <div class="nav-hint">← → 切换页面</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    generateYesterdayStatsPage() {
        const anesthesiaStats = this.getYesterdayAnesthesiaStats();
        const emergencyCount = this.getYesterdayEmergencyCount();
        const currentDate = new Date();
        const dateStr = currentDate.toLocaleDateString('zh-CN');
        const timeStr = currentDate.toLocaleTimeString('zh-CN');

        return `
            <div class="page-container">
                <!-- 标题栏 -->
                <div class="header-bar">
                    <div class="header-left">
                        <div class="header-logo">麻</div>
                        <div class="header-title">
                            <h1>麻醉科交班大屏</h1>
                            <div class="header-subtitle">Anesthesia Handover Display</div>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="header-time" id="headerTime">
                            <span class="datetime-display">${dateStr} ${timeStr}</span>
                        </div>
                    </div>
                </div>

                <!-- 内容区 -->
                <div class="content-area">
                    <div class="main-content">
                        <div class="page-title">
                            <h2>昨日手术统计</h2>
                            <div class="page-subtitle">Yesterday Surgery Statistics</div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card primary">
                                <div class="stat-number">${this.yesterdayStats?.total_surgeries || 0}</div>
                                <div class="stat-label">总手术量</div>
                            </div>
                            <div class="stat-card warning">
                                <div class="stat-number">${emergencyCount}</div>
                                <div class="stat-label">急诊手术</div>
                            </div>
                            <div class="stat-card success">
                                <div class="stat-number">${(this.yesterdayStats?.total_surgeries || 0) - emergencyCount}</div>
                                <div class="stat-label">择期手术</div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <div class="detail-left">
                                <div class="detail-title">麻醉方式统计</div>
                                <div class="detail-items">
                                    ${Object.entries(anesthesiaStats).map(([type, count]) => `
                                        <div class="detail-item">
                                            <span class="detail-name">${type}</span>
                                            <span class="detail-count">${count}例</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="detail-right">
                                <canvas id="yesterdayChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <div class="status-indicator">
                            <div class="status-dot"></div>
                            <div class="status-text">数据已更新</div>
                        </div>
                    </div>
                    <div class="status-right">
                        <div class="page-nav">
                            <div class="page-number">2 / ${this.pages.length}</div>
                            <div class="nav-hint">← → 切换页面</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    generateTodayStatsPage() {
        const departmentStats = this.getTodayDepartmentStats();
        const emergencyCount = this.getEmergencyCount();
        const currentDate = new Date();
        const dateStr = currentDate.toLocaleDateString('zh-CN');
        const timeStr = currentDate.toLocaleTimeString('zh-CN');

        return `
            <div class="page-container">
                <!-- 标题栏 -->
                <div class="header-bar">
                    <div class="header-left">
                        <div class="header-logo">麻</div>
                        <div class="header-title">
                            <h1>麻醉科交班大屏</h1>
                            <div class="header-subtitle">Anesthesia Handover Display</div>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="header-time" id="headerTime">
                            <span class="datetime-display">${dateStr} ${timeStr}</span>
                        </div>
                    </div>
                </div>

                <!-- 内容区 -->
                <div class="content-area">
                    <div class="main-content">
                        <div class="page-title">
                            <h2>今日手术统计</h2>
                            <div class="page-subtitle">Today Surgery Statistics</div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card primary">
                                <div class="stat-number">${this.todayStats?.total_surgeries || 0}</div>
                                <div class="stat-label">总手术量</div>
                            </div>
                            <div class="stat-card warning">
                                <div class="stat-number">${emergencyCount}</div>
                                <div class="stat-label">急诊手术</div>
                            </div>
                            <div class="stat-card success">
                                <div class="stat-number">${(this.todayStats?.total_surgeries || 0) - emergencyCount}</div>
                                <div class="stat-label">择期手术</div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <div class="detail-left">
                                <div class="detail-title">科室分布</div>
                                <div class="detail-items">
                                    ${Object.entries(departmentStats).map(([dept, count]) => `
                                        <div class="detail-item">
                                            <span class="detail-name">${dept}</span>
                                            <span class="detail-count">${count}例</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="detail-right">
                                <canvas id="todayChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <div class="status-indicator">
                            <div class="status-dot"></div>
                            <div class="status-text">实时数据</div>
                        </div>
                    </div>
                    <div class="status-right">
                        <div class="page-nav">
                            <div class="page-number">3 / ${this.pages.length}</div>
                            <div class="nav-hint">← → 切换页面</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    generatePatientPage(patient) {
        const pageIndex = this.currentPageIndex + 1;
        const currentDate = new Date();
        const dateStr = currentDate.toLocaleDateString('zh-CN');
        const timeStr = currentDate.toLocaleTimeString('zh-CN');

        return `
            <div class="page-container">
                <!-- 标题栏 -->
                <div class="header-bar">
                    <div class="header-left">
                        <div class="header-logo">麻</div>
                        <div class="header-title">
                            <h1>麻醉科交班大屏</h1>
                            <div class="header-subtitle">Anesthesia Handover Display</div>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="header-time" id="headerTime">
                            <span class="datetime-display">${dateStr} ${timeStr}</span>
                        </div>
                    </div>
                </div>

                <!-- 内容区 -->
                <div class="content-area">
                    <div class="main-content">
                        <div class="page-title">
                            <h2>特殊病例</h2>
                            <div class="page-subtitle">Special Case Information</div>
                        </div>

                        <div class="patient-info">
                            <div class="patient-header">
                                <h3>${patient.patient_name || '患者姓名'}</h3>
                                ${patient.is_emergency ? '<div class="emergency-badge">急诊</div>' : ''}
                            </div>
                            <div class="patient-details">
                                <div class="patient-row">
                                    <span class="patient-label">手术名称</span>
                                    <span class="patient-value">${patient.surgery_name || '未知'}</span>
                                </div>
                                <div class="patient-row">
                                    <span class="patient-label">科室</span>
                                    <span class="patient-value">${patient.department || '未知'}</span>
                                </div>
                                <div class="patient-row">
                                    <span class="patient-label">麻醉方式</span>
                                    <span class="patient-value">${patient.anesthesia_type || '未知'}</span>
                                </div>
                                ${patient.allergies ? `
                                    <div class="patient-row highlight">
                                        <span class="patient-label">过敏史</span>
                                        <span class="patient-value allergy">${patient.allergies}</span>
                                    </div>
                                ` : ''}
                                ${patient.special_notes ? `
                                    <div class="patient-row highlight">
                                        <span class="patient-label">特殊注意</span>
                                        <span class="patient-value special">${patient.special_notes}</span>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <div class="status-indicator">
                            <div class="status-dot"></div>
                            <div class="status-text">特殊关注</div>
                        </div>
                    </div>
                    <div class="status-right">
                        <div class="page-nav">
                            <div class="page-number">${pageIndex} / ${this.pages.length}</div>
                            <div class="nav-hint">← → 切换页面</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    generateNoticesPage() {
        const notices = [
            { title: '重要提醒', content: '请各位医护人员注意手术安全，严格执行三查七对制度。' },
            { title: '设备维护', content: '麻醉机定期维护已完成，设备运行正常。' },
            { title: '培训通知', content: '下周三下午2点进行急救技能培训，请准时参加。' }
        ];

        const currentDate = new Date();
        const dateStr = currentDate.toLocaleDateString('zh-CN');
        const timeStr = currentDate.toLocaleTimeString('zh-CN');

        return `
            <div class="page-container">
                <!-- 标题栏 -->
                <div class="header-bar">
                    <div class="header-left">
                        <div class="header-logo">麻</div>
                        <div class="header-title">
                            <h1>麻醉科交班大屏</h1>
                            <div class="header-subtitle">Anesthesia Handover Display</div>
                        </div>
                    </div>
                    <div class="header-right">
                        <div class="header-time" id="headerTime">
                            <span class="datetime-display">${dateStr} ${timeStr}</span>
                        </div>
                    </div>
                </div>

                <!-- 内容区 -->
                <div class="content-area">
                    <div class="main-content">
                        <div class="page-title">
                            <h2>通知事项</h2>
                            <div class="page-subtitle">Important Notices</div>
                        </div>

                        <div class="notices-list">
                            ${notices.map(notice => `
                                <div class="notice-item">
                                    <div class="notice-title">${notice.title}</div>
                                    <div class="notice-content">${notice.content}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>

                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="status-left">
                        <div class="status-indicator">
                            <div class="status-dot"></div>
                            <div class="status-text">信息完整</div>
                        </div>
                    </div>
                    <div class="status-right">
                        <div class="page-nav">
                            <div class="page-number">${this.pages.length} / ${this.pages.length}</div>
                            <div class="nav-hint">← → 切换页面</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 辅助方法
    getEmergencyCount() {
        if (!this.todaySurgeries || !Array.isArray(this.todaySurgeries)) {
            console.log('todaySurgeries不是数组:', this.todaySurgeries);
            return 0;
        }
        return this.todaySurgeries.filter(s => s && s.is_emergency).length;
    }
    
    getYesterdayEmergencyCount() {
        return 1;
    }
    
    getYesterdayAnesthesiaStats() {
        return { '全身麻醉': 3, '局部麻醉': 1, '椎管内麻醉': 1 };
    }
    
    getTodayDepartmentStats() {
        return { '普外科': 1, '骨科': 1, '心外科': 1 };
    }
    
    // 页面控制方法
    showPage(index) {
        if (index < 0 || index >= this.pages.length) return;
        
        this.currentPageIndex = index;
        const page = this.pages[index];
        const container = document.getElementById('pageContainer');
        
        if (container && page) {
            // 检查是否在缩放包装器中
            const isInWrapper = container.parentElement && container.parentElement.classList.contains('scale-wrapper');

            if (isInWrapper) {
                // 如果在包装器中，直接更新内容
                container.innerHTML = page.generator();
            } else {
                // 如果不在包装器中，设置内容并重新初始化缩放
                container.innerHTML = page.generator();

                // 重新初始化缩放管理器
                if (window.pptScaleManager) {
                    window.pptScaleManager.resetContainer();
                } else if (window.resolutionScaleManager) {
                    window.resolutionScaleManager.resetContainer();
                } else if (window.modernScaleManager) {
                    window.modernScaleManager.setupContainer();
                } else if (window.backgroundScaleManager) {
                    window.backgroundScaleManager.setupContainer();
                }
            }

            this.updatePageIndicator();

            // 强制重新应用缩放
            setTimeout(() => {
                if (window.pptScaleManager) {
                    window.pptScaleManager.forceUpdate();
                } else if (window.resolutionScaleManager) {
                    window.resolutionScaleManager.forceUpdate();
                } else if (window.modernScaleManager) {
                    window.modernScaleManager.forceUpdate();
                } else if (window.backgroundScaleManager) {
                    window.backgroundScaleManager.forceUpdate();
                }
                this.initCharts();
            }, 100);
        }
    }
    
    nextPage() {
        const nextIndex = (this.currentPageIndex + 1) % this.pages.length;
        this.showPage(nextIndex);
    }
    
    prevPage() {
        const prevIndex = (this.currentPageIndex - 1 + this.pages.length) % this.pages.length;
        this.showPage(prevIndex);
    }
    
    startAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
        }
        
        this.autoPlayInterval = setInterval(() => {
            if (this.isAutoPlaying) {
                this.nextPage();
            }
        }, this.autoPlayDelay);
    }
    
    toggleAutoPlay() {
        this.isAutoPlaying = !this.isAutoPlaying;
        console.log('自动播放:', this.isAutoPlaying ? '开启' : '暂停');
    }
    
    updatePageIndicator() {
        const indicator = document.getElementById('pageIndicator');
        if (indicator) {
            indicator.textContent = `${this.currentPageIndex + 1} / ${this.pages.length}`;
        }
    }
    
    initCharts() {
        const canvas = document.querySelector('canvas');
        if (canvas && window.Chart) {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['全身麻醉', '局部麻醉', '椎管内麻醉'],
                    datasets: [{
                        data: [3, 1, 1],
                        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { color: 'white', font: { size: 16 } }
                        }
                    }
                }
            });
        }
    }
    
    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowLeft':
                    this.prevPage();
                    console.log('切换到上一页');
                    break;
                case 'ArrowRight':
                    this.nextPage();
                    console.log('切换到下一页');
                    break;
                case 'p':
                case 'P':
                    this.toggleAutoPlay();
                    break;
                case 'r':
                case 'R':
                    this.refresh();
                    console.log('刷新数据');
                    break;
                case 'h':
                case 'H':
                    this.showHelp();
                    break;
            }
        });

        // 显示初始提示
        console.log('键盘控制：← → 切换页面，P 开启/关闭自动播放，R 刷新数据，H 显示帮助');
    }
    
    async refresh() {
        console.log('刷新数据...');
        await this.loadData();
        this.generatePages();
        this.showPage(this.currentPageIndex);
    }
    
    showErrorPage() {
        const container = document.getElementById('pageContainer');
        if (container) {
            container.innerHTML = `
                <div class="page-container">
                    <div class="content-area">
                        <div style="text-align: center; margin-top: 200px;">
                            <h1>加载失败</h1>
                            <p style="font-size: 24px; margin: 40px 0;">请检查网络连接或刷新页面重试</p>
                            <button onclick="location.reload()" style="background: #007bff; color: white; border: none; padding: 20px 40px; border-radius: 10px; cursor: pointer; font-size: 20px;">刷新页面</button>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    showHelp() {
        console.log('=== 麻醉科交班大屏操作指南 ===');
        console.log('← → 箭头键：切换页面');
        console.log('P 键：开启/关闭自动播放');
        console.log('R 键：刷新数据');
        console.log('H 键：显示此帮助信息');
        console.log('');
        console.log('当前状态：');
        console.log(`- 当前页面：${this.currentPageIndex + 1} / ${this.pages.length}`);
        console.log(`- 自动播放：${this.isAutoPlaying ? '开启' : '关闭'}`);
        console.log(`- 页面列表：${this.pages.map(p => p.title).join(', ')}`);
        console.log('===============================');
    }

    /**
     * 启动动态时间更新
     */
    startTimeUpdate() {
        // 立即更新一次
        this.updateTime();

        // 每秒更新时间
        this.timeUpdateInterval = setInterval(() => {
            this.updateTime();
        }, 1000);

        console.log('✅ 动态时间更新已启动');
    }

    /**
     * 更新时间显示
     */
    updateTime() {
        const timeElement = document.getElementById('headerTime');
        if (timeElement) {
            const now = new Date();
            const dateStr = now.toLocaleDateString('zh-CN');
            const timeStr = now.toLocaleTimeString('zh-CN');

            const datetimeDisplay = timeElement.querySelector('.datetime-display');
            if (datetimeDisplay) {
                datetimeDisplay.textContent = `${dateStr} ${timeStr}`;
            }
        }
    }

    /**
     * 停止时间更新
     */
    stopTimeUpdate() {
        if (this.timeUpdateInterval) {
            clearInterval(this.timeUpdateInterval);
            this.timeUpdateInterval = null;
            console.log('⏹️ 动态时间更新已停止');
        }
    }
}

// 全局实例
window.pageManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.pageManager = new BackgroundPageManager();
});
