/**
 * 麻醉科交班大屏软件主脚本 - 5种模块化页面模板
 */

class HandoverDisplay {
    constructor() {
        this.websocket = null;
        this.init();
    }
    
    /**
     * 初始化应用
     */
    async init() {
        console.log('初始化麻醉科交班大屏系统...');

        // 检查主题系统是否已经初始化
        if (window.themeSystem) {
            console.log('主题系统已初始化');
        } else if (window.ThemeSystem) {
            console.log('创建主题系统实例');
            window.themeSystem = new ThemeSystem();
        } else {
            console.error('主题系统未找到');
        }

        // 等待一下让主题系统初始化完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 初始化页面管理器
        if (window.HandoverPageManager) {
            console.log('创建页面管理器实例');
            window.handoverPageManager = new HandoverPageManager({
                enableKeyboard: true
            });
        } else {
            console.error('页面管理器类未找到');
        }

        this.setupEventListeners();
        this.updateClock();

        console.log('麻醉科交班大屏系统已启动');
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                window.handoverPageManager?.refresh();
            }
        });
        
        // 窗口焦点变化时的处理
        window.addEventListener('focus', () => {
            window.handoverPageManager?.refresh();
        });
        
        // 错误处理
        window.addEventListener('error', (event) => {
            console.error('页面错误:', event.error);
        });
        
        // 控制面板显示/隐藏
        document.addEventListener('keydown', (e) => {
            if (e.key === 'c' || e.key === 'C') {
                this.toggleControlPanel();
            }
        });
    }
    
    /**
     * 控制面板显示/隐藏
     */
    toggleControlPanel() {
        const controlPanel = document.querySelector('.control-panel');
        if (controlPanel) {
            controlPanel.classList.toggle('hidden');
        }
    }
    
    /**
     * 更新时钟显示
     */
    updateClock() {
        const updateTime = () => {
            const timeElement = document.querySelector('.header-time');
            if (timeElement) {
                timeElement.textContent = DateTimeUtils.formatFullDateTime();
            }
        };

        updateTime();
        setInterval(updateTime, 1000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.handoverDisplay = new HandoverDisplay();
});
