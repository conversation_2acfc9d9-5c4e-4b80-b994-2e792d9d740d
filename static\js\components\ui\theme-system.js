/**
 * 主题系统 - 5个工作日主题
 * 每个主题对应不同的色彩方案和视觉风格
 */

class ThemeSystem {
    constructor() {
        this.themes = {
            // 参照 ECharts 默认主题 - 蓝色系
            monday: {
                name: '星期一 - 科技蓝 (ECharts Default)',
                primary: '#5470c6',
                secondary: '#91cc75',
                accent: '#fac858',
                tertiary: '#ee6666',
                quaternary: '#73c0de',
                background: 'linear-gradient(135deg, #7c9cc9 0%, #8fb3d3 50%, #a2c4dc 100%)',

                // 区域背景色 - 增强对比度
                headerBg: 'rgba(84, 112, 198, 0.85)',
                contentBg: 'rgba(240, 244, 248, 0.95)',
                statusBg: 'rgba(84, 112, 198, 0.80)',

                // 卡片背景色 - 与标题栏区分的柔和色调
                cardBackground: 'rgba(255, 255, 255, 0.98)',
                cardGradient: 'linear-gradient(135deg, rgba(84, 112, 198, 0.08) 0%, rgba(145, 204, 117, 0.05) 100%)',

                // 人员卡片专用渐变 - 使用更柔和的色调
                doctorCardGradient: 'linear-gradient(135deg, rgba(145, 204, 117, 0.9) 0%, rgba(250, 200, 88, 0.8) 100%)',
                nurseCardGradient: 'linear-gradient(135deg, rgba(250, 200, 88, 0.9) 0%, rgba(115, 192, 222, 0.8) 100%)',
                staffCardGradient: 'linear-gradient(135deg, rgba(115, 192, 222, 0.9) 0%, rgba(238, 102, 102, 0.8) 100%)',

                // 文字颜色
                textPrimary: '#2c3e50',
                textSecondary: '#7f8c8d',
                textLight: '#ffffff',
                textMuted: 'rgba(255, 255, 255, 0.85)',

                // 状态颜色 (ECharts 配色)
                success: '#91cc75',
                warning: '#fac858',
                error: '#ee6666',
                info: '#73c0de',

                // 效果
                shadow: '0 8px 32px rgba(84, 112, 198, 0.15)',
                glassmorphism: 'rgba(145, 204, 117, 0.25)', // 使用柔和的绿色，降低透明度
                borderColor: 'rgba(255, 255, 255, 0.18)',

                // 边框和分割线
                borderLight: 'rgba(236, 240, 241, 0.6)',
                divider: 'rgba(189, 195, 199, 0.3)'
            },
            // 参照 ECharts Vintage 主题 - 复古色系
            tuesday: {
                name: '星期二 - 复古棕 (ECharts Vintage)',
                primary: '#d87c7c',
                secondary: '#919e8b',
                accent: '#d7ab82',
                tertiary: '#6e7074',
                quaternary: '#61a0a8',
                background: 'linear-gradient(135deg, #c4a484 0%, #d1b896 50%, #dcc5a8 100%)',

                // 区域背景色 - 增强对比度
                headerBg: 'rgba(216, 124, 124, 0.85)',
                contentBg: 'rgba(250, 248, 246, 0.95)',
                statusBg: 'rgba(145, 158, 139, 0.80)',

                // 卡片背景色 - 与标题栏区分的柔和色调
                cardBackground: 'rgba(255, 255, 255, 0.98)',
                cardGradient: 'linear-gradient(135deg, rgba(216, 124, 124, 0.08) 0%, rgba(145, 158, 139, 0.05) 100%)',

                // 人员卡片专用渐变 - 使用更柔和的色调
                doctorCardGradient: 'linear-gradient(135deg, rgba(145, 158, 139, 0.9) 0%, rgba(215, 171, 130, 0.8) 100%)',
                nurseCardGradient: 'linear-gradient(135deg, rgba(215, 171, 130, 0.9) 0%, rgba(97, 160, 168, 0.8) 100%)',
                staffCardGradient: 'linear-gradient(135deg, rgba(97, 160, 168, 0.9) 0%, rgba(110, 112, 116, 0.8) 100%)',

                // 文字颜色
                textPrimary: '#3e4444',
                textSecondary: '#6e7074',
                textLight: '#ffffff',
                textMuted: 'rgba(255, 255, 255, 0.85)',

                // 状态颜色 (Vintage 配色)
                success: '#919e8b',
                warning: '#d7ab82',
                error: '#d87c7c',
                info: '#61a0a8',

                // 效果
                shadow: '0 8px 32px rgba(216, 124, 124, 0.15)',
                glassmorphism: 'rgba(145, 158, 139, 0.25)', // 使用柔和的绿色，降低透明度
                borderColor: 'rgba(255, 255, 255, 0.18)',

                // 边框和分割线
                borderLight: 'rgba(248, 245, 243, 0.6)',
                divider: 'rgba(190, 185, 180, 0.3)'
            },
            // 清新中性灰色系 - 明亮舒适
            wednesday: {
                name: '星期三 - 清新灰 (Fresh Neutral)',
                primary: '#6c7b7f',
                secondary: '#95a5a6',
                accent: '#7fb3d3',
                tertiary: '#a8c8ec',
                quaternary: '#b8d4f0',
                background: 'linear-gradient(135deg, #ecf0f1 0%, #d5dbdb 50%, #bdc3c7 100%)',

                // 区域背景色 - 增强对比度
                headerBg: 'rgba(108, 123, 127, 0.85)',
                contentBg: 'rgba(252, 253, 254, 0.95)',
                statusBg: 'rgba(149, 165, 166, 0.80)',

                // 卡片背景色 - 与标题栏区分的柔和色调
                cardBackground: 'rgba(255, 255, 255, 0.98)',
                cardGradient: 'linear-gradient(135deg, rgba(108, 123, 127, 0.06) 0%, rgba(127, 179, 211, 0.04) 100%)',

                // 人员卡片专用渐变 - 使用更柔和的色调
                doctorCardGradient: 'linear-gradient(135deg, rgba(149, 165, 166, 0.9) 0%, rgba(127, 179, 211, 0.8) 100%)',
                nurseCardGradient: 'linear-gradient(135deg, rgba(127, 179, 211, 0.9) 0%, rgba(168, 200, 236, 0.8) 100%)',
                staffCardGradient: 'linear-gradient(135deg, rgba(168, 200, 236, 0.9) 0%, rgba(184, 212, 240, 0.8) 100%)',

                // 文字颜色
                textPrimary: '#2c3e50',
                textSecondary: '#7f8c8d',
                textLight: '#ffffff',
                textMuted: 'rgba(255, 255, 255, 0.90)',

                // 状态颜色 (清新配色)
                success: '#27ae60',
                warning: '#f39c12',
                error: '#e74c3c',
                info: '#3498db',

                // 效果
                shadow: '0 8px 32px rgba(108, 123, 127, 0.12)',
                glassmorphism: 'rgba(149, 165, 166, 0.25)', // 使用柔和的灰色，降低透明度
                borderColor: 'rgba(108, 123, 127, 0.15)',

                // 边框和分割线
                borderLight: 'rgba(236, 240, 241, 0.8)',
                divider: 'rgba(189, 195, 199, 0.4)'
            },
            // 参照 ECharts Macarons 主题 - 马卡龙色系
            thursday: {
                name: '星期四 - 马卡龙 (ECharts Macarons)',
                primary: '#2ec7c9',
                secondary: '#b6a2de',
                accent: '#5ab1ef',
                tertiary: '#ffb980',
                quaternary: '#d87a80',
                background: 'linear-gradient(135deg, #a8d8ea 0%, #b8e6b8 50%, #d4c5f9 100%)',

                // 区域背景色 - 增强对比度
                headerBg: 'rgba(46, 199, 201, 0.85)',
                contentBg: 'rgba(248, 252, 255, 0.95)',
                statusBg: 'rgba(182, 162, 222, 0.80)',

                // 卡片背景色 - 与标题栏区分的柔和色调
                cardBackground: 'rgba(255, 255, 255, 0.98)',
                cardGradient: 'linear-gradient(135deg, rgba(46, 199, 201, 0.05) 0%, rgba(182, 162, 222, 0.03) 100%)',

                // 人员卡片专用渐变 - 使用更柔和的色调
                doctorCardGradient: 'linear-gradient(135deg, rgba(182, 162, 222, 0.9) 0%, rgba(90, 177, 239, 0.8) 100%)',
                nurseCardGradient: 'linear-gradient(135deg, rgba(90, 177, 239, 0.9) 0%, rgba(255, 185, 128, 0.8) 100%)',
                staffCardGradient: 'linear-gradient(135deg, rgba(255, 185, 128, 0.9) 0%, rgba(216, 122, 128, 0.8) 100%)',

                // 文字颜色
                textPrimary: '#333333',
                textSecondary: '#666666',
                textLight: '#ffffff',
                textMuted: 'rgba(255, 255, 255, 0.9)',

                // 状态颜色 (Macarons 配色)
                success: '#2ec7c9',
                warning: '#ffb980',
                error: '#d87a80',
                info: '#5ab1ef',

                // 效果
                shadow: '0 8px 32px rgba(46, 199, 201, 0.12)',
                glassmorphism: 'rgba(182, 162, 222, 0.25)', // 使用柔和的紫色，降低透明度
                borderColor: 'rgba(46, 199, 201, 0.2)',

                // 边框和分割线
                borderLight: 'rgba(232, 244, 248, 0.8)',
                divider: 'rgba(182, 162, 222, 0.2)'
            },
            // 参照 ECharts Roma 主题 - 罗马红色系
            friday: {
                name: '星期五 - 罗马红 (ECharts Roma)',
                primary: '#e01f54',
                secondary: '#001852',
                accent: '#f5e8a3',
                tertiary: '#b8d2c7',
                quaternary: '#c6b38e',
                background: 'linear-gradient(135deg, #c4a1a1 0%, #d4b5b5 50%, #e4c9c9 100%)',

                // 区域背景色 - 增强对比度
                headerBg: 'rgba(224, 31, 84, 0.85)',
                contentBg: 'rgba(252, 248, 245, 0.95)',
                statusBg: 'rgba(184, 210, 199, 0.85)',

                // 卡片背景色 - 与标题栏区分的柔和色调
                cardBackground: 'rgba(255, 255, 255, 0.98)',
                cardGradient: 'linear-gradient(135deg, rgba(224, 31, 84, 0.08) 0%, rgba(245, 232, 163, 0.05) 100%)',

                // 人员卡片专用渐变 - 使用更柔和的色调
                doctorCardGradient: 'linear-gradient(135deg, rgba(184, 210, 199, 0.9) 0%, rgba(198, 179, 142, 0.8) 100%)',
                nurseCardGradient: 'linear-gradient(135deg, rgba(245, 232, 163, 0.9) 0%, rgba(184, 210, 199, 0.8) 100%)',
                staffCardGradient: 'linear-gradient(135deg, rgba(198, 179, 142, 0.9) 0%, rgba(245, 232, 163, 0.8) 100%)',

                // 文字颜色
                textPrimary: '#001852',
                textSecondary: '#2c4875',
                textLight: '#ffffff',
                textMuted: 'rgba(255, 255, 255, 0.85)',

                // 状态颜色 (Roma 配色)
                success: '#b8d2c7',
                warning: '#f5e8a3',
                error: '#e01f54',
                info: '#c6b38e',

                // 效果
                shadow: '0 8px 32px rgba(224, 31, 84, 0.15)',
                glassmorphism: 'rgba(245, 232, 163, 0.25)', // 使用柔和的黄色，降低透明度
                borderColor: 'rgba(255, 255, 255, 0.18)',

                // 边框和分割线
                borderLight: 'rgba(252, 248, 245, 0.6)',
                divider: 'rgba(198, 179, 142, 0.3)'
            }
        };

        this.currentTheme = this.getThemeByDay();
        this.applyTheme(this.currentTheme);
    }

    /**
     * 根据当前星期几获取主题
     * @returns {string} 主题名称
     */
    getThemeByDay() {
        const today = new Date().getDay();
        const themeMap = {
            1: 'monday',
            2: 'tuesday',
            3: 'wednesday',
            4: 'thursday',
            5: 'friday',
            0: 'friday', // 周日使用周五主题
            6: 'friday'  // 周六使用周五主题
        };
        return themeMap[today] || 'monday';
    }

    /**
     * 应用主题
     * @param {string} themeName - 主题名称
     */
    applyTheme(themeName) {
        if (!this.themes[themeName]) {
            console.warn(`主题 ${themeName} 不存在`);
            return;
        }

        const theme = this.themes[themeName];
        const root = document.documentElement;

        // 设置CSS变量
        root.style.setProperty('--theme-primary', theme.primary);
        root.style.setProperty('--theme-secondary', theme.secondary);
        root.style.setProperty('--theme-accent', theme.accent);
        root.style.setProperty('--theme-tertiary', theme.tertiary);
        root.style.setProperty('--theme-quaternary', theme.quaternary);
        root.style.setProperty('--theme-background', theme.background);

        // 区域背景色
        root.style.setProperty('--theme-header-bg', theme.headerBg);
        root.style.setProperty('--theme-content-bg', theme.contentBg);
        root.style.setProperty('--theme-status-bg', theme.statusBg);

        // 卡片背景色
        root.style.setProperty('--theme-card-bg', theme.cardBackground);
        root.style.setProperty('--theme-card-gradient', theme.cardGradient);

        // 人员卡片专用渐变
        root.style.setProperty('--theme-doctor-card-gradient', theme.doctorCardGradient || theme.cardGradient);
        root.style.setProperty('--theme-nurse-card-gradient', theme.nurseCardGradient || theme.cardGradient);
        root.style.setProperty('--theme-staff-card-gradient', theme.staffCardGradient || theme.cardGradient);

        // 文字颜色
        root.style.setProperty('--theme-text-primary', theme.textPrimary);
        root.style.setProperty('--theme-text-secondary', theme.textSecondary);
        root.style.setProperty('--theme-text-light', theme.textLight);
        root.style.setProperty('--theme-text-muted', theme.textMuted);

        // 状态颜色
        root.style.setProperty('--theme-success', theme.success);
        root.style.setProperty('--theme-warning', theme.warning);
        root.style.setProperty('--theme-error', theme.error);
        root.style.setProperty('--theme-info', theme.info);

        // 效果
        root.style.setProperty('--theme-shadow', theme.shadow);
        root.style.setProperty('--theme-glass', theme.glassmorphism);
        root.style.setProperty('--theme-border', theme.borderColor);

        // 边框和分割线
        root.style.setProperty('--theme-border-light', theme.borderLight);
        root.style.setProperty('--theme-divider', theme.divider);

        // 更新body类名
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add(`theme-${themeName}`);

        this.currentTheme = themeName;

        // 同步更新兼容性变量
        this.updateCompatibilityVariables(theme);

        // 触发主题变更事件
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: themeName, config: theme }
        }));
    }

    /**
     * 获取当前主题配置
     * @returns {Object} 主题配置
     */
    getCurrentTheme() {
        return this.themes[this.currentTheme];
    }

    /**
     * 获取主题名称
     * @returns {string} 主题名称
     */
    getCurrentThemeName() {
        return this.currentTheme;
    }

    /**
     * 手动切换主题
     * @param {string} themeName - 主题名称
     */
    switchTheme(themeName) {
        this.applyTheme(themeName);
    }

    /**
     * 获取所有可用主题
     * @returns {Object} 所有主题配置
     */
    getAllThemes() {
        return this.themes;
    }

    /**
     * 生成主题选择器HTML
     * @returns {string} HTML字符串
     */
    generateThemeSelector() {
        const themes = Object.keys(this.themes);
        return `
            <div class="theme-selector">
                <div class="theme-selector-title">选择主题</div>
                <div class="theme-options">
                    ${themes.map(themeName => {
                        const theme = this.themes[themeName];
                        const isActive = themeName === this.currentTheme;
                        return `
                            <div class="theme-option ${isActive ? 'active' : ''}" 
                                 data-theme="${themeName}"
                                 onclick="themeSystem.switchTheme('${themeName}')">
                                <div class="theme-preview" 
                                     style="background: ${theme.background};">
                                </div>
                                <div class="theme-name">${theme.name}</div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 获取主题相关的图标颜色
     * @param {string} type - 图标类型
     * @returns {string} 颜色值
     */
    getIconColor(type = 'primary') {
        const theme = this.getCurrentTheme();
        const colorMap = {
            primary: theme.primary,
            secondary: theme.secondary,
            accent: theme.accent,
            success: theme.success,
            warning: theme.warning,
            error: theme.error,
            text: theme.textPrimary
        };
        return colorMap[type] || theme.primary;
    }

    /**
     * 生成主题相关的渐变背景
     * @param {string} direction - 渐变方向
     * @returns {string} CSS渐变值
     */
    getGradientBackground(direction = '135deg') {
        const theme = this.getCurrentTheme();
        return `linear-gradient(${direction}, ${theme.primary} 0%, ${theme.secondary} 50%, ${theme.accent} 100%)`;
    }

    /**
     * 更新兼容性变量
     * 确保旧的CSS变量名也能正常工作
     * @param {Object} theme - 主题配置
     */
    updateCompatibilityVariables(theme) {
        const root = document.documentElement;

        // main.css 兼容性变量
        root.style.setProperty('--primary-color', theme.primary);
        root.style.setProperty('--primary-light', theme.secondary);
        root.style.setProperty('--primary-dark', theme.tertiary);
        root.style.setProperty('--success-color', theme.success);
        root.style.setProperty('--warning-color', theme.warning);
        root.style.setProperty('--error-color', theme.error);
        root.style.setProperty('--info-color', theme.info);
        root.style.setProperty('--background-color', theme.contentBg);
        root.style.setProperty('--surface-color', theme.cardBackground);
        root.style.setProperty('--text-primary', theme.textPrimary);
        root.style.setProperty('--text-secondary', theme.textSecondary);
        root.style.setProperty('--divider-color', theme.divider);

        // 确保所有组件都能获取到最新的主题变量
        console.log(`主题变量已更新: ${Object.keys(theme).length} 个变量`);
    }
}

// 全局导出
window.ThemeSystem = ThemeSystem;
window.themeSystem = new ThemeSystem();
