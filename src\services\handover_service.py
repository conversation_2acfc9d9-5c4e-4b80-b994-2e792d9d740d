"""
交班业务服务
"""
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
import logging

from src.database.connection import db_connection
from src.models.surgery import Surgery, SurgeryStatus
from src.services.statistics_service import StatisticsService

logger = logging.getLogger(__name__)

class HandoverService:
    """交班业务服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.statistics_service = StatisticsService()
    
    def get_handover_summary(self, target_date: Optional[date] = None) -> Dict[str, Any]:
        """
        获取交班汇总信息
        
        Args:
            target_date: 目标日期，默认为今天
            
        Returns:
            Dict[str, Any]: 交班汇总数据
        """
        if target_date is None:
            target_date = date.today()
        
        try:
            # 获取昨日统计
            yesterday = target_date - timedelta(days=1)
            yesterday_stats = self.statistics_service.get_daily_statistics(yesterday)
            
            # 获取今日统计
            today_stats = self.statistics_service.get_daily_statistics(target_date)
            
            # 获取今日手术安排
            today_surgeries = self.get_surgeries_by_date(target_date)
            
            # 获取重点关注事项
            important_items = self.get_important_items(target_date)
            
            # 获取设备状态（这里简化处理）
            equipment_status = self.get_equipment_status()
            
            handover_summary = {
                "date": target_date.isoformat(),
                "yesterday_summary": {
                    "date": yesterday.isoformat(),
                    "total_surgeries": yesterday_stats.total_surgeries,
                    "completed_surgeries": yesterday_stats.completed_surgeries,
                    "cancelled_surgeries": yesterday_stats.cancelled_surgeries,
                    "completion_rate": yesterday_stats.completion_rate,
                    "average_duration": yesterday_stats.average_duration
                },
                "today_summary": {
                    "date": target_date.isoformat(),
                    "total_scheduled": today_stats.total_surgeries,
                    "completed": today_stats.completed_surgeries,
                    "in_progress": len([s for s in today_surgeries if s.status == SurgeryStatus.IN_PROGRESS]),
                    "pending": len([s for s in today_surgeries if s.status == SurgeryStatus.SCHEDULED]),
                    "cancelled": today_stats.cancelled_surgeries
                },
                "today_schedule": [s.to_dict() for s in today_surgeries],
                "important_items": important_items,
                "equipment_status": equipment_status,
                "generated_at": datetime.now().isoformat()
            }
            
            self.logger.info(f"获取{target_date}交班汇总成功")
            return handover_summary
            
        except Exception as e:
            self.logger.error(f"获取交班汇总失败: {e}")
            return {
                "date": target_date.isoformat(),
                "yesterday_summary": {},
                "today_summary": {},
                "today_schedule": [],
                "important_items": [],
                "equipment_status": {},
                "generated_at": datetime.now().isoformat()
            }
    
    def get_surgeries_by_date(self, target_date: date) -> List[Surgery]:
        """
        根据日期获取手术列表
        
        Args:
            target_date: 目标日期
            
        Returns:
            List[Surgery]: 手术列表
        """
        try:
            date_str = target_date.isoformat()
            
            with db_connection.get_db_context() as conn:
                cursor = conn.execute("""
                    SELECT * FROM surgeries 
                    WHERE DATE(start_time) = ? 
                    ORDER BY start_time, room_number
                """, (date_str,))
                
                surgeries = []
                for row in cursor.fetchall():
                    surgery = Surgery.from_row(row)
                    surgeries.append(surgery)
                
                self.logger.info(f"获取{target_date}手术列表成功，共{len(surgeries)}条")
                return surgeries
                
        except Exception as e:
            self.logger.error(f"获取手术列表失败: {e}")
            return []
    
    def get_important_items(self, target_date: date) -> List[Dict[str, Any]]:
        """
        获取重点关注事项
        
        Args:
            target_date: 目标日期
            
        Returns:
            List[Dict[str, Any]]: 重点关注事项列表
        """
        important_items = []
        
        try:
            date_str = target_date.isoformat()
            
            with db_connection.get_db_context() as conn:
                # 1. 急诊手术
                cursor = conn.execute("""
                    SELECT patient_name, surgery_type, start_time, notes
                    FROM surgeries 
                    WHERE DATE(start_time) = ? AND (
                        notes LIKE '%急诊%' OR notes LIKE '%紧急%' OR notes LIKE '%emergency%'
                    )
                    ORDER BY start_time
                """, (date_str,))
                
                for row in cursor.fetchall():
                    important_items.append({
                        "type": "emergency",
                        "title": "急诊手术",
                        "description": f"患者: {row[0]}, 手术: {row[1]}",
                        "time": row[2],
                        "notes": row[3],
                        "priority": "high"
                    })
                
                # 2. 复杂手术（预计时间超过3小时）
                cursor = conn.execute("""
                    SELECT patient_name, surgery_type, start_time, notes
                    FROM surgeries 
                    WHERE DATE(start_time) = ? AND (
                        notes LIKE '%复杂%' OR notes LIKE '%4小时%' OR notes LIKE '%3小时%'
                    )
                    ORDER BY start_time
                """, (date_str,))
                
                for row in cursor.fetchall():
                    important_items.append({
                        "type": "complex",
                        "title": "复杂手术",
                        "description": f"患者: {row[0]}, 手术: {row[1]}",
                        "time": row[2],
                        "notes": row[3],
                        "priority": "medium"
                    })
                
                # 3. 已取消的手术
                cursor = conn.execute("""
                    SELECT patient_name, surgery_type, start_time, notes
                    FROM surgeries 
                    WHERE DATE(start_time) = ? AND status = 'cancelled'
                    ORDER BY start_time
                """, (date_str,))
                
                for row in cursor.fetchall():
                    important_items.append({
                        "type": "cancelled",
                        "title": "取消手术",
                        "description": f"患者: {row[0]}, 手术: {row[1]}",
                        "time": row[2],
                        "notes": row[3],
                        "priority": "low"
                    })
            
            self.logger.info(f"获取重点关注事项成功，共{len(important_items)}项")
            return important_items
            
        except Exception as e:
            self.logger.error(f"获取重点关注事项失败: {e}")
            return []
    
    def get_equipment_status(self) -> Dict[str, Any]:
        """
        获取设备状态（简化版本）
        
        Returns:
            Dict[str, Any]: 设备状态信息
        """
        # 这里是简化的设备状态，实际项目中可能需要连接设备管理系统
        return {
            "anesthesia_machines": {
                "total": 8,
                "available": 7,
                "maintenance": 1,
                "status": "normal"
            },
            "monitors": {
                "total": 10,
                "available": 10,
                "maintenance": 0,
                "status": "normal"
            },
            "ventilators": {
                "total": 6,
                "available": 5,
                "maintenance": 1,
                "status": "normal"
            },
            "last_updated": datetime.now().isoformat()
        }
    
    def get_staff_schedule(self, target_date: date) -> Dict[str, Any]:
        """
        获取人员排班信息
        
        Args:
            target_date: 目标日期
            
        Returns:
            Dict[str, Any]: 人员排班信息
        """
        try:
            date_str = target_date.isoformat()
            
            with db_connection.get_db_context() as conn:
                # 获取当日参与手术的医生
                cursor = conn.execute("""
                    SELECT DISTINCT surgeon, anesthesiologist
                    FROM surgeries 
                    WHERE DATE(start_time) = ?
                """, (date_str,))
                
                surgeons = set()
                anesthesiologists = set()
                
                for row in cursor.fetchall():
                    if row[0]:
                        surgeons.add(row[0])
                    if row[1]:
                        anesthesiologists.add(row[1])
                
                # 获取每个医生的工作负荷
                surgeon_workload = {}
                for surgeon in surgeons:
                    cursor = conn.execute("""
                        SELECT COUNT(*), 
                               SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END)
                        FROM surgeries 
                        WHERE DATE(start_time) = ? AND surgeon = ?
                    """, (date_str, surgeon))
                    
                    result = cursor.fetchone()
                    surgeon_workload[surgeon] = {
                        "total_surgeries": result[0] or 0,
                        "completed_surgeries": result[1] or 0
                    }
                
                anesthesiologist_workload = {}
                for anesthesiologist in anesthesiologists:
                    cursor = conn.execute("""
                        SELECT COUNT(*), 
                               SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END)
                        FROM surgeries 
                        WHERE DATE(start_time) = ? AND anesthesiologist = ?
                    """, (date_str, anesthesiologist))
                    
                    result = cursor.fetchone()
                    anesthesiologist_workload[anesthesiologist] = {
                        "total_surgeries": result[0] or 0,
                        "completed_surgeries": result[1] or 0
                    }
                
                staff_schedule = {
                    "date": date_str,
                    "surgeons": {
                        "count": len(surgeons),
                        "list": list(surgeons),
                        "workload": surgeon_workload
                    },
                    "anesthesiologists": {
                        "count": len(anesthesiologists),
                        "list": list(anesthesiologists),
                        "workload": anesthesiologist_workload
                    }
                }
                
                self.logger.info(f"获取{target_date}人员排班成功")
                return staff_schedule
                
        except Exception as e:
            self.logger.error(f"获取人员排班失败: {e}")
            return {
                "date": target_date.isoformat(),
                "surgeons": {"count": 0, "list": [], "workload": {}},
                "anesthesiologists": {"count": 0, "list": [], "workload": {}}
            }
    
    def update_surgery_status(self, surgery_id: int, new_status: SurgeryStatus, notes: Optional[str] = None) -> bool:
        """
        更新手术状态
        
        Args:
            surgery_id: 手术ID
            new_status: 新状态
            notes: 备注信息
            
        Returns:
            bool: 是否更新成功
        """
        try:
            with db_connection.get_db_context() as conn:
                # 检查手术是否存在
                cursor = conn.execute("SELECT id FROM surgeries WHERE id = ?", (surgery_id,))
                if not cursor.fetchone():
                    self.logger.warning(f"手术ID {surgery_id} 不存在")
                    return False
                
                # 更新状态
                update_sql = "UPDATE surgeries SET status = ?, updated_at = CURRENT_TIMESTAMP"
                params = [new_status.value]
                
                if notes:
                    update_sql += ", notes = ?"
                    params.append(notes)
                
                # 如果状态是已完成，设置结束时间
                if new_status == SurgeryStatus.COMPLETED:
                    update_sql += ", end_time = ?"
                    params.append(datetime.now().isoformat())
                
                update_sql += " WHERE id = ?"
                params.append(surgery_id)
                
                cursor = conn.execute(update_sql, params)
                conn.commit()
                
                if cursor.rowcount > 0:
                    self.logger.info(f"手术状态更新成功: ID={surgery_id}, 状态={new_status.value}")

                    # 发送实时更新通知
                    try:
                        from src.services.data_update_notifier import notifier
                        notifier.notify_surgery_update_sync(surgery_id, "surgery_status")
                    except Exception as e:
                        self.logger.warning(f"发送实时更新通知失败: {e}")

                    return True
                else:
                    self.logger.warning(f"手术状态更新失败: ID={surgery_id}")
                    return False
                
        except Exception as e:
            self.logger.error(f"更新手术状态失败: {e}")
            return False
