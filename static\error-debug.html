<!DOCTYPE html>
<html>
<head>
    <title>错误调试</title>
    <style>
        body { 
            background: #333; 
            color: white; 
            font-family: Arial, sans-serif; 
            padding: 20px; 
        }
        .error-log { 
            background: rgba(255, 0, 0, 0.1);
            border: 1px solid #ff6b6b;
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .info-log { 
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #51cf66;
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>JavaScript错误调试</h1>
    <div id="logs"></div>
    
    <script>
        const logs = document.getElementById('logs');
        
        // 捕获所有错误
        window.addEventListener('error', function(event) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-log';
            errorDiv.textContent = `
错误类型: ${event.error?.name || 'Unknown'}
错误消息: ${event.message}
文件: ${event.filename}
行号: ${event.lineno}
列号: ${event.colno}
堆栈: ${event.error?.stack || 'No stack trace'}
            `;
            logs.appendChild(errorDiv);
        });
        
        // 捕获Promise错误
        window.addEventListener('unhandledrejection', function(event) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-log';
            errorDiv.textContent = `
Promise错误: ${event.reason}
            `;
            logs.appendChild(errorDiv);
        });
        
        function logInfo(message) {
            const infoDiv = document.createElement('div');
            infoDiv.className = 'info-log';
            infoDiv.textContent = message;
            logs.appendChild(infoDiv);
        }
        
        logInfo('开始错误检测...');
        
        // 测试可能出现语法错误的代码
        try {
            // 测试1: 基本对象解构
            logInfo('测试1: 基本对象解构');
            const config1 = { name: 'test' };
            const { name = 'default' } = config1;
            logInfo(`✓ 对象解构成功: ${name}`);
            
            // 测试2: 复杂模板字符串
            logInfo('测试2: 复杂模板字符串');
            const allergies = ['青霉素', '磺胺类'];
            const template = `
                <div>
                    ${allergies.length > 0 ? `
                        <div>
                            ${allergies.map(allergy => `
                                <span>${allergy}</span>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
            logInfo('✓ 复杂模板字符串成功');
            
            // 测试3: 箭头函数和map
            logInfo('测试3: 箭头函数和map');
            const items = ['item1', 'item2'];
            const result = items.map(item => `<span>${item}</span>`).join('');
            logInfo(`✓ 箭头函数和map成功: ${result}`);
            
            // 测试4: 类定义
            logInfo('测试4: 类定义');
            class TestCard {
                createCard(config) {
                    const { title = 'default' } = config;
                    return `<div>${title}</div>`;
                }
            }
            const card = new TestCard();
            const cardResult = card.createCard({ title: 'test' });
            logInfo(`✓ 类定义成功: ${cardResult}`);
            
            // 测试5: 复杂的嵌套结构
            logInfo('测试5: 复杂的嵌套结构');
            const complexConfig = {
                allergies: ['test1', 'test2'],
                vitals: { bp: '120/80' }
            };
            
            const complexTemplate = `
                <div>
                    ${complexConfig.allergies.length > 0 ? `
                        <div>
                            ${complexConfig.allergies.map(allergy => `
                                <span style="background: red;">${allergy}</span>
                            `).join('')}
                        </div>
                    ` : ''}
                    ${Object.keys(complexConfig.vitals).length > 0 ? `
                        <div>
                            ${Object.entries(complexConfig.vitals).map(([key, value]) => `
                                <div>${key}: ${value}</div>
                            `).join('')}
                        </div>
                    ` : ''}
                </div>
            `;
            logInfo('✓ 复杂嵌套结构成功');
            
            // 显示结果
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = complexTemplate;
            resultDiv.style.marginTop = '20px';
            resultDiv.style.padding = '15px';
            resultDiv.style.background = 'rgba(255, 255, 255, 0.1)';
            resultDiv.style.borderRadius = '8px';
            document.body.appendChild(resultDiv);
            
            logInfo('所有测试完成，没有发现语法错误');
            
        } catch (error) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-log';
            errorDiv.textContent = `
捕获到错误:
名称: ${error.name}
消息: ${error.message}
堆栈: ${error.stack}
            `;
            logs.appendChild(errorDiv);
        }
    </script>
</body>
</html>
