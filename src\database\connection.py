"""
数据库连接管理模块
"""
import sqlite3
from contextlib import contextmanager
from typing import Generator
import logging
from pathlib import Path

from config.settings import settings

logger = logging.getLogger(__name__)

class DatabaseConnection:
    """数据库连接管理类"""
    
    def __init__(self, db_path: str = None):
        """
        初始化数据库连接
        
        Args:
            db_path: 数据库文件路径，如果为None则使用配置中的路径
        """
        if db_path is None:
            # 从DATABASE_URL中提取SQLite文件路径
            db_url = settings.DATABASE_URL
            if db_url.startswith("sqlite:///"):
                self.db_path = db_url.replace("sqlite:///", "")
            else:
                self.db_path = str(settings.BASE_DIR / "data" / "database" / "handover.db")
        else:
            self.db_path = db_path
        
        # 确保数据库目录存在
        Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"数据库路径: {self.db_path}")
    
    def get_connection(self) -> sqlite3.Connection:
        """
        获取数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
            return conn
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    @contextmanager
    def get_db_context(self) -> Generator[sqlite3.Connection, None, None]:
        """
        获取数据库连接上下文管理器
        
        Yields:
            sqlite3.Connection: 数据库连接对象
        """
        conn = None
        try:
            conn = self.get_connection()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def execute_script(self, script_path: str) -> None:
        """
        执行SQL脚本文件
        
        Args:
            script_path: SQL脚本文件路径
        """
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                script = f.read()
            
            with self.get_db_context() as conn:
                conn.executescript(script)
                conn.commit()
                logger.info(f"SQL脚本执行成功: {script_path}")
        
        except Exception as e:
            logger.error(f"SQL脚本执行失败: {e}")
            raise
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            with self.get_db_context() as conn:
                cursor = conn.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False

# 全局数据库连接实例
db_connection = DatabaseConnection()
