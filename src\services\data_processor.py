"""
数据处理服务
"""
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Tuple
import logging

from src.services.excel_parser import ExcelParser
from src.models.surgery import Surgery
from src.database.connection import db_connection

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理服务类"""
    
    def __init__(self):
        self.excel_parser = ExcelParser()
        self.logger = logging.getLogger(__name__)
    
    def process_excel_file(self, file_path: str, upload_record_id: int) -> Dict[str, Any]:
        """
        处理Excel文件并导入数据库
        
        Args:
            file_path: Excel文件路径
            upload_record_id: 上传记录ID
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            self.logger.info(f"开始处理Excel文件: {file_path}")
            
            # 更新上传记录状态为处理中
            self._update_upload_status(upload_record_id, "processing")
            
            # 解析Excel文件
            surgeries, parse_errors = self.excel_parser.parse_excel_file(file_path)
            
            if not surgeries and parse_errors:
                # 解析完全失败
                error_message = "; ".join(parse_errors[:5])  # 只保留前5个错误
                self._update_upload_status(upload_record_id, "failed", error_message)
                return {
                    "success": False,
                    "message": "Excel文件解析失败",
                    "errors": parse_errors,
                    "imported_count": 0
                }
            
            # 导入数据到数据库
            imported_count, import_errors = self._import_surgeries(surgeries)
            
            # 合并所有错误
            all_errors = parse_errors + import_errors
            
            # 确定最终状态
            if imported_count > 0:
                status = "completed"
                message = f"成功导入{imported_count}条记录"
                if all_errors:
                    message += f"，{len(all_errors)}条记录有错误"
            else:
                status = "failed"
                message = "没有成功导入任何记录"
            
            # 更新上传记录
            error_message = "; ".join(all_errors[:10]) if all_errors else None  # 只保留前10个错误
            self._update_upload_status(
                upload_record_id, 
                status, 
                error_message, 
                imported_count
            )
            
            self.logger.info(f"Excel文件处理完成: 导入{imported_count}条，错误{len(all_errors)}条")
            
            return {
                "success": imported_count > 0,
                "message": message,
                "imported_count": imported_count,
                "errors": all_errors,
                "total_parsed": len(surgeries)
            }
            
        except Exception as e:
            self.logger.error(f"Excel文件处理失败: {e}")
            self._update_upload_status(upload_record_id, "failed", str(e))
            return {
                "success": False,
                "message": f"文件处理失败: {str(e)}",
                "errors": [str(e)],
                "imported_count": 0
            }
    
    def _import_surgeries(self, surgeries: List[Surgery]) -> Tuple[int, List[str]]:
        """
        导入手术数据到数据库
        
        Args:
            surgeries: 手术数据列表
            
        Returns:
            Tuple[int, List[str]]: (成功导入数量, 错误信息列表)
        """
        imported_count = 0
        errors = []
        
        with db_connection.get_db_context() as conn:
            for i, surgery in enumerate(surgeries):
                try:
                    # 检查是否已存在相同的记录（根据患者姓名、科室和手术日期）
                    cursor = conn.execute("""
                        SELECT id FROM surgeries
                        WHERE patient_name = ? AND department = ? AND surgery_date = ?
                    """, (surgery.patient_name, surgery.department, surgery.surgery_date.isoformat()))

                    existing = cursor.fetchone()
                    if existing:
                        errors.append(f"第{i+1}条记录已存在（患者: {surgery.patient_name}，科室: {surgery.department}，日期: {surgery.surgery_date}）")
                        continue
                    
                    # 插入新记录
                    cursor = conn.execute("""
                        INSERT INTO surgeries (
                            department, patient_name, age, bed_number, surgery_type,
                            anesthesia_type, allergy_history, special_situation,
                            surgery_date, is_emergency, status
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        surgery.department,
                        surgery.patient_name,
                        surgery.age,
                        surgery.bed_number,
                        surgery.surgery_type,
                        surgery.anesthesia_type,
                        surgery.allergy_history,
                        surgery.special_situation,
                        surgery.surgery_date.isoformat() if surgery.surgery_date else None,
                        1 if surgery.is_emergency else 0,
                        surgery.status.value
                    ))
                    
                    imported_count += 1
                    
                except Exception as e:
                    error_msg = f"第{i+1}条记录导入失败: {str(e)}"
                    errors.append(error_msg)
                    self.logger.warning(error_msg)
            
            # 提交事务
            conn.commit()
        
        return imported_count, errors
    
    def _update_upload_status(
        self, 
        upload_record_id: int, 
        status: str, 
        error_message: str = None, 
        imported_count: int = 0
    ):
        """更新上传记录状态"""
        try:
            with db_connection.get_db_context() as conn:
                if status == "processing":
                    conn.execute("""
                        UPDATE upload_records 
                        SET status = ?, processed_time = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (status, upload_record_id))
                else:
                    conn.execute("""
                        UPDATE upload_records 
                        SET status = ?, error_message = ?, imported_count = ?,
                            processed_time = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (status, error_message, imported_count, upload_record_id))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"更新上传记录状态失败: {e}")
    
    def validate_excel_structure(self, file_path: str) -> Dict[str, Any]:
        """
        验证Excel文件结构
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            import pandas as pd
            
            # 读取Excel文件的第一行（列名）
            df = pd.read_excel(file_path, nrows=0)
            columns = [str(col).strip() for col in df.columns]
            
            # 检查必需的列
            required_columns = [
                '患者姓名', '患者ID', '手术类型', '麻醉方式', 
                '主刀医生', '麻醉医生', '手术间', '开始时间'
            ]
            
            missing_columns = []
            for req_col in required_columns:
                found = False
                for col in columns:
                    if req_col in col or col in req_col:
                        found = True
                        break
                if not found:
                    missing_columns.append(req_col)
            
            # 读取前几行数据进行预览
            preview_df = pd.read_excel(file_path, nrows=5)
            preview_data = preview_df.to_dict('records')
            
            return {
                "valid": len(missing_columns) == 0,
                "columns": columns,
                "missing_columns": missing_columns,
                "total_rows": len(pd.read_excel(file_path)),
                "preview_data": preview_data,
                "suggestions": self._get_column_suggestions(columns)
            }
            
        except Exception as e:
            self.logger.error(f"Excel结构验证失败: {e}")
            return {
                "valid": False,
                "error": str(e),
                "columns": [],
                "missing_columns": [],
                "total_rows": 0,
                "preview_data": [],
                "suggestions": []
            }
    
    def _get_column_suggestions(self, columns: List[str]) -> List[Dict[str, str]]:
        """获取列名建议"""
        suggestions = []
        
        # 列名映射建议
        mapping_suggestions = {
            '病人姓名': '患者姓名',
            '姓名': '患者姓名',
            '住院号': '患者ID',
            '病历号': '患者ID',
            '手术名称': '手术类型',
            '麻醉类型': '麻醉方式',
            '手术医生': '主刀医生',
            '手术室': '手术间',
            '房间号': '手术间',
            '手术开始时间': '开始时间',
            '手术结束时间': '结束时间',
            '手术状态': '状态'
        }
        
        for col in columns:
            if col in mapping_suggestions:
                suggestions.append({
                    "original": col,
                    "suggested": mapping_suggestions[col],
                    "reason": "标准化列名"
                })
        
        return suggestions
    
    def get_import_statistics(self, days: int = 7) -> Dict[str, Any]:
        """
        获取导入统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            with db_connection.get_db_context() as conn:
                # 获取最近N天的上传记录统计
                cursor = conn.execute("""
                    SELECT 
                        DATE(upload_time) as upload_date,
                        COUNT(*) as total_uploads,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_uploads,
                        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_uploads,
                        SUM(imported_count) as total_imported
                    FROM upload_records 
                    WHERE upload_time >= datetime('now', '-{} days')
                    GROUP BY DATE(upload_time)
                    ORDER BY upload_date DESC
                """.format(days))
                
                daily_stats = []
                for row in cursor.fetchall():
                    daily_stats.append({
                        "date": row[0],
                        "total_uploads": row[1],
                        "successful_uploads": row[2],
                        "failed_uploads": row[3],
                        "total_imported": row[4]
                    })
                
                # 获取总体统计
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_uploads,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_uploads,
                        SUM(imported_count) as total_imported,
                        AVG(file_size) as avg_file_size
                    FROM upload_records
                """)
                
                overall_stats = cursor.fetchone()
                
                return {
                    "daily_statistics": daily_stats,
                    "overall_statistics": {
                        "total_uploads": overall_stats[0] or 0,
                        "successful_uploads": overall_stats[1] or 0,
                        "total_imported": overall_stats[2] or 0,
                        "average_file_size": round(overall_stats[3] or 0, 2),
                        "success_rate": round((overall_stats[1] or 0) / max(overall_stats[0] or 1, 1) * 100, 2)
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取导入统计失败: {e}")
            return {
                "daily_statistics": [],
                "overall_statistics": {
                    "total_uploads": 0,
                    "successful_uploads": 0,
                    "total_imported": 0,
                    "average_file_size": 0,
                    "success_rate": 0
                }
            }
