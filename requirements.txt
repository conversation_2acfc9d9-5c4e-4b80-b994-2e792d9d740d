# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据处理
pandas==2.1.3
openpyxl==3.1.2
xlrd==2.0.1

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1

# 文件处理
python-multipart==0.0.6
aiofiles==23.2.1

# 模板引擎
jinja2==3.1.2

# 配置管理
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 日志和监控
loguru==0.7.2

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# HTTP客户端
httpx==0.25.2
requests==2.31.0

# 开发工具
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# 安全
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# WebSocket支持
websockets==12.0

# 数据验证
email-validator==2.1.0

# 图像处理（可选）
Pillow==10.1.0

# 缓存（可选）
redis==5.0.1

# 环境变量
python-decouple==3.8
