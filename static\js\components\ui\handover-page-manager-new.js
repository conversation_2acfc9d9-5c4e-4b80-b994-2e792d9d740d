/**
 * 交班大屏页面管理器 - 模块化组件系统
 */

class HandoverPageManager {
    constructor(options = {}) {
        this.options = {
            enableKeyboard: true,
            ...options
        };
        
        this.currentPageIndex = 0;
        this.pageComponents = [];
        this.currentPage = null;
        
        this.init();
    }
    
    async init() {
        console.log('初始化交班大屏页面管理器...');
        
        // 创建页面组件实例
        this.pageComponents = [
            new TitlePage(),
            new StatisticsPage('yesterday'),
            new StatisticsPage('today'),
            new PatientsPage(),
            new NoticesPage()
        ];
        
        // 显示第一页
        await this.showPage(0);
        
        // 绑定键盘事件
        if (this.options.enableKeyboard) {
            this.bindKeyboardEvents();
        }
        
        // 更新时间显示
        this.updateTime();
        setInterval(() => this.updateTime(), 1000);
        
        console.log(`交班大屏初始化完成，共 ${this.pageComponents.length} 页`);
    }
    
    /**
     * 显示指定页面
     */
    async showPage(index) {
        if (index < 0 || index >= this.pageComponents.length) return;
        
        // 失活当前页面
        if (this.currentPage) {
            this.currentPage.onDeactivate();
        }
        
        this.currentPageIndex = index;
        const pageComponent = this.pageComponents[index];
        
        try {
            // 渲染页面内容
            const content = await pageComponent.render();
            
            // 更新内容区
            const contentArea = document.querySelector('.content-area');
            if (contentArea) {
                contentArea.innerHTML = content;
            }
            
            // 更新标题
            const headerTitle = document.querySelector('.header-title');
            if (headerTitle) {
                headerTitle.textContent = pageComponent.getTitle();
            }
            
            // 更新页码显示
            this.updatePageIndicator();
            
            // 激活新页面
            pageComponent.onActivate();
            this.currentPage = pageComponent;
            
            console.log(`显示第 ${index + 1} 页: ${pageComponent.getTitle()}`);
        } catch (error) {
            console.error(`显示页面失败:`, error);
            this.showErrorPage(error);
        }
    }
    
    /**
     * 显示错误页面
     */
    showErrorPage(error) {
        const contentArea = document.querySelector('.content-area');
        if (contentArea) {
            contentArea.innerHTML = `
                <div class="error-page">
                    <div class="error-content">
                        <h2>页面加载失败</h2>
                        <p>错误信息: ${error.message}</p>
                        <button onclick="window.handoverPageManager.refresh()">重试</button>
                    </div>
                </div>
            `;
        }
    }
    
    updatePageIndicator() {
        const pageIndicator = document.querySelector('.page-indicator');
        if (pageIndicator) {
            pageIndicator.textContent = `${this.currentPageIndex + 1} / ${this.pageComponents.length}`;
        }
    }
    
    updateTime() {
        const timeElement = document.querySelector('.header-time');
        if (timeElement) {
            timeElement.textContent = DateTimeUtils.formatFullDateTime();
        }
    }
    
    nextPage() {
        const nextIndex = (this.currentPageIndex + 1) % this.pageComponents.length;
        this.showPage(nextIndex);
    }
    
    prevPage() {
        const prevIndex = (this.currentPageIndex - 1 + this.pageComponents.length) % this.pageComponents.length;
        this.showPage(prevIndex);
    }
    
    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            // 首先让当前页面处理键盘事件
            if (this.currentPage && this.currentPage.onKeyDown) {
                this.currentPage.onKeyDown(e);
            }
            
            // 然后处理全局键盘事件
            switch (e.key) {
                case 'ArrowLeft':
                    this.prevPage();
                    break;
                case 'ArrowRight':
                    this.nextPage();
                    break;
                case 'r':
                case 'R':
                    this.refresh();
                    break;
            }
        });
    }
    
    async refresh() {
        console.log('刷新当前页面数据...');
        if (this.currentPage && this.currentPage.refresh) {
            await this.currentPage.refresh();
            // 重新渲染当前页面
            await this.showPage(this.currentPageIndex);
        }
    }
    
    /**
     * 销毁页面管理器
     */
    destroy() {
        // 销毁所有页面组件
        this.pageComponents.forEach(page => {
            if (page.destroy) {
                page.destroy();
            }
        });
        
        this.pageComponents = [];
        this.currentPage = null;
        console.log('页面管理器已销毁');
    }
}

// 全局导出
window.HandoverPageManager = HandoverPageManager;
