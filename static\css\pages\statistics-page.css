/**
 * 昨日手术统计页面样式 - PPT全屏模式
 * 固定比例布局，包含卡片和图表
 */

/* 统计页面主容器 - 固定内容，禁止滚动 */
.stats-page-enhanced {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    position: fixed;
    top: 0;
    left: 0;
}

/* 主网格布局 - 固定16:9比例 */
.stats-main-grid {
    /* 固定16:9比例的容器 */
    width: 90vw;
    height: 90vh;
    max-width: 1920px;
    max-height: 1080px;
    aspect-ratio: 16/9;
    
    /* 如果屏幕比例不是16:9，则按比例缩放 */
    transform-origin: center;
    
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2vw;
    padding: 2vh 2vw;
    box-sizing: border-box;
}

/* 统计卡片区域 */
.stats-cards-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1.5vw;
    height: 100%;
    width: 100%;
}

/* 图表区域 */
.stats-charts-section {
    display: flex;
    flex-direction: column;
    gap: 2vh;
    height: 100%;
    width: 100%;
}

/* 图表容器 */
.chart-container {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1vw;
    padding: 2vh 1.5vw;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 0.5vw 2vw rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.chart-title {
    color: #ffffff;
    font-size: 1.5vw;
    font-weight: 600;
    margin: 0 0 1.5vh 0;
    text-align: center;
    text-shadow: 0 0.2vw 0.4vw rgba(0, 0, 0, 0.3);
}

.chart-container canvas {
    flex: 1;
    width: 100% !important;
    height: auto !important;
    max-height: none !important;
}

/* 统计卡片样式 - PPT全屏模式，固定比例 */
.stats-cards-section .stat-card {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2vh 1vw;
    border-radius: 1vw;
    box-shadow: 0 0.5vw 2vw rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 0;
}

.stats-cards-section .stat-card:hover {
    transform: translateY(-0.3vh);
    box-shadow: 0 1vw 3vw rgba(0, 0, 0, 0.15);
}

/* 统计卡片内容样式 - 使用视口单位 */
.stats-cards-section .stat-card .stat-icon {
    font-size: 3vw;
    margin-bottom: 1vh;
    opacity: 0.9;
}

.stats-cards-section .stat-card .stat-value {
    font-size: 4vw;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.5vh;
    text-shadow: 0 0.2vw 0.4vw rgba(0, 0, 0, 0.3);
    line-height: 1;
}

.stats-cards-section .stat-card .stat-title {
    font-size: 1.3vw;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 0.5vh;
    text-shadow: 0 0.1vw 0.2vw rgba(0, 0, 0, 0.3);
}

.stats-cards-section .stat-card .stat-subtitle {
    font-size: 1vw;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 0.1vw 0.2vw rgba(0, 0, 0, 0.2);
}

/* 确保在所有屏幕尺寸下保持相同的视觉效果 */
@media (max-aspect-ratio: 16/9) {
    .stats-main-grid {
        width: 95vw;
        height: calc(95vw * 9 / 16);
    }
}

@media (min-aspect-ratio: 16/9) {
    .stats-main-grid {
        width: calc(95vh * 16 / 9);
        height: 95vh;
    }
}
