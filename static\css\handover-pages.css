/**
 * 交班大屏通用样式
 * 只包含通用组件和布局，页面专用样式已分离到独立文件
 */

/* ===== 通用页面样式 ===== */
.content-area {
    padding: var(--spacing-2xl);
    height: 100%;
    overflow: hidden; /* 确保不滚动 */
    position: relative;
}

/* 传统标题页样式（非PPT模式） */
.title-page {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    text-align: center;
}

.title-header {
    margin-bottom: var(--spacing-2xl);
}

.hospital-name {
    font-size: var(--font-size-6xl);
    font-weight: 800;
    color: var(--theme-text-light);
    margin-bottom: var(--spacing-lg);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.8);
}

.date-info {
    display: flex;
    gap: var(--spacing-xl);
    justify-content: center;
    margin-bottom: var(--spacing-xl);
}

.date, .day {
    font-size: var(--font-size-4xl);
    color: var(--theme-primary);
    font-weight: 600;
}

.session-title {
    font-size: var(--font-size-5xl);
    color: var(--theme-accent);
    font-weight: 700;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.6);
}







/* ===== 通用组件样式 ===== */

/* 通用卡片容器 */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    width: 100%;
}

/* 通用节标题 */
.section-title {
    font-size: var(--font-size-3xl);
    color: var(--theme-text-light);
    margin: 0;
    text-align: center;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* ===== 通用页面组件样式 ===== */

/* 通用页面标题样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--theme-border);
}

.page-header h2 {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--theme-text-light);
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.8);
}

.date-range {
    font-size: var(--font-size-2xl);
    color: var(--theme-text-muted);
    font-weight: 500;
}

/* ===== 通用徽章和标签样式 ===== */

.risk-badge {
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-lg);
    font-weight: 600;
    text-transform: uppercase;
}

.risk-badge.high {
    background: var(--theme-error);
    color: var(--theme-text-light);
}

.risk-badge.medium {
    background: var(--theme-warning);
    color: var(--theme-text-light);
}

.risk-badge.low {
    background: var(--theme-success);
    color: var(--theme-text-light);
}

.badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.badge.emergency {
    background: var(--theme-error);
    color: var(--theme-text-light);
}

/* ===== 通用信息项样式 ===== */

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: var(--theme-glass);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--theme-border);
    backdrop-filter: blur(20px);
}

.info-label {
    font-size: var(--font-size-lg);
    color: var(--theme-text-muted);
    font-weight: 500;
}

.info-value {
    font-size: var(--font-size-2xl);
    color: var(--theme-text-light);
    font-weight: 600;
}

/* ===== 通用空状态和错误页面样式 ===== */

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
    color: var(--theme-text-muted);
}

.empty-icon {
    font-size: var(--font-size-6xl);
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-text {
    font-size: var(--font-size-xl);
    font-weight: 500;
}
