/* 现代化组件样式 */
/* 注意：CSS变量现在统一在 theme-variables.css 中定义 */
    
    /* 阴影系统 */
}

/* 统计卡片组件 */
.modern-stat-card {
    position: relative;
    background: var(--theme-card-bg, rgba(255, 255, 255, 0.95));
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--theme-border-light, rgba(255, 255, 255, 0.2));
    backdrop-filter: blur(20px);
    transition: var(--transition-base);
    overflow: hidden;
    cursor: pointer;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.modern-stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
}

.modern-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);
}

.stat-card-content {
    position: relative;
    z-index: 2;
}

.stat-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.stat-icon {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--theme-primary, #667eea);
    border-radius: var(--border-radius-lg);
    color: var(--theme-text-light, white);
    font-size: var(--font-size-2xl);
    backdrop-filter: blur(10px);
    border: 1px solid var(--theme-border-light, rgba(255, 255, 255, 0.3));
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.stat-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text-light, white);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-value {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--theme-text-light, white);
    line-height: 1;
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.trend-up { color: var(--theme-success); }
.trend-down { color: var(--theme-error); }
.trend-neutral { color: var(--theme-text-secondary); }

.stat-card-decoration {
    position: absolute;
    top: -50%;
    right: -20%;
    width: 120px;
    height: 120px;
    background: var(--theme-primary);
    opacity: 0.05;
    border-radius: 50%;
    transform: rotate(45deg);
}

/* 卡片尺寸变体 */
.stat-card-small {
    padding: var(--spacing-md);
}

.stat-card-small .stat-value {
    font-size: var(--font-size-3xl);
}

.stat-card-large {
    padding: var(--spacing-xl);
}

.stat-card-large .stat-value {
    font-size: 48px;
}

/* 人员卡片组件 */
.modern-person-card {
    background: var(--theme-card-bg, rgba(255, 255, 255, 0.95));
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--theme-border-light, rgba(255, 255, 255, 0.2));
    backdrop-filter: blur(20px);
    transition: var(--transition-base);
    overflow: hidden;
    position: relative;
    cursor: pointer;
    min-height: 180px;
}

/* 人员卡片文字增强可读性 */
.modern-person-card .person-role,
.modern-person-card .person-name {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-weight: 600;
}

.modern-person-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
}

.modern-person-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);
}

.person-card-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    height: 100%;
}

.person-avatar {
    position: relative;
    width: 64px;
    height: 64px;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    background: var(--theme-primary, #667eea);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid var(--theme-border-light, rgba(255, 255, 255, 0.3));
}

.person-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-icon {
    color: var(--theme-text-light, white);
    font-size: var(--font-size-2xl);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.status-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
}

.status-active { background: var(--theme-success); }
.status-busy { background: var(--theme-warning); }
.status-offline { background: var(--theme-text-secondary); }

.person-info {
    flex: 1;
}

.person-role {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.person-name {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--theme-text-light, white);
    margin-bottom: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.person-department {
    font-size: var(--font-size-xs);
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 信息卡片组件 */
.modern-info-card {
    background: var(--theme-card-bg, rgba(255, 255, 255, 0.95));
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--theme-border-light, rgba(255, 255, 255, 0.2));
    backdrop-filter: blur(20px);
    overflow: hidden;
    transition: var(--transition-base);
    position: relative;
    cursor: pointer;
    padding: var(--spacing-xl);
    min-height: 200px;
}

.modern-info-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
}

.modern-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);
}

.info-card-header {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.info-icon {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--theme-primary, #667eea);
    border-radius: var(--border-radius-lg);
    color: var(--theme-text-light, white);
    font-size: var(--font-size-2xl);
    backdrop-filter: blur(10px);
    border: 1px solid var(--theme-border-light, rgba(255, 255, 255, 0.3));
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.info-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--theme-text-primary, #1e293b);
}

.info-card-content {
    position: relative;
    z-index: 2;
    color: var(--theme-text-secondary, #64748b);
    line-height: 1.6;
}

.info-card-actions {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--theme-border);
    background: var(--theme-glass);
}

.info-action-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    background: var(--theme-primary);
    color: white;
}

.info-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 图表卡片组件 */
.modern-chart-card {
    background: var(--theme-card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--theme-border);
    backdrop-filter: blur(20px);
    overflow: hidden;
}

.chart-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--theme-border);
    background: var(--theme-glass);
}

.chart-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text-primary);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chart-control-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--border-radius-sm);
    background: var(--theme-glass);
    color: var(--theme-text-primary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-control-btn:hover {
    background: var(--theme-primary);
    color: white;
}

.chart-card-content {
    padding: var(--spacing-lg);
}

/* 列表卡片组件 */
.modern-list-card {
    background: var(--theme-card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--theme-border);
    backdrop-filter: blur(20px);
    overflow: hidden;
}

.list-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--theme-border);
    background: var(--theme-glass);
}

.list-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text-primary);
}

.list-count {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    background: var(--theme-glass);
    padding: 4px 8px;
    border-radius: var(--border-radius-sm);
}

.list-card-content {
    overflow-y: auto;
}

.list-items {
    padding: var(--spacing-sm);
}

.list-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
    cursor: pointer;
}

.list-item:hover {
    background: var(--theme-glass);
}

.list-item-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-primary);
}

.list-item-content {
    flex: 1;
}

.list-item-title {
    font-size: var(--font-size-base);
    font-weight: 500;
    color: var(--theme-text-primary);
    margin-bottom: 2px;
}

.list-item-subtitle {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
}

.list-item-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-primary);
}

.list-item-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

/* 网格容器 */
.modern-grid-container {
    display: grid;
    gap: var(--spacing-lg);
}

/* 页面容器 */
.modern-page-container {
    min-height: 100vh;
    background: var(--theme-background);
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--theme-glass);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--theme-border);
}

.page-title-section {
    flex: 1;
}

.page-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: white;
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

.page-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.current-time {
    font-size: var(--font-size-lg);
    font-weight: 500;
    color: white;
    background: var(--theme-glass);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--theme-border);
}

.page-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* 主题选择器 */
.theme-selector {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--theme-card-bg);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--theme-border);
    backdrop-filter: blur(20px);
    z-index: 1000;
    min-width: 200px;
}

.theme-selector-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--theme-text-primary);
    margin-bottom: var(--spacing-sm);
    text-align: center;
}

.theme-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.theme-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    border: 2px solid transparent;
}

.theme-option:hover {
    background: var(--theme-glass);
}

.theme-option.active {
    border-color: var(--theme-primary);
    background: var(--theme-glass);
}

.theme-preview {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: var(--shadow-sm);
}

.theme-name {
    font-size: var(--font-size-xs);
    color: var(--theme-text-primary);
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .modern-grid-container {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .modern-page-container {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .modern-grid-container {
        grid-template-columns: 1fr !important;
    }

    .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .theme-selector {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: var(--spacing-lg);
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.modern-stat-card,
.modern-person-card,
.modern-info-card,
.modern-chart-card,
.modern-list-card {
    animation: fadeInUp 0.6s ease-out;
}

.modern-stat-card:nth-child(2) { animation-delay: 0.1s; }
.modern-stat-card:nth-child(3) { animation-delay: 0.2s; }
.modern-stat-card:nth-child(4) { animation-delay: 0.3s; }

/* 悬停效果增强 */
.modern-stat-card:hover,
.modern-person-card:hover,
.modern-info-card:hover,
.modern-chart-card:hover,
.modern-list-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* 加载状态 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 页面区域样式 */
.overview-section {
    margin-bottom: var(--spacing-xl);
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: white;
    margin-bottom: var(--spacing-lg);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.section-title::before {
    content: '';
    width: 4px;
    height: 24px;
    background: var(--theme-primary);
    border-radius: 2px;
}

/* 主题特定样式 */
.theme-monday {
    --theme-gradient: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
}

.theme-tuesday {
    --theme-gradient: linear-gradient(135deg, #047857 0%, #10b981 50%, #34d399 100%);
}

.theme-wednesday {
    --theme-gradient: linear-gradient(135deg, #c2410c 0%, #ea580c 50%, #fb923c 100%);
}

.theme-thursday {
    --theme-gradient: linear-gradient(135deg, #6d28d9 0%, #7c3aed 50%, #a78bfa 100%);
}

.theme-friday {
    --theme-gradient: linear-gradient(135deg, #b91c1c 0%, #dc2626 50%, #f87171 100%);
}

/* 统计卡片主题变体 */
.stat-card-primary .stat-icon {
    background: var(--theme-primary);
}

.stat-card-warning .stat-icon {
    background: var(--theme-warning);
}

.stat-card-success .stat-icon {
    background: var(--theme-success);
}

.stat-card-error .stat-icon {
    background: var(--theme-error);
}

/* 现代化布局增强 */
.modern-page-container {
    position: relative;
    overflow: hidden;
}

.modern-page-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--theme-background);
    z-index: -1;
}

/* 网格布局响应式增强 */
.duty-grid {
    margin-bottom: var(--spacing-xl);
}

.stats-grid {
    margin-bottom: var(--spacing-xl);
}

/* 卡片进入动画 */
.modern-stat-card,
.modern-person-card {
    opacity: 0;
    transform: translateY(20px);
    animation: cardFadeIn 0.6s ease-out forwards;
}

.modern-stat-card:nth-child(1) { animation-delay: 0.1s; }
.modern-stat-card:nth-child(2) { animation-delay: 0.2s; }
.modern-stat-card:nth-child(3) { animation-delay: 0.3s; }

.modern-person-card:nth-child(1) { animation-delay: 0.1s; }
.modern-person-card:nth-child(2) { animation-delay: 0.2s; }
.modern-person-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes cardFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 现代化特色卡片 */
.modern-feature-card {
    position: relative;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transition: var(--transition-base);
    cursor: pointer;
    max-width: 400px;
    margin: 0 auto;
}

.modern-feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
}

.feature-card-background {
    position: relative;
    padding: var(--spacing-xl);
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-card-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    backdrop-filter: blur(10px);
}

.feature-card-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.feature-icon-container {
    margin-bottom: var(--spacing-lg);
}

.feature-icon-bg {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.feature-icon {
    font-size: var(--font-size-2xl);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.feature-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.feature-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: white;
    margin: 0 0 var(--spacing-sm) 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-description {
    font-size: var(--font-size-base);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0 0 var(--spacing-lg) 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.feature-action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-base);
    backdrop-filter: blur(10px);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    align-self: flex-start;
}

.feature-action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.feature-action-btn:active {
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modern-grid-container {
        grid-template-columns: 1fr !important;
        gap: var(--spacing-sm) !important;
    }

    .modern-stat-card,
    .modern-person-card,
    .modern-info-card {
        padding: var(--spacing-md);
    }

    .stat-value {
        font-size: var(--font-size-3xl);
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-lg);
    }

    .modern-feature-card {
        max-width: 100%;
    }

    .feature-card-background {
        padding: var(--spacing-lg);
        min-height: 240px;
    }

    .feature-title {
        font-size: var(--font-size-xl);
    }

    .feature-description {
        font-size: var(--font-size-sm);
    }
}
