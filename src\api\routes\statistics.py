"""
统计相关API路由
"""
from datetime import datetime, date, timedelta
from typing import Dict, Any, List
import logging

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from src.database.connection import db_connection
from src.services.statistics_service import StatisticsService
from src.services.handover_service import HandoverService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/statistics", tags=["statistics"])

class StatisticsResponse(BaseModel):
    """统计数据响应模型"""
    date: str
    total_surgeries: int
    completed_surgeries: int
    cancelled_surgeries: int
    emergency_surgeries: int
    in_progress_surgeries: int
    scheduled_surgeries: int
    average_duration: float
    completion_rate: float
    cancellation_rate: float

class ChartDataResponse(BaseModel):
    """图表数据响应模型"""
    labels: List[str]
    datasets: List[Dict[str, Any]]

@router.get("/yesterday", response_model=StatisticsResponse)
async def get_yesterday_statistics():
    """获取昨日手术统计"""
    try:
        yesterday = (date.today() - timedelta(days=1)).isoformat()
        
        with db_connection.get_db_context() as conn:
            # 获取基础统计数据
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_surgeries,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                    SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled,
                    AVG(CASE WHEN end_time IS NOT NULL AND status = 'completed'
                        THEN (julianday(end_time) - julianday(start_time)) * 24 * 60 
                        ELSE NULL END) as avg_duration
                FROM surgeries 
                WHERE DATE(start_time) = ?
            """, (yesterday,))
            
            result = cursor.fetchone()
            
            total = result[0] or 0
            completed = result[1] or 0
            cancelled = result[2] or 0
            in_progress = result[3] or 0
            scheduled = result[4] or 0
            avg_duration = result[5] or 0.0
            
            # 计算比率
            completion_rate = (completed / total * 100) if total > 0 else 0.0
            cancellation_rate = (cancelled / total * 100) if total > 0 else 0.0
            
            # 急诊手术数量（这里简化处理，实际可能需要额外字段标识）
            emergency_surgeries = 0
            
            statistics = StatisticsResponse(
                date=yesterday,
                total_surgeries=total,
                completed_surgeries=completed,
                cancelled_surgeries=cancelled,
                emergency_surgeries=emergency_surgeries,
                in_progress_surgeries=in_progress,
                scheduled_surgeries=scheduled,
                average_duration=round(avg_duration, 2),
                completion_rate=round(completion_rate, 2),
                cancellation_rate=round(cancellation_rate, 2)
            )
            
            logger.info(f"获取昨日统计成功: {yesterday}")
            return statistics
            
    except Exception as e:
        logger.error(f"获取昨日统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取昨日统计失败")

@router.get("/today", response_model=StatisticsResponse)
async def get_today_statistics():
    """获取今日手术统计"""
    try:
        today = date.today().isoformat()
        
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_surgeries,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                    SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled,
                    AVG(CASE WHEN end_time IS NOT NULL AND status = 'completed'
                        THEN (julianday(end_time) - julianday(start_time)) * 24 * 60 
                        ELSE NULL END) as avg_duration
                FROM surgeries 
                WHERE DATE(start_time) = ?
            """, (today,))
            
            result = cursor.fetchone()
            
            total = result[0] or 0
            completed = result[1] or 0
            cancelled = result[2] or 0
            in_progress = result[3] or 0
            scheduled = result[4] or 0
            avg_duration = result[5] or 0.0
            
            completion_rate = (completed / total * 100) if total > 0 else 0.0
            cancellation_rate = (cancelled / total * 100) if total > 0 else 0.0
            emergency_surgeries = 0
            
            statistics = StatisticsResponse(
                date=today,
                total_surgeries=total,
                completed_surgeries=completed,
                cancelled_surgeries=cancelled,
                emergency_surgeries=emergency_surgeries,
                in_progress_surgeries=in_progress,
                scheduled_surgeries=scheduled,
                average_duration=round(avg_duration, 2),
                completion_rate=round(completion_rate, 2),
                cancellation_rate=round(cancellation_rate, 2)
            )
            
            logger.info(f"获取今日统计成功: {today}")
            return statistics
            
    except Exception as e:
        logger.error(f"获取今日统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取今日统计失败")

@router.get("/date/{target_date}", response_model=StatisticsResponse)
async def get_statistics_by_date(target_date: str):
    """根据日期获取统计数据"""
    try:
        # 验证日期格式
        datetime.fromisoformat(target_date)
        
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_surgeries,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                    SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled,
                    AVG(CASE WHEN end_time IS NOT NULL AND status = 'completed'
                        THEN (julianday(end_time) - julianday(start_time)) * 24 * 60 
                        ELSE NULL END) as avg_duration
                FROM surgeries 
                WHERE DATE(start_time) = ?
            """, (target_date,))
            
            result = cursor.fetchone()
            
            total = result[0] or 0
            completed = result[1] or 0
            cancelled = result[2] or 0
            in_progress = result[3] or 0
            scheduled = result[4] or 0
            avg_duration = result[5] or 0.0
            
            completion_rate = (completed / total * 100) if total > 0 else 0.0
            cancellation_rate = (cancelled / total * 100) if total > 0 else 0.0
            emergency_surgeries = 0
            
            statistics = StatisticsResponse(
                date=target_date,
                total_surgeries=total,
                completed_surgeries=completed,
                cancelled_surgeries=cancelled,
                emergency_surgeries=emergency_surgeries,
                in_progress_surgeries=in_progress,
                scheduled_surgeries=scheduled,
                average_duration=round(avg_duration, 2),
                completion_rate=round(completion_rate, 2),
                cancellation_rate=round(cancellation_rate, 2)
            )
            
            logger.info(f"获取{target_date}统计成功")
            return statistics
            
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误")
    except Exception as e:
        logger.error(f"获取统计数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取统计数据失败")

@router.get("/chart/yesterday", response_model=ChartDataResponse)
async def get_yesterday_chart_data():
    """获取昨日统计图表数据"""
    try:
        yesterday = (date.today() - timedelta(days=1)).isoformat()
        
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT 
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                    SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled
                FROM surgeries 
                WHERE DATE(start_time) = ?
            """, (yesterday,))
            
            result = cursor.fetchone()
            
            completed = result[0] or 0
            cancelled = result[1] or 0
            in_progress = result[2] or 0
            scheduled = result[3] or 0
            
            chart_data = ChartDataResponse(
                labels=["已完成", "已取消", "进行中", "已安排"],
                datasets=[{
                    "label": "手术状态分布",
                    "data": [completed, cancelled, in_progress, scheduled],
                    "backgroundColor": [
                        "#43a047",  # 绿色 - 已完成
                        "#e53935",  # 红色 - 已取消
                        "#fb8c00",  # 橙色 - 进行中
                        "#1e88e5"   # 蓝色 - 已安排
                    ],
                    "borderWidth": 1
                }]
            )
            
            logger.info(f"获取昨日图表数据成功: {yesterday}")
            return chart_data
            
    except Exception as e:
        logger.error(f"获取昨日图表数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取昨日图表数据失败")

@router.get("/weekly", response_model=List[StatisticsResponse])
async def get_weekly_statistics():
    """获取最近7天的统计数据"""
    try:
        statistics_list = []
        
        for i in range(7):
            target_date = (date.today() - timedelta(days=i)).isoformat()
            
            with db_connection.get_db_context() as conn:
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_surgeries,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                        SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled,
                        AVG(CASE WHEN end_time IS NOT NULL AND status = 'completed'
                            THEN (julianday(end_time) - julianday(start_time)) * 24 * 60 
                            ELSE NULL END) as avg_duration
                    FROM surgeries 
                    WHERE DATE(start_time) = ?
                """, (target_date,))
                
                result = cursor.fetchone()
                
                total = result[0] or 0
                completed = result[1] or 0
                cancelled = result[2] or 0
                in_progress = result[3] or 0
                scheduled = result[4] or 0
                avg_duration = result[5] or 0.0
                
                completion_rate = (completed / total * 100) if total > 0 else 0.0
                cancellation_rate = (cancelled / total * 100) if total > 0 else 0.0
                
                statistics = StatisticsResponse(
                    date=target_date,
                    total_surgeries=total,
                    completed_surgeries=completed,
                    cancelled_surgeries=cancelled,
                    emergency_surgeries=0,
                    in_progress_surgeries=in_progress,
                    scheduled_surgeries=scheduled,
                    average_duration=round(avg_duration, 2),
                    completion_rate=round(completion_rate, 2),
                    cancellation_rate=round(cancellation_rate, 2)
                )
                
                statistics_list.append(statistics)
        
        # 按日期倒序排列（最新的在前）
        statistics_list.reverse()
        
        logger.info("获取周统计数据成功")
        return statistics_list

    except Exception as e:
        logger.error(f"获取周统计数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取周统计数据失败")

@router.get("/handover", response_model=Dict[str, Any])
async def get_handover_summary():
    """获取交班汇总信息"""
    try:
        handover_service = HandoverService()
        summary = handover_service.get_handover_summary()

        logger.info("获取交班汇总成功")
        return summary

    except Exception as e:
        logger.error(f"获取交班汇总失败: {e}")
        raise HTTPException(status_code=500, detail="获取交班汇总失败")

@router.get("/surgery-types")
async def get_surgery_type_distribution(days: int = 7):
    """获取手术类型分布统计"""
    try:
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)

        statistics_service = StatisticsService()
        distribution = statistics_service.get_surgery_distribution_by_type(start_date, end_date)

        logger.info(f"获取手术类型分布成功，时间范围: {start_date} 到 {end_date}")
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days
            },
            "distribution": distribution
        }

    except Exception as e:
        logger.error(f"获取手术类型分布失败: {e}")
        raise HTTPException(status_code=500, detail="获取手术类型分布失败")

@router.get("/surgeon-performance")
async def get_surgeon_performance(days: int = 30):
    """获取医生绩效统计"""
    try:
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)

        statistics_service = StatisticsService()
        performance = statistics_service.get_surgeon_performance(start_date, end_date)

        logger.info(f"获取医生绩效统计成功，时间范围: {start_date} 到 {end_date}")
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days
            },
            "performance": performance
        }

    except Exception as e:
        logger.error(f"获取医生绩效统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取医生绩效统计失败")

@router.get("/room-utilization")
async def get_room_utilization(days: int = 30):
    """获取手术间利用率统计"""
    try:
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)

        statistics_service = StatisticsService()
        utilization = statistics_service.get_room_utilization(start_date, end_date)

        logger.info(f"获取手术间利用率统计成功，时间范围: {start_date} 到 {end_date}")
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days
            },
            "utilization": utilization
        }

    except Exception as e:
        logger.error(f"获取手术间利用率统计失败: {e}")
        raise HTTPException(status_code=500, detail="获取手术间利用率统计失败")

@router.get("/trends")
async def get_trend_analysis(days: int = 30):
    """获取趋势分析数据"""
    try:
        statistics_service = StatisticsService()
        trends = statistics_service.get_trend_analysis(days)

        logger.info(f"获取趋势分析数据成功，分析天数: {days}")
        return trends

    except Exception as e:
        logger.error(f"获取趋势分析数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取趋势分析数据失败")

@router.get("/staff-schedule")
async def get_staff_schedule(target_date: str = None):
    """获取人员排班信息"""
    try:
        if target_date:
            target_date_obj = datetime.fromisoformat(target_date).date()
        else:
            target_date_obj = date.today()

        handover_service = HandoverService()
        schedule = handover_service.get_staff_schedule(target_date_obj)

        logger.info(f"获取人员排班信息成功: {target_date_obj}")
        return schedule

    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误")
    except Exception as e:
        logger.error(f"获取人员排班信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取人员排班信息失败")
