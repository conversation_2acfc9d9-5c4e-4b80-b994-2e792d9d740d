/* 全屏三段式布局样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    color: var(--theme-text-light);
    position: fixed; /* 确保完全固定，禁止滚动 */
    top: 0;
    left: 0;
}

/* 全屏容器 - 固定定位，禁止滚动 */
.fullscreen-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    background: var(--theme-background);
    position: fixed;
    top: 0;
    left: 0;
    overflow: hidden;
}

/* 标题栏 */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    background: var(--theme-glass);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--theme-border);
    min-height: 80px;
    z-index: 100;
}

.header-left {
    flex: 1;
    display: flex;
    align-items: center;
}

.header-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--theme-text-light);
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.8), 0 1px 3px rgba(0, 0, 0, 0.6);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 24px;
}

.header-time {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: #ffffff;
    background: var(--theme-glass);
    padding: 12px 24px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7), 0 1px 2px rgba(0, 0, 0, 0.5);
    border-radius: 8px;
    border: 1px solid var(--theme-border);
    text-align: center;
    white-space: nowrap;
}

.theme-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--theme-glass);
    padding: 8px 16px;
    border-radius: 8px;
    border: 1px solid var(--theme-border);
}

.theme-name {
    font-size: var(--font-size-sm);
    color: white;
    font-weight: 500;
}

/* 内容区 - 固定高度，禁止滚动 */
.content-area {
    flex: 1;
    padding: 40px;
    overflow: hidden; /* 禁止滚动 */
    position: relative;
    height: calc(100vh - 120px); /* 固定高度，减去头部和底部 */
}

/* 加载状态 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-content h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: 24px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.loading-content p {
    font-size: var(--font-size-base);
    color: rgba(255, 255, 255, 0.8);
    margin-top: 24px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态栏 */
.status-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 40px;
    background: var(--theme-glass);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--theme-border);
    min-height: 70px;
    z-index: 100;
    font-size: var(--font-size-base);
    color: var(--theme-text-light);
    font-weight: 500;
}

.status-left,
.status-center,
.status-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.status-center {
    justify-content: center;
    flex: 1;
}

.hospital-info {
    font-size: 14px;
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7), 0 1px 2px rgba(0, 0, 0, 0.5);
}

.page-indicator {
    font-size: 16px;
    color: #ffffff;
    font-weight: 700;
    background: var(--theme-primary);
    padding: 4px 12px;
    border-radius: 12px;
    min-width: 60px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4);
}

.auto-play-status,
.theme-status {
    font-size: 12px;
    color: #ffffff;
    background: var(--theme-glass);
    padding: 4px 8px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7), 0 1px 2px rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    border: 1px solid var(--theme-border);
}

/* 内容区域样式适配 */
.content-area .overview-section {
    margin-bottom: 40px;
}

.content-area .section-title {
    font-size: 24px;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 24px;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.8), 0 1px 3px rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    gap: 12px;
}

.content-area .section-title::before {
    content: '';
    width: 4px;
    height: 24px;
    background: var(--theme-primary);
    border-radius: 2px;
}

/* 卡片容器适配 */
.content-area .modern-grid-container {
    margin-bottom: 32px;
}

/* 内容区所有文字增强对比度 */
.content-area h1, .content-area h2, .content-area h3, .content-area h4, .content-area h5, .content-area h6 {
    color: #ffffff !important;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.8), 0 1px 3px rgba(0, 0, 0, 0.6) !important;
    font-weight: 700 !important;
}

.content-area p, .content-area span, .content-area div {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7), 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    font-weight: 500 !important;
}

.content-area .loading-content h2 {
    color: #ffffff !important;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.8), 0 1px 3px rgba(0, 0, 0, 0.6) !important;
    font-weight: 700 !important;
}

.content-area .loading-content p {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7), 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    font-weight: 500 !important;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .header-section {
        padding: 16px 32px;
    }
    
    .content-area {
        padding: 32px;
        height: calc(100vh - 100px); /* 调整固定高度 */
    }
    
    .status-section {
        padding: 10px 32px;
    }
    
    .header-title {
        font-size: 28px;
    }
}

@media (max-width: 1200px) {
    .header-section {
        padding: 12px 24px;
    }
    
    .content-area {
        padding: 24px;
        height: calc(100vh - 90px); /* 调整固定高度 */
    }
    
    .status-section {
        padding: 8px 24px;
    }
    
    .header-title {
        font-size: 24px;
    }
    
    .header-time {
        font-size: 16px;
        min-width: 150px;
    }
}

@media (max-width: 768px) {
    .header-section {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        padding: 16px;
    }
    
    .header-right {
        gap: 16px;
    }
    
    .content-area {
        padding: 16px;
        height: calc(100vh - 80px); /* 调整固定高度 */
    }
    
    .status-section {
        flex-direction: column;
        gap: 8px;
        padding: 12px 16px;
    }
    
    .status-left,
    .status-center,
    .status-right {
        justify-content: center;
    }
}

/* 不需要滚动条样式，因为内容固定不滚动 */

/* 动画效果 */
.header-section,
.content-area,
.status-section {
    animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 主题切换动画 */
.fullscreen-container {
    transition: background 0.5s ease;
}

.header-section,
.status-section {
    transition: background 0.5s ease, border-color 0.5s ease;
}

/* 确保全屏显示 */
.fullscreen-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}
