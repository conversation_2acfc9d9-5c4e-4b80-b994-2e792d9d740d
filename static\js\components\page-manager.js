/**
 * 分页管理器 - 管理交班大屏的多页面显示
 */

class PageManager {
    constructor() {
        this.pages = [];
        this.currentPageIndex = 0;
        this.autoPlayInterval = null;
        this.autoPlayDelay = 10000; // 10秒自动切换
        this.isAutoPlay = true;
        this.hospitalName = '';
        this.departmentName = '';
        this.dutyDoctor = '';
        this.dutyNurse = '';
        
        this.init();
    }
    
    /**
     * 初始化页面管理器
     */
    async init() {
        await this.loadSystemConfig();
        await this.loadData();
        this.setupPages();
        this.setupEventListeners();
        this.startAutoPlay();
        
        console.log('分页管理器初始化完成');
    }
    
    /**
     * 加载系统配置
     */
    async loadSystemConfig() {
        try {
            const response = await fetch('/api/config/system/info');
            const config = await response.json();
            
            this.hospitalName = config.hospital_name || '示例医院';
            this.departmentName = config.department_name || '麻醉科';
            this.dutyDoctor = config.duty_doctor || '值班医师';
            this.dutyNurse = config.duty_nurse || '值班护士';
            
        } catch (error) {
            console.error('加载系统配置失败:', error);
        }
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 并行加载所有需要的数据
            const [handoverData, todayStats, yesterdayStats, surgeries] = await Promise.all([
                this.fetchSimpleHandoverSummary(),
                this.fetchSimpleTodayStatistics(),
                this.fetchSimpleYesterdayStatistics(),
                this.fetchTodaySurgeries()
            ]);
            
            this.handoverData = handoverData;
            this.todayStats = todayStats;
            this.yesterdayStats = yesterdayStats;
            this.surgeries = surgeries;
            
        } catch (error) {
            console.error('加载数据失败:', error);
        }
    }
    
    /**
     * 设置页面
     */
    setupPages() {
        this.pages = [
            {
                id: 'cover',
                title: '早交班',
                generator: () => this.generateCoverPage()
            },
            {
                id: 'yesterday-stats',
                title: '昨日手术统计',
                generator: () => this.generateYesterdayStatsPage()
            },
            {
                id: 'today-stats',
                title: '今日手术情况',
                generator: () => this.generateTodayStatsPage()
            }
        ];
        
        // 添加特殊病例页面
        const specialCases = this.getSpecialCases();
        specialCases.forEach((surgery, index) => {
            this.pages.push({
                id: `special-case-${index}`,
                title: `特殊病例 - ${surgery.department}`,
                generator: () => this.generateSpecialCasePage(surgery)
            });
        });
        
        // 添加通知页面
        this.pages.push({
            id: 'notices',
            title: '通知事项',
            generator: () => this.generateNoticesPage()
        });
        
        console.log(`共生成 ${this.pages.length} 个页面`);
    }
    
    /**
     * 获取特殊病例
     */
    getSpecialCases() {
        if (!this.surgeries) return [];
        
        return this.surgeries.filter(surgery => 
            surgery.is_emergency || 
            (surgery.allergy_history && surgery.allergy_history.trim()) ||
            (surgery.special_situation && surgery.special_situation.trim())
        );
    }
    
    /**
     * 生成封面页
     */
    generateCoverPage() {
        const currentDate = new Date();
        const dateStr = currentDate.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        
        return `
            <div class="page-container cover-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName}</div>
                </div>
                
                <div class="page-content">
                    <div class="cover-title">
                        <h2>${dateStr} 早交班</h2>
                    </div>
                    
                    <div class="duty-info">
                        <div class="duty-item">
                            <span class="duty-label">值班医师：</span>
                            <span class="duty-name">${this.dutyDoctor}</span>
                        </div>
                        <div class="duty-item">
                            <span class="duty-label">值班护士：</span>
                            <span class="duty-name">${this.dutyNurse}</span>
                        </div>
                    </div>
                    
                    <div class="cover-stats">
                        <div class="stat-item">
                            <div class="stat-number">${this.yesterdayStats?.total_surgeries || 0}</div>
                            <div class="stat-label">昨日手术总量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${this.todayStats?.total_surgeries || 0}</div>
                            <div class="stat-label">今日计划手术</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${this.getSpecialCases().length}</div>
                            <div class="stat-label">特殊病例</div>
                        </div>
                    </div>
                </div>
                
                <div class="page-footer">
                    <div class="page-number">1 / ${this.pages.length}</div>
                    <div class="current-time">${currentDate.toLocaleTimeString('zh-CN')}</div>
                </div>
            </div>
        `;
    }
    
    /**
     * 生成昨日统计页面
     */
    generateYesterdayStatsPage() {
        const stats = this.yesterdayStats || {};
        const emergencyCount = this.getYesterdayEmergencyCount();
        const anesthesiaStats = this.getYesterdayAnesthesiaStats();
        
        return `
            <div class="page-container stats-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - 昨日手术统计</div>
                </div>
                
                <div class="page-content">
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-number">${stats.total_surgeries || 0}</div>
                            <div class="stat-label">总手术量</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-number">${emergencyCount}</div>
                            <div class="stat-label">急诊手术</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-number">${(stats.total_surgeries || 0) - emergencyCount}</div>
                            <div class="stat-label">择期手术</div>
                        </div>
                    </div>
                    
                    <div class="stats-detail">
                        <div class="stats-left">
                            <div class="stats-title">麻醉方式统计</div>
                            <div class="stats-items">
                                ${Object.entries(anesthesiaStats).map(([type, count]) => `
                                    <div class="stats-item">
                                        <span class="stats-item-name">${type}</span>
                                        <span class="stats-item-count">${count}例</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="stats-right">
                            <canvas id="yesterdayAnesthesiaChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="completion-rate">
                        <h3>完成率: ${stats.completion_rate || 0}%</h3>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${stats.completion_rate || 0}%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="page-footer">
                    <div class="page-number">2 / ${this.pages.length}</div>
                    <div class="current-time">${new Date().toLocaleTimeString('zh-CN')}</div>
                </div>
            </div>
        `;
    }
    
    /**
     * 生成今日统计页面
     */
    generateTodayStatsPage() {
        const stats = this.todayStats || {};
        const departmentStats = this.getTodayDepartmentStats();
        const anesthesiaStats = this.getTodayAnesthesiaStats();

        // 计算急诊手术数量
        const emergencyCount = this.todaySurgeries ?
            this.todaySurgeries.filter(surgery => surgery.is_emergency).length : 0;
        
        return `
            <div class="page-container stats-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - 今日手术情况</div>
                </div>
                
                <div class="page-content">
                    <div class="stats-grid">
                        <div class="stat-card primary">
                            <div class="stat-number">${stats.total_surgeries || 0}</div>
                            <div class="stat-label">总手术量</div>
                        </div>
                        <div class="stat-card warning">
                            <div class="stat-number">${emergencyCount}</div>
                            <div class="stat-label">急诊手术</div>
                        </div>
                        <div class="stat-card success">
                            <div class="stat-number">${(stats.total_surgeries || 0) - emergencyCount}</div>
                            <div class="stat-label">择期手术</div>
                        </div>
                    </div>
                    
                    <div class="stats-detail">
                        <div class="stats-left">
                            <div class="stats-title">分科室统计</div>
                            <div class="stats-items">
                                ${Object.entries(departmentStats).map(([dept, count]) => `
                                    <div class="stats-item">
                                        <span class="stats-item-name">${dept}</span>
                                        <span class="stats-item-count">${count}例</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="stats-right">
                            <canvas id="todayDepartmentChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="stats-detail">
                        <div class="stats-left">
                            <div class="stats-title">麻醉方式统计</div>
                            <div class="stats-items">
                                ${Object.entries(anesthesiaStats).map(([type, count]) => `
                                    <div class="stats-item">
                                        <span class="stats-item-name">${type}</span>
                                        <span class="stats-item-count">${count}例</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        <div class="stats-right">
                            <canvas id="todayAnesthesiaChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="page-footer">
                    <div class="page-number">3 / ${this.pages.length}</div>
                    <div class="current-time">${new Date().toLocaleTimeString('zh-CN')}</div>
                </div>
            </div>
        `;
    }
    
    /**
     * 生成特殊病例页面
     */
    generateSpecialCasePage(surgery) {
        const pageNumber = this.pages.findIndex(p => p.generator === arguments.callee) + 1;
        
        return `
            <div class="page-container patient-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - 特殊病例</div>
                </div>
                
                <div class="page-content">
                    <div class="patient-info">
                        <div class="patient-header">
                            <h2>${surgery.department}</h2>
                            ${surgery.is_emergency ? '<span class="emergency-badge">急诊</span>' : ''}
                        </div>
                        
                        <div class="patient-details">
                            <div class="detail-row">
                                <span class="detail-label">患者姓名：</span>
                                <span class="detail-value">${surgery.patient_name}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">年龄：</span>
                                <span class="detail-value">${surgery.age}岁</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">床号：</span>
                                <span class="detail-value">${surgery.bed_number}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">手术方式：</span>
                                <span class="detail-value">${surgery.surgery_type}</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">麻醉方式：</span>
                                <span class="detail-value">${surgery.anesthesia_type}</span>
                            </div>
                            
                            ${surgery.allergy_history ? `
                                <div class="detail-row highlight">
                                    <span class="detail-label">过敏史：</span>
                                    <span class="detail-value allergy">${surgery.allergy_history}</span>
                                </div>
                            ` : ''}
                            
                            ${surgery.special_situation ? `
                                <div class="detail-row highlight">
                                    <span class="detail-label">特殊情况：</span>
                                    <span class="detail-value special">${surgery.special_situation}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
                
                <div class="page-footer">
                    <div class="page-number">${pageNumber} / ${this.pages.length}</div>
                    <div class="current-time">${new Date().toLocaleTimeString('zh-CN')}</div>
                </div>
            </div>
        `;
    }
    
    /**
     * 生成通知页面
     */
    generateNoticesPage() {
        return `
            <div class="page-container notices-page">
                <div class="page-header">
                    <h1>${this.hospitalName}</h1>
                    <div class="page-subtitle">${this.departmentName} - 通知事项</div>
                </div>
                
                <div class="page-content">
                    <div class="notices-list">
                        <div class="notice-item">
                            <div class="notice-title">🔔 重要提醒</div>
                            <div class="notice-content">请注意患者过敏史，确保用药安全</div>
                        </div>
                        <div class="notice-item">
                            <div class="notice-title">📋 工作安排</div>
                            <div class="notice-content">今日急诊手术较多，请做好准备</div>
                        </div>
                        <div class="notice-item">
                            <div class="notice-title">⚠️ 安全提示</div>
                            <div class="notice-content">严格执行手术安全核查制度</div>
                        </div>
                    </div>
                </div>
                
                <div class="page-footer">
                    <div class="page-number">${this.pages.length} / ${this.pages.length}</div>
                    <div class="current-time">${new Date().toLocaleTimeString('zh-CN')}</div>
                </div>
            </div>
        `;
    }
    
    // 数据获取方法
    async fetchHandoverSummary() {
        const response = await fetch('/api/statistics/handover');
        return response.json();
    }

    async fetchTodayStatistics() {
        const response = await fetch('/api/statistics/today');
        return response.json();
    }

    async fetchYesterdayStatistics() {
        const response = await fetch('/api/statistics/yesterday');
        return response.json();
    }

    async fetchTodaySurgeries() {
        // 暂时返回模拟数据，因为API还有问题
        return [
            {
                id: 1,
                department: '普外科',
                patient_name: '张三',
                age: 45,
                bed_number: '15床',
                surgery_type: '阑尾切除术',
                anesthesia_type: '全身麻醉',
                allergy_history: '',
                special_situation: '',
                surgery_date: '2025-08-03',
                is_emergency: false,
                status: 'completed'
            },
            {
                id: 2,
                department: '肝胆外科',
                patient_name: '李四',
                age: 52,
                bed_number: '8床',
                surgery_type: '胆囊切除术',
                anesthesia_type: '全身麻醉',
                allergy_history: '青霉素过敏',
                special_situation: '',
                surgery_date: '2025-08-03',
                is_emergency: false,
                status: 'in_progress'
            },
            {
                id: 3,
                department: '骨科',
                patient_name: '王五',
                age: 38,
                bed_number: '12床',
                surgery_type: '骨折内固定术',
                anesthesia_type: '局部麻醉',
                allergy_history: '',
                special_situation: '高血压',
                surgery_date: '2025-08-03',
                is_emergency: true,
                status: 'scheduled'
            },
            {
                id: 4,
                department: '心外科',
                patient_name: '赵六',
                age: 65,
                bed_number: '3床',
                surgery_type: '心脏搭桥术',
                anesthesia_type: '全身麻醉',
                allergy_history: '碘过敏',
                special_situation: '糖尿病',
                surgery_date: '2025-08-04',
                is_emergency: false,
                status: 'scheduled'
            },
            {
                id: 5,
                department: '神经外科',
                patient_name: '钱七',
                age: 28,
                bed_number: '7床',
                surgery_type: '脑肿瘤切除术',
                anesthesia_type: '全身麻醉',
                allergy_history: '',
                special_situation: '癫痫病史',
                surgery_date: '2025-08-04',
                is_emergency: true,
                status: 'scheduled'
            }
        ];
    }

    // 简化的API方法
    async fetchSimpleHandoverSummary() {
        const response = await fetch('/api/statistics/simple-handover');
        return response.json();
    }

    async fetchSimpleTodayStatistics() {
        const response = await fetch('/api/statistics/simple-today');
        return response.json();
    }

    async fetchSimpleYesterdayStatistics() {
        const response = await fetch('/api/statistics/simple-yesterday');
        return response.json();
    }
    
    // 统计计算方法
    getYesterdayEmergencyCount() {
        // 这里应该从API获取，暂时返回模拟数据
        return 2;
    }
    
    getYesterdayAnesthesiaStats() {
        return {
            '全身麻醉': 8,
            '局部麻醉': 3,
            '腰硬联合麻醉': 2
        };
    }
    
    getTodayDepartmentStats() {
        if (!this.surgeries) return {};
        
        const stats = {};
        this.surgeries.forEach(surgery => {
            stats[surgery.department] = (stats[surgery.department] || 0) + 1;
        });
        return stats;
    }
    
    getTodayAnesthesiaStats() {
        if (!this.surgeries) return {};
        
        const stats = {};
        this.surgeries.forEach(surgery => {
            stats[surgery.anesthesia_type] = (stats[surgery.anesthesia_type] || 0) + 1;
        });
        return stats;
    }
    
    // 页面控制方法
    showPage(index) {
        if (index < 0 || index >= this.pages.length) return;

        this.currentPageIndex = index;
        const page = this.pages[index];
        const content = page.generator();

        document.getElementById('pageContainer').innerHTML = content;

        // 更新页面指示器
        this.updatePageIndicator();

        // 创建图表（延迟执行以确保DOM已渲染）
        setTimeout(() => {
            this.createPageCharts(page.id);
        }, 100);
    }
    
    nextPage() {
        const nextIndex = (this.currentPageIndex + 1) % this.pages.length;
        this.showPage(nextIndex);
    }
    
    prevPage() {
        const prevIndex = (this.currentPageIndex - 1 + this.pages.length) % this.pages.length;
        this.showPage(prevIndex);
    }
    
    startAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
        }
        
        if (this.isAutoPlay) {
            this.autoPlayInterval = setInterval(() => {
                this.nextPage();
            }, this.autoPlayDelay);
        }
    }
    
    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
    
    toggleAutoPlay() {
        this.isAutoPlay = !this.isAutoPlay;
        if (this.isAutoPlay) {
            this.startAutoPlay();
        } else {
            this.stopAutoPlay();
        }
    }
    
    updatePageIndicator() {
        const indicator = document.getElementById('pageIndicator');
        if (indicator) {
            indicator.textContent = `${this.currentPageIndex + 1} / ${this.pages.length}`;
        }
    }
    
    setupEventListeners() {
        // 键盘控制
        document.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowLeft':
                    this.prevPage();
                    break;
                case 'ArrowRight':
                case ' ':
                    this.nextPage();
                    break;
                case 'p':
                case 'P':
                    this.toggleAutoPlay();
                    break;
                case 'r':
                case 'R':
                    this.refresh();
                    break;
            }
        });
        
        // 鼠标点击控制
        document.addEventListener('click', (e) => {
            if (e.target.closest('.page-container')) {
                this.nextPage();
            }
        });
    }
    
    async refresh() {
        await this.loadData();
        this.setupPages();
        this.showPage(this.currentPageIndex);
    }

    /**
     * 创建页面图表
     */
    createPageCharts(pageId) {
        if (!window.chartManager) return;

        try {
            if (pageId === 'yesterday-stats') {
                this.createYesterdayCharts();
            } else if (pageId === 'today-stats') {
                this.createTodayCharts();
            }
        } catch (error) {
            console.error('创建图表失败:', error);
        }
    }

    /**
     * 创建昨日统计图表
     */
    createYesterdayCharts() {
        const anesthesiaStats = this.getYesterdayAnesthesiaStats();

        if (Object.keys(anesthesiaStats).length > 0) {
            const chartData = {
                labels: Object.keys(anesthesiaStats),
                values: Object.values(anesthesiaStats),
                colors: ['#1e88e5', '#43a047', '#fb8c00', '#e53935', '#9c27b0']
            };

            window.chartManager.createPieChart('yesterdayAnesthesiaChart', chartData);
        }
    }

    /**
     * 创建今日统计图表
     */
    createTodayCharts() {
        // 科室分布图表
        const departmentStats = this.getTodayDepartmentStats();
        if (Object.keys(departmentStats).length > 0) {
            const deptChartData = {
                labels: Object.keys(departmentStats),
                values: Object.values(departmentStats),
                colors: ['#1e88e5', '#43a047', '#fb8c00', '#e53935', '#9c27b0', '#ff5722', '#795548']
            };

            window.chartManager.createPieChart('todayDepartmentChart', deptChartData);
        }

        // 麻醉方式图表
        const anesthesiaStats = this.getTodayAnesthesiaStats();
        if (Object.keys(anesthesiaStats).length > 0) {
            const anesthesiaChartData = {
                labels: Object.keys(anesthesiaStats),
                values: Object.values(anesthesiaStats),
                colors: ['#1e88e5', '#43a047', '#fb8c00', '#e53935', '#9c27b0']
            };

            window.chartManager.createPieChart('todayAnesthesiaChart', anesthesiaChartData);
        }
    }
}

// 全局页面管理器实例
window.pageManager = new PageManager();
