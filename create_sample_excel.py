"""
创建示例Excel文件用于测试
"""
import pandas as pd
from datetime import datetime, timedelta
import os

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 示例数据
    sample_data = [
        {
            "科室": "普外科",
            "患者姓名": "张三",
            "年龄": 45,
            "床号": "15床",
            "手术方式": "阑尾切除术",
            "麻醉方式": "全身麻醉",
            "过敏史": "",
            "特殊情况": "",
            "手术日期": "2025-08-04",
            "是否急诊": "否",
            "状态": "已安排"
        },
        {
            "科室": "肝胆外科",
            "患者姓名": "李四",
            "年龄": 52,
            "床号": "8床",
            "手术方式": "胆囊切除术",
            "麻醉方式": "全身麻醉",
            "过敏史": "青霉素过敏",
            "特殊情况": "",
            "手术日期": "2025-08-04",
            "是否急诊": "否",
            "状态": "已安排"
        },
        {
            "科室": "骨科",
            "患者姓名": "王五",
            "年龄": 38,
            "床号": "12床",
            "手术方式": "骨折内固定术",
            "麻醉方式": "局部麻醉",
            "过敏史": "",
            "特殊情况": "高血压",
            "手术日期": "2025-08-04",
            "是否急诊": "是",
            "状态": "已安排"
        },
        {
            "科室": "心外科",
            "患者姓名": "赵六",
            "年龄": 65,
            "床号": "3床",
            "手术方式": "心脏搭桥术",
            "麻醉方式": "全身麻醉",
            "过敏史": "碘过敏",
            "特殊情况": "糖尿病",
            "手术日期": "2025-08-04",
            "是否急诊": "否",
            "状态": "已安排"
        },
        {
            "科室": "神经外科",
            "患者姓名": "钱七",
            "年龄": 28,
            "床号": "7床",
            "手术方式": "脑肿瘤切除术",
            "麻醉方式": "全身麻醉",
            "过敏史": "",
            "特殊情况": "癫痫病史",
            "手术日期": "2025-08-04",
            "是否急诊": "是",
            "状态": "已安排"
        },
        {
            "科室": "泌尿外科",
            "患者姓名": "孙八",
            "年龄": 55,
            "床号": "20床",
            "手术方式": "肾结石取石术",
            "麻醉方式": "腰硬联合麻醉",
            "过敏史": "磺胺过敏",
            "特殊情况": "",
            "手术日期": "2025-08-04",
            "是否急诊": "否",
            "状态": "已安排"
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data)
    
    # 确保data目录存在
    os.makedirs("data/uploads", exist_ok=True)
    
    # 保存为Excel文件
    excel_file = "data/uploads/sample_surgeries.xlsx"
    df.to_excel(excel_file, index=False, engine='openpyxl')
    
    print(f"示例Excel文件已创建: {excel_file}")
    print(f"包含 {len(sample_data)} 条示例数据")
    
    # 显示数据预览
    print("\n数据预览:")
    print(df.to_string(index=False))
    
    return excel_file

if __name__ == "__main__":
    create_sample_excel()
