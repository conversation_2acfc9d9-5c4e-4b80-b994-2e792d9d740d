"""
数据验证工具
"""
import re
from datetime import datetime
from typing import List, Any, Optional
import logging

logger = logging.getLogger(__name__)

def validate_surgery_data(data: dict) -> List[str]:
    """
    验证手术数据
    
    Args:
        data: 手术数据字典
        
    Returns:
        List[str]: 验证错误信息列表
    """
    errors = []
    
    # 验证患者姓名
    if not validate_patient_name(data.get('patient_name')):
        errors.append("患者姓名格式不正确")
    
    # 验证患者ID
    if not validate_patient_id(data.get('patient_id')):
        errors.append("患者ID格式不正确")
    
    # 验证手术类型
    if not validate_surgery_type(data.get('surgery_type')):
        errors.append("手术类型不能为空")
    
    # 验证麻醉方式
    if not validate_anesthesia_type(data.get('anesthesia_type')):
        errors.append("麻醉方式不能为空")
    
    # 验证医生姓名
    if not validate_doctor_name(data.get('surgeon')):
        errors.append("主刀医生姓名格式不正确")
    
    if not validate_doctor_name(data.get('anesthesiologist')):
        errors.append("麻醉医生姓名格式不正确")
    
    # 验证手术间号
    if not validate_room_number(data.get('room_number')):
        errors.append("手术间号格式不正确")
    
    # 验证时间
    start_time = data.get('start_time')
    end_time = data.get('end_time')
    
    if not validate_datetime(start_time):
        errors.append("开始时间格式不正确")
    
    if end_time and not validate_datetime(end_time):
        errors.append("结束时间格式不正确")
    
    # 验证时间逻辑
    if start_time and end_time:
        if isinstance(start_time, datetime) and isinstance(end_time, datetime):
            if end_time <= start_time:
                errors.append("结束时间必须晚于开始时间")
    
    return errors

def validate_patient_name(name: Any) -> bool:
    """验证患者姓名"""
    if not name or not isinstance(name, str):
        return False
    
    name = name.strip()
    if len(name) < 2 or len(name) > 50:
        return False
    
    # 检查是否包含有效字符（中文、英文、数字、空格）
    pattern = r'^[\u4e00-\u9fa5a-zA-Z0-9\s]+$'
    return bool(re.match(pattern, name))

def validate_patient_id(patient_id: Any) -> bool:
    """验证患者ID"""
    if not patient_id or not isinstance(patient_id, str):
        return False
    
    patient_id = patient_id.strip()
    if len(patient_id) < 1 or len(patient_id) > 50:
        return False
    
    # 检查是否包含有效字符（字母、数字、连字符、下划线）
    pattern = r'^[a-zA-Z0-9\-_]+$'
    return bool(re.match(pattern, patient_id))

def validate_surgery_type(surgery_type: Any) -> bool:
    """验证手术类型"""
    if not surgery_type or not isinstance(surgery_type, str):
        return False
    
    surgery_type = surgery_type.strip()
    return len(surgery_type) >= 2 and len(surgery_type) <= 200

def validate_anesthesia_type(anesthesia_type: Any) -> bool:
    """验证麻醉方式"""
    if not anesthesia_type or not isinstance(anesthesia_type, str):
        return False
    
    anesthesia_type = anesthesia_type.strip()
    return len(anesthesia_type) >= 2 and len(anesthesia_type) <= 100

def validate_doctor_name(name: Any) -> bool:
    """验证医生姓名"""
    if not name or not isinstance(name, str):
        return False
    
    name = name.strip()
    if len(name) < 2 or len(name) > 50:
        return False
    
    # 检查是否包含有效字符（中文、英文、空格）
    pattern = r'^[\u4e00-\u9fa5a-zA-Z\s]+$'
    return bool(re.match(pattern, name))

def validate_room_number(room_number: Any) -> bool:
    """验证手术间号"""
    if not room_number or not isinstance(room_number, str):
        return False
    
    room_number = room_number.strip()
    if len(room_number) < 1 or len(room_number) > 20:
        return False
    
    # 检查是否包含有效字符（中文、英文、数字、常用符号）
    pattern = r'^[\u4e00-\u9fa5a-zA-Z0-9\-_号间室]+$'
    return bool(re.match(pattern, room_number))

def validate_datetime(dt: Any) -> bool:
    """验证日期时间"""
    if not dt:
        return False
    
    if isinstance(dt, datetime):
        return True
    
    if isinstance(dt, str):
        try:
            # 尝试解析常见的日期时间格式
            datetime.strptime(dt.strip(), '%Y-%m-%d %H:%M:%S')
            return True
        except ValueError:
            try:
                datetime.strptime(dt.strip(), '%Y/%m/%d %H:%M:%S')
                return True
            except ValueError:
                try:
                    datetime.strptime(dt.strip(), '%Y-%m-%d')
                    return True
                except ValueError:
                    return False
    
    return False

def validate_phone_number(phone: Any) -> bool:
    """验证电话号码"""
    if not phone or not isinstance(phone, str):
        return False
    
    phone = phone.strip()
    # 中国手机号码格式
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))

def validate_email(email: Any) -> bool:
    """验证邮箱地址"""
    if not email or not isinstance(email, str):
        return False
    
    email = email.strip()
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_file_size(file_size: int, max_size: int = 10 * 1024 * 1024) -> bool:
    """
    验证文件大小
    
    Args:
        file_size: 文件大小（字节）
        max_size: 最大允许大小（字节），默认10MB
        
    Returns:
        bool: 是否有效
    """
    return 0 < file_size <= max_size

def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
    """
    验证文件扩展名
    
    Args:
        filename: 文件名
        allowed_extensions: 允许的扩展名列表
        
    Returns:
        bool: 是否有效
    """
    if not filename:
        return False
    
    file_ext = filename.lower().split('.')[-1]
    return f'.{file_ext}' in [ext.lower() for ext in allowed_extensions]

def sanitize_string(value: Any) -> str:
    """
    清理字符串，移除危险字符
    
    Args:
        value: 输入值
        
    Returns:
        str: 清理后的字符串
    """
    if not value:
        return ""
    
    # 转换为字符串
    text = str(value).strip()
    
    # 移除控制字符
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    
    return text

def validate_config_value(key: str, value: Any) -> bool:
    """
    验证配置值
    
    Args:
        key: 配置键
        value: 配置值
        
    Returns:
        bool: 是否有效
    """
    if not value:
        return False
    
    # 根据配置键类型进行验证
    if key in ['refresh_interval', 'display_rows']:
        try:
            int_value = int(value)
            return 1 <= int_value <= 3600  # 1秒到1小时
        except (ValueError, TypeError):
            return False
    
    elif key in ['hospital_name', 'department_name']:
        return isinstance(value, str) and 1 <= len(value.strip()) <= 100
    
    elif key == 'timezone':
        # 简单的时区验证
        return isinstance(value, str) and len(value.strip()) > 0
    
    else:
        # 默认验证：非空字符串
        return isinstance(value, str) and len(value.strip()) > 0
