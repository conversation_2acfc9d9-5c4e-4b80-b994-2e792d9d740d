/**
 * 图表管理组件
 */

class ChartManager {
    constructor() {
        this.charts = {};
        this.defaultColors = {
            primary: '#1e88e5',
            success: '#43a047',
            warning: '#fb8c00',
            error: '#e53935',
            info: '#039be5'
        };
    }
    
    /**
     * 创建饼图
     */
    createPieChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error(`Canvas element with id '${canvasId}' not found`);
            return null;
        }
        
        // 销毁现有图表
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }
        
        const defaultOptions = {
            type: 'doughnut',
            data: {
                labels: data.labels || [],
                datasets: [{
                    data: data.values || [],
                    backgroundColor: data.colors || [
                        this.defaultColors.success,
                        this.defaultColors.warning,
                        this.defaultColors.primary,
                        this.defaultColors.error
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                ...options
            }
        };
        
        this.charts[canvasId] = new Chart(ctx, defaultOptions);
        return this.charts[canvasId];
    }
    
    /**
     * 创建柱状图
     */
    createBarChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error(`Canvas element with id '${canvasId}' not found`);
            return null;
        }
        
        // 销毁现有图表
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }
        
        const defaultOptions = {
            type: 'bar',
            data: {
                labels: data.labels || [],
                datasets: data.datasets || []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#e0e0e0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                ...options
            }
        };
        
        this.charts[canvasId] = new Chart(ctx, defaultOptions);
        return this.charts[canvasId];
    }
    
    /**
     * 创建折线图
     */
    createLineChart(canvasId, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) {
            console.error(`Canvas element with id '${canvasId}' not found`);
            return null;
        }
        
        // 销毁现有图表
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }
        
        const defaultOptions = {
            type: 'line',
            data: {
                labels: data.labels || [],
                datasets: data.datasets || []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#e0e0e0'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                },
                ...options
            }
        };
        
        this.charts[canvasId] = new Chart(ctx, defaultOptions);
        return this.charts[canvasId];
    }
    
    /**
     * 更新图表数据
     */
    updateChart(canvasId, newData) {
        const chart = this.charts[canvasId];
        if (!chart) {
            console.error(`Chart with id '${canvasId}' not found`);
            return;
        }
        
        // 更新标签
        if (newData.labels) {
            chart.data.labels = newData.labels;
        }
        
        // 更新数据集
        if (newData.datasets) {
            chart.data.datasets = newData.datasets;
        } else if (newData.values) {
            // 简单数据更新
            chart.data.datasets[0].data = newData.values;
        }
        
        // 更新颜色
        if (newData.colors) {
            chart.data.datasets[0].backgroundColor = newData.colors;
        }
        
        chart.update('active');
    }
    
    /**
     * 销毁图表
     */
    destroyChart(canvasId) {
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
            delete this.charts[canvasId];
        }
    }
    
    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        Object.keys(this.charts).forEach(canvasId => {
            this.destroyChart(canvasId);
        });
    }
    
    /**
     * 获取图表实例
     */
    getChart(canvasId) {
        return this.charts[canvasId];
    }
    
    /**
     * 创建手术状态分布图表
     */
    createSurgeryStatusChart(canvasId, statusData) {
        const data = {
            labels: ['已完成', '进行中', '已安排', '已取消'],
            values: [
                statusData.completed || 0,
                statusData.in_progress || 0,
                statusData.scheduled || 0,
                statusData.cancelled || 0
            ],
            colors: [
                this.defaultColors.success,  // 已完成
                this.defaultColors.warning,  // 进行中
                this.defaultColors.primary,  // 已安排
                this.defaultColors.error     // 已取消
            ]
        };
        
        return this.createPieChart(canvasId, data);
    }
    
    /**
     * 创建趋势图表
     */
    createTrendChart(canvasId, trendData) {
        const data = {
            labels: trendData.dates || [],
            datasets: [{
                label: '手术数量',
                data: trendData.values || [],
                borderColor: this.defaultColors.primary,
                backgroundColor: this.defaultColors.primary + '20',
                fill: true
            }]
        };
        
        return this.createLineChart(canvasId, data);
    }
    
    /**
     * 创建医生绩效图表
     */
    createPerformanceChart(canvasId, performanceData) {
        const data = {
            labels: performanceData.doctors || [],
            datasets: [{
                label: '手术数量',
                data: performanceData.counts || [],
                backgroundColor: this.defaultColors.primary,
                borderColor: this.defaultColors.primary,
                borderWidth: 1
            }]
        };
        
        return this.createBarChart(canvasId, data);
    }
}

// 全局实例
window.chartManager = new ChartManager();
