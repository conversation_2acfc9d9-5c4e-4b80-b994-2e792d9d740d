/* 现代化三段式布局 - 自适应字体大小 */
/* 注意：CSS变量现在统一在 theme-variables.css 中定义 */

/* 布局特定变量 */
:root {
    /* 高度 - 固定px值 */
    --header-height: 120px;
    --status-height: 60px;

    /* 布局特定字体大小 */
    --xxl-font-size: 48px;
    --huge-font-size: 64px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--theme-background, linear-gradient(135deg, #667eea 0%, #764ba2 100%));
    color: var(--theme-text-light, white);
    font-size: var(--base-font-size);
}

/* 主容器 - 1920x1080 */
.page-container {
    width: 1920px;
    height: 1080px;
    position: relative;
    display: flex;
    flex-direction: column;
    background: var(--theme-background, linear-gradient(135deg, #1e3c72 0%, #2a5298 100%));
    transform-origin: top left;
}

/* 缩放容器 */
.scale-wrapper {
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--theme-background);
}

/* 标题栏 - 自适应高度 */
.header-bar {
    height: var(--header-height);
    background: var(--theme-header-bg, linear-gradient(90deg, #1a1a2e 0%, #16213e 50%, #1a1a2e 100%));
    border-bottom: 3px solid var(--theme-primary, #00d4ff);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    box-shadow: 0 4px 20px var(--theme-shadow, rgba(0, 212, 255, 0.3));
    flex-shrink: 0;
    backdrop-filter: blur(20px);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.header-logo {
    width: 80px;
    height: 80px;
    background: var(--theme-primary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--xl-font-size);
    font-weight: bold;
    color: var(--theme-text-light);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.header-title {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    gap: var(--spacing-md);
}

.header-title h1 {
    font-size: var(--xxl-font-size);
    font-weight: 700;
    color: var(--theme-text-light);
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.7), 0 1px 3px rgba(0, 0, 0, 0.5);
    line-height: 1.1;
}

.header-subtitle {
    font-size: var(--base-font-size);
    color: var(--theme-text-light);
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4);
    opacity: 0.95;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.header-time {
    text-align: right;
}

.datetime-display {
    font-size: var(--medium-font-size);
    font-weight: 600;
    color: var(--theme-text-light);
    font-family: 'Consolas', monospace;
    line-height: 1;
    white-space: nowrap;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4);
}

/* 内容区 - 自适应高度 */
.content-area {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    background: var(--theme-content-bg, rgba(255, 255, 255, 0.02));
}

/* 主要内容容器 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--theme-card-gradient, rgba(255, 255, 255, 0.05));
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-md);
    backdrop-filter: blur(20px);
    border: 1px solid var(--theme-border-light, rgba(255, 255, 255, 0.1));
    box-shadow: var(--theme-shadow, 0 8px 32px rgba(0, 0, 0, 0.3));
    min-height: 0;
    overflow: hidden;
}

/* 页面标题 */
.page-title {
    text-align: center;
    margin-bottom: var(--spacing-md);
    flex-shrink: 0;
}

.page-title h2 {
    font-size: var(--xl-font-size);
    font-weight: 700;
    background: var(--theme-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-xs);
    line-height: 1.1;
}

.page-subtitle {
    font-size: var(--base-font-size);
    color: var(--theme-text-muted);
    font-weight: 400;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
    margin: var(--spacing-sm) 0;
    flex-shrink: 0;
}

.stat-card {
    background: var(--theme-primary);
    border-radius: 24px;
    padding: var(--spacing-lg);
    text-align: left;
    border: none;
    box-shadow:
        0 8px 24px rgba(79, 172, 254, 0.2),
        0 4px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}





.stat-card.primary {
    background: var(--theme-primary);
    box-shadow:
        0 20px 40px rgba(102, 126, 234, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-card.warning {
    background: var(--theme-warning);
    box-shadow:
        0 8px 24px rgba(240, 147, 251, 0.2),
        0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-card.success {
    background: var(--theme-primary);
    box-shadow:
        0 8px 24px rgba(79, 172, 254, 0.2),
        0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-title {
    font-size: var(--large-font-size);
    font-weight: 700;
    color: var(--theme-text-light);
    margin-bottom: var(--spacing-xs);
    line-height: 1.2;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.stat-title .stat-icon {
    font-size: 1.5rem;
    opacity: 0.9;
    display: inline-flex;
    align-items: center;
}

.stat-description {
    font-size: var(--small-font-size);
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
    line-height: 1.4;
    margin-bottom: var(--spacing-md);
}

.stat-number {
    font-size: var(--huge-font-size);
    font-weight: 800;
    color: var(--theme-text-light);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--small-font-size);
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
}



/* 详细信息区域 */
.detail-section {
    display: flex;
    gap: var(--spacing-xl);
    flex: 1;
    min-height: 0;
    margin-top: var(--spacing-md);
}

.detail-left {
    flex: 2;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.detail-title {
    font-size: var(--xl-font-size);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--theme-primary);
    text-align: center;
    flex-shrink: 0;
}

.detail-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    flex: 1;
    align-content: start;
}

.detail-item {
    background: rgba(255, 255, 255, 0.08);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.detail-item:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-2px);
}

.detail-name {
    font-size: var(--medium-font-size);
    color: var(--theme-text-muted);
    font-weight: 500;
}

.detail-count {
    font-size: var(--large-font-size);
    font-weight: 700;
    color: var(--theme-primary);
}

.detail-right {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 300px;
}

/* 值班信息 */
.duty-section {
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
    margin: var(--spacing-sm) 0;
    flex-shrink: 0;
}

.duty-item {
    background: var(--theme-secondary);
    border-radius: 24px;
    padding: var(--spacing-lg);
    text-align: left;
    border: none;
    box-shadow:
        0 20px 40px rgba(168, 237, 234, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.1);
    min-width: 220px;
    min-height: 160px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}





.duty-title {
    font-size: var(--medium-font-size);
    font-weight: 700;
    color: var(--theme-text-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.2;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.duty-title .duty-icon {
    font-size: 1.2rem;
    opacity: 0.8;
    display: inline-flex;
    align-items: center;
}

.duty-name {
    font-size: var(--xl-font-size);
    font-weight: 800;
    color: var(--theme-text-primary);
    line-height: 1;
}

.duty-label {
    font-size: var(--small-font-size);
    color: rgba(45, 55, 72, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
}

/* 状态栏 - 自适应高度 */
.status-bar {
    height: var(--status-height);
    background: var(--theme-status-bg, linear-gradient(90deg, #1a1a2e 0%, #16213e 50%, #1a1a2e 100%));
    border-top: 2px solid var(--theme-primary, #00d4ff);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-lg);
    flex-shrink: 0;
    box-shadow: 0 -4px 20px var(--theme-shadow, rgba(0, 212, 255, 0.2));
    backdrop-filter: blur(20px);
}

.status-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--theme-success);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.status-text {
    font-size: var(--base-font-size);
    color: var(--theme-text-light);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4);
    font-weight: 600;
}

.status-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.page-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.page-number {
    font-size: var(--base-font-size);
    font-weight: 700;
    color: var(--theme-text-light);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6), 0 1px 2px rgba(0, 0, 0, 0.4);
    background: rgba(0, 212, 255, 0.1);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(0, 212, 255, 0.3);
}

.nav-hint {
    font-size: var(--small-font-size);
    color: var(--theme-text-muted);
}

/* 缩放适配 */
@media screen {
    .scale-wrapper {
        transform-origin: center center;
    }
}

/* 图表容器 */
canvas {
    max-width: 100%;
    max-height: 100%;
    border-radius: 12px;
}

/* 患者信息页面 */
.patient-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.patient-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(0, 212, 255, 0.3);
    flex-shrink: 0;
}

.patient-header h3 {
    font-size: var(--xxl-font-size);
    font-weight: 700;
    background: var(--theme-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.1;
}

.emergency-badge {
    background: var(--theme-error);
    color: var(--theme-text-light);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-size: var(--medium-font-size);
    font-weight: 700;
    animation: pulse-emergency 2s infinite;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
    text-transform: uppercase;
    letter-spacing: 1px;
}

@keyframes pulse-emergency {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}

.patient-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
    flex: 1;
    align-content: start;
}

.patient-row {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background: rgba(255, 255, 255, 0.08);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.patient-row:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-2px);
}

.patient-row.highlight {
    background: rgba(255, 193, 7, 0.15);
    border: 2px solid rgba(255, 193, 7, 0.5);
    animation: glow-warning 3s ease-in-out infinite alternate;
}

@keyframes glow-warning {
    from { box-shadow: 0 0 15px rgba(255, 193, 7, 0.3); }
    to { box-shadow: 0 0 25px rgba(255, 193, 7, 0.5); }
}

.patient-label {
    min-width: 160px;
    font-size: var(--medium-font-size);
    color: var(--theme-text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.patient-value {
    font-size: var(--large-font-size);
    font-weight: 600;
    color: var(--theme-text-light);
    flex: 1;
}

.patient-value.allergy {
    color: var(--theme-error);
    font-weight: 700;
}

.patient-value.special {
    color: var(--theme-warning);
    font-weight: 700;
}

/* 通知页面 */
.notices-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    flex: 1;
    min-height: 0;
}

.notice-item {
    background: rgba(255, 255, 255, 0.08);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg) var(--spacing-xl);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.notice-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--theme-primary);
}

.notice-item:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 8px 25px rgba(0, 212, 255, 0.15);
}

.notice-title {
    font-size: var(--large-font-size);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--theme-primary);
}

.notice-content {
    font-size: var(--medium-font-size);
    line-height: 1.6;
    color: var(--theme-text-muted);
}
