/**
 * 手术列表页面样式 - PPT全屏模式
 * 表格布局，适配大屏显示
 */

/* 手术列表页面主容器 - 固定内容，禁止滚动 */
.surgery-list-page {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 2vh 2vw;
    position: fixed;
    top: 0;
    left: 0;
}

/* 页面标题区域 */
.page-header {
    text-align: center;
    margin-bottom: 2vh;
    color: #ffffff;
}

.page-header h2 {
    font-size: 2.5vw;
    font-weight: 700;
    margin: 0 0 1vh 0;
    text-shadow: 0 0.3vw 0.6vw rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    font-size: 1.3vw;
    font-weight: 500;
    opacity: 0.9;
    text-shadow: 0 0.2vw 0.4vw rgba(0, 0, 0, 0.3);
}

/* 表格容器 - 固定高度，不滚动 */
.table-container {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1vw;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 1vw 3vw rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 15vh); /* 固定最大高度 */
}

/* 表格样式 */
.surgery-table {
    width: 100%;
    border-collapse: collapse;
    color: #ffffff;
    font-size: 1.1vw;
}

.surgery-table thead {
    background: rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 10;
}

.surgery-table th {
    padding: 2vh 1vw;
    text-align: left;
    font-weight: 600;
    font-size: 1.2vw;
    text-shadow: 0 0.1vw 0.2vw rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.surgery-table tbody {
    overflow: hidden; /* 禁止滚动 */
}

.surgery-table td {
    padding: 1.5vh 1vw;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    vertical-align: middle;
}

.surgery-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 0.5vh 1vw;
    border-radius: 0.5vw;
    font-size: 0.9vw;
    font-weight: 600;
    text-align: center;
    min-width: 4vw;
}

.status-badge.scheduled {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.8) 0%, rgba(0, 242, 254, 0.8) 100%);
    color: #ffffff;
}

.status-badge.in-progress {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.8) 0%, rgba(245, 87, 108, 0.8) 100%);
    color: #ffffff;
}

.status-badge.completed {
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.8) 0%, rgba(56, 249, 215, 0.8) 100%);
    color: #ffffff;
}

.status-badge.cancelled {
    background: rgba(128, 128, 128, 0.6);
    color: #ffffff;
}

/* 优先级标签 */
.priority-badge {
    display: inline-block;
    padding: 0.5vh 1vw;
    border-radius: 0.5vw;
    font-size: 0.9vw;
    font-weight: 600;
    text-align: center;
    min-width: 3vw;
}

.priority-badge.emergency {
    background: linear-gradient(135deg, rgba(245, 87, 108, 0.9) 0%, rgba(255, 69, 58, 0.9) 100%);
    color: #ffffff;
    animation: pulse 2s infinite;
}

.priority-badge.urgent {
    background: linear-gradient(135deg, rgba(255, 159, 10, 0.8) 0%, rgba(255, 204, 0, 0.8) 100%);
    color: #ffffff;
}

.priority-badge.routine {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(245, 87, 108, 0.7);
    }
    70% {
        box-shadow: 0 0 0 0.5vw rgba(245, 87, 108, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(245, 87, 108, 0);
    }
}

/* 时间列样式 */
.time-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

/* 医生姓名样式 */
.doctor-name {
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
}

/* 手术名称样式 */
.surgery-name {
    font-weight: 500;
    max-width: 15vw;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 科室名称样式 */
.department-name {
    font-size: 1vw;
    color: rgba(255, 255, 255, 0.8);
}

/* 不需要滚动条样式，因为内容固定不滚动 */

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 5vh 2vw;
    color: rgba(255, 255, 255, 0.7);
}

.empty-state .empty-icon {
    font-size: 4vw;
    margin-bottom: 2vh;
    opacity: 0.5;
}

.empty-state .empty-text {
    font-size: 1.5vw;
    font-weight: 500;
}

/* 确保在所有屏幕尺寸下保持相同的视觉效果 */
@media (max-aspect-ratio: 16/9) {
    .surgery-list-page {
        padding: 2vh 3vw;
    }
}

@media (min-aspect-ratio: 16/9) {
    .surgery-list-page {
        padding: 3vh 2vw;
    }
}
