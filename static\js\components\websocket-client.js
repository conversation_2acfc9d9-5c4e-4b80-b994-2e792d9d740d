/**
 * WebSocket客户端组件
 */

class WebSocketClient {
    constructor(url = null) {
        this.url = url || this.getWebSocketUrl();
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.heartbeatInterval = null;
        this.heartbeatTimeout = null;
        this.isConnected = false;
        this.messageHandlers = new Map();
        this.connectionListeners = [];
        
        // 绑定方法
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.send = this.send.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onOpen = this.onOpen.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
    }
    
    /**
     * 获取WebSocket URL
     */
    getWebSocketUrl() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        return `${protocol}//${host}/api/ws?client_type=dashboard&client_id=${this.generateClientId()}`;
    }
    
    /**
     * 生成客户端ID
     */
    generateClientId() {
        return 'dashboard-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 连接WebSocket
     */
    connect() {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            console.log('WebSocket已经连接');
            return;
        }
        
        try {
            console.log('正在连接WebSocket:', this.url);
            this.socket = new WebSocket(this.url);
            
            this.socket.onopen = this.onOpen;
            this.socket.onmessage = this.onMessage;
            this.socket.onclose = this.onClose;
            this.socket.onerror = this.onError;
            
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.scheduleReconnect();
        }
    }
    
    /**
     * 断开WebSocket连接
     */
    disconnect() {
        this.isConnected = false;
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
        
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        
        console.log('WebSocket连接已断开');
    }
    
    /**
     * 发送消息
     */
    send(message) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
            this.socket.send(messageStr);
            return true;
        } else {
            console.warn('WebSocket未连接，无法发送消息:', message);
            return false;
        }
    }
    
    /**
     * 连接打开事件
     */
    onOpen(event) {
        console.log('WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // 启动心跳
        this.startHeartbeat();
        
        // 订阅数据更新
        this.subscribe('statistics');
        this.subscribe('surgeries');
        
        // 通知连接监听器
        this.notifyConnectionListeners('connected');
        
        // 显示连接成功通知
        if (window.notification) {
            window.notification.success('实时连接已建立', 3000);
        }
    }
    
    /**
     * 消息接收事件
     */
    onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        } catch (error) {
            console.error('解析WebSocket消息失败:', error, event.data);
        }
    }
    
    /**
     * 连接关闭事件
     */
    onClose(event) {
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        this.isConnected = false;
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        // 通知连接监听器
        this.notifyConnectionListeners('disconnected');
        
        // 尝试重连
        if (event.code !== 1000) { // 非正常关闭
            this.scheduleReconnect();
        }
    }
    
    /**
     * 连接错误事件
     */
    onError(event) {
        console.error('WebSocket连接错误:', event);
        
        if (window.notification) {
            window.notification.error('实时连接出现问题', 5000);
        }
    }
    
    /**
     * 处理接收到的消息
     */
    handleMessage(data) {
        const messageType = data.type;
        
        switch (messageType) {
            case 'welcome':
                console.log('收到欢迎消息:', data.message);
                break;
                
            case 'heartbeat':
                this.handleHeartbeat(data);
                break;
                
            case 'update':
                this.handleUpdate(data);
                break;
                
            case 'notification':
                this.handleNotification(data);
                break;
                
            case 'current_data':
                this.handleCurrentData(data);
                break;
                
            case 'error':
                console.error('服务器错误:', data.message);
                if (window.notification) {
                    window.notification.error(data.message);
                }
                break;
                
            default:
                console.log('未知消息类型:', messageType, data);
        }
        
        // 调用注册的消息处理器
        if (this.messageHandlers.has(messageType)) {
            const handlers = this.messageHandlers.get(messageType);
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error('消息处理器执行失败:', error);
                }
            });
        }
    }
    
    /**
     * 处理心跳消息
     */
    handleHeartbeat(data) {
        // 更新服务器时间显示
        if (data.server_time) {
            const timeElement = document.querySelector('.header-time');
            if (timeElement) {
                timeElement.textContent = data.server_time;
            }
        }
        
        // 重置心跳超时
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
        }
        
        this.heartbeatTimeout = setTimeout(() => {
            console.warn('心跳超时，可能连接已断开');
            if (window.notification) {
                window.notification.warning('连接可能已断开，正在尝试重连...');
            }
        }, 60000); // 60秒超时
    }
    
    /**
     * 处理数据更新
     */
    handleUpdate(data) {
        console.log('收到数据更新:', data.update_type);
        
        // 触发全局数据刷新
        if (window.handoverApp) {
            window.handoverApp.loadInitialData();
        }
        
        if (window.notification) {
            window.notification.info('数据已更新', 2000);
        }
    }
    
    /**
     * 处理通知消息
     */
    handleNotification(data) {
        if (window.notification) {
            const level = data.level || 'info';
            window.notification[level](data.message, 5000);
        }
    }
    
    /**
     * 处理当前数据
     */
    handleCurrentData(data) {
        console.log('收到当前数据:', data.data_type);
        // 这里可以根据数据类型更新相应的界面组件
    }
    
    /**
     * 启动心跳
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            this.send({
                type: 'heartbeat',
                timestamp: new Date().toISOString()
            });
        }, 30000); // 30秒发送一次心跳
    }
    
    /**
     * 订阅数据更新
     */
    subscribe(subscriptionType) {
        this.send({
            type: 'subscribe',
            subscription_type: subscriptionType
        });
    }
    
    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('达到最大重连次数，停止重连');
            if (window.notification) {
                window.notification.error('无法建立实时连接，请刷新页面重试');
            }
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
        
        console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连`);
        
        setTimeout(() => {
            this.connect();
        }, delay);
    }
    
    /**
     * 注册消息处理器
     */
    onMessageType(messageType, handler) {
        if (!this.messageHandlers.has(messageType)) {
            this.messageHandlers.set(messageType, []);
        }
        this.messageHandlers.get(messageType).push(handler);
    }
    
    /**
     * 注册连接状态监听器
     */
    onConnectionChange(listener) {
        this.connectionListeners.push(listener);
    }
    
    /**
     * 通知连接监听器
     */
    notifyConnectionListeners(status) {
        this.connectionListeners.forEach(listener => {
            try {
                listener(status, this.isConnected);
            } catch (error) {
                console.error('连接监听器执行失败:', error);
            }
        });
    }
    
    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            url: this.url
        };
    }
}

// 全局WebSocket客户端实例
window.wsClient = new WebSocketClient();
