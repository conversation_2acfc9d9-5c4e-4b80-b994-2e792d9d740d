/**
 * 页面组件基类
 * 定义所有页面组件的标准接口和生命周期
 */

class BasePage {
    constructor(options = {}) {
        this.options = {
            autoRefresh: false,
            refreshInterval: 30000,
            ...options
        };
        
        this.data = {};
        this.template = '';
        this.isInitialized = false;
        this.refreshTimer = null;
        
        // 绑定方法上下文
        this.render = this.render.bind(this);
        this.refresh = this.refresh.bind(this);
        this.destroy = this.destroy.bind(this);
    }
    
    /**
     * 初始化页面组件
     * 子类应该重写此方法
     */
    async init() {
        if (this.isInitialized) return;
        
        console.log(`初始化页面组件: ${this.constructor.name}`);
        
        // 加载数据
        await this.loadData();
        
        // 设置自动刷新
        if (this.options.autoRefresh) {
            this.startAutoRefresh();
        }
        
        this.isInitialized = true;
    }
    
    /**
     * 加载页面数据
     * 子类必须实现此方法
     */
    async loadData() {
        throw new Error('loadData() 方法必须在子类中实现');
    }
    
    /**
     * 获取页面模板
     * 子类必须实现此方法
     */
    getTemplate() {
        throw new Error('getTemplate() 方法必须在子类中实现');
    }
    
    /**
     * 渲染页面
     * @returns {string} 渲染后的HTML字符串
     */
    async render() {
        if (!this.isInitialized) {
            await this.init();
        }
        
        // 获取模板
        const template = this.getTemplate();
        
        // 使用模板引擎渲染
        return this.renderTemplate(template, this.data);
    }
    
    /**
     * 模板渲染引擎
     * 简单的模板变量替换
     * @param {string} template 模板字符串
     * @param {object} data 数据对象
     * @returns {string} 渲染后的HTML
     */
    renderTemplate(template, data) {
        return template.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
            const value = this.getNestedValue(data, path);
            return value !== undefined ? value : match;
        });
    }
    
    /**
     * 获取嵌套对象的值
     * @param {object} obj 对象
     * @param {string} path 路径，如 'user.name'
     * @returns {any} 值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }
    
    /**
     * 刷新页面数据
     */
    async refresh() {
        console.log(`刷新页面数据: ${this.constructor.name}`);
        await this.loadData();
    }
    
    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }
        
        this.refreshTimer = setInterval(() => {
            this.refresh();
        }, this.options.refreshInterval);
        
        console.log(`启动自动刷新: ${this.options.refreshInterval}ms`);
    }
    
    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
            console.log('停止自动刷新');
        }
    }
    
    /**
     * 销毁页面组件
     */
    destroy() {
        this.stopAutoRefresh();
        this.isInitialized = false;
        this.data = {};
        console.log(`销毁页面组件: ${this.constructor.name}`);
    }
    
    /**
     * 获取页面标题
     * 子类可以重写此方法
     */
    getTitle() {
        return '页面标题';
    }
    
    /**
     * 获取页面类型
     * 子类应该重写此方法
     */
    getType() {
        return 'base';
    }
    
    /**
     * 页面激活时调用
     * 子类可以重写此方法
     */
    onActivate() {
        console.log(`页面激活: ${this.constructor.name}`);
    }
    
    /**
     * 页面失活时调用
     * 子类可以重写此方法
     */
    onDeactivate() {
        console.log(`页面失活: ${this.constructor.name}`);
    }
    
    /**
     * 处理键盘事件
     * 子类可以重写此方法
     * @param {KeyboardEvent} event 键盘事件
     */
    onKeyDown(event) {
        // 默认不处理任何键盘事件
    }
    
    /**
     * 获取页面配置
     * 子类可以重写此方法
     */
    getConfig() {
        return {
            title: this.getTitle(),
            type: this.getType(),
            autoRefresh: this.options.autoRefresh,
            refreshInterval: this.options.refreshInterval
        };
    }
}

// 全局导出
window.BasePage = BasePage;
