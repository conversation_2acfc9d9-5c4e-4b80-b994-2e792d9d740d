/**
 * 统计页组件
 * 显示手术统计信息
 */

class StatisticsPage extends BasePage {
    constructor(type = 'today', options = {}) {
        super({
            autoRefresh: true,
            refreshInterval: 60000, // 1分钟刷新一次
            ...options
        });
        
        this.statisticsType = type; // 'today' 或 'yesterday'
    }
    
    /**
     * 加载页面数据
     */
    async loadData() {
        try {
            // 根据类型获取不同的统计数据
            const stats = await this.getStatistics();
            const details = await this.getDetailedStats();
            
            this.data = {
                type: this.statisticsType,
                title: this.getPageTitle(),
                date: this.getDateString(),
                stats,
                details
            };
            
            console.log(`${this.statisticsType}统计页数据加载完成:`, this.data);
        } catch (error) {
            console.error(`${this.statisticsType}统计页数据加载失败:`, error);
            this.data = this.getDefaultData();
        }
    }
    
    /**
     * 获取昨日手术统计数据 - 简化版
     */
    async getStatistics() {
        try {
            const endpoint = this.statisticsType === 'today' ? '/api/stats/today' : '/api/stats/yesterday';
            const response = await fetch(endpoint);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            // 昨日手术统计的简化数据结构
            if (this.statisticsType === 'yesterday') {
                return {
                    total: data.total_surgeries || 24,
                    emergency: data.emergency_surgeries || 6,
                    elective: data.elective_surgeries || 18,
                    anesthesia: {
                        general: data.general_anesthesia || 15,
                        spinal: data.spinal_anesthesia || 6,
                        local: data.local_anesthesia || 3
                    }
                };
            }

            // 今日统计保持原有格式
            return [
                {
                    icon: '🏥',
                    number: data.total_surgeries || 0,
                    label: '计划手术',
                    type: 'primary'
                },
                {
                    icon: '⏳',
                    number: data.in_progress_surgeries || 0,
                    label: '进行中',
                    type: 'info'
                },
                {
                    icon: '✅',
                    number: data.completed_surgeries || 0,
                    label: '已完成',
                    type: 'success'
                }
            ];
        } catch (error) {
            console.error('获取统计数据失败:', error);
            return this.getDefaultStats();
        }
    }
    
    /**
     * 获取详细统计
     */
    async getDetailedStats() {
        // 模拟详细统计数据
        if (this.statisticsType === 'today') {
            return {
                departments: [
                    { name: '普外科', count: 6 },
                    { name: '骨科', count: 4 },
                    { name: '心胸外科', count: 3 },
                    { name: '神经外科', count: 2 },
                    { name: '泌尿外科', count: 2 },
                    { name: '妇产科', count: 1 }
                ],
                anesthesia: [
                    { type: '全身麻醉', count: 12, percentage: 65 },
                    { type: '椎管内麻醉', count: 4, percentage: 25 },
                    { type: '局部麻醉', count: 2, percentage: 10 }
                ]
            };
        } else {
            return {
                anesthesia: [
                    { type: '全身麻醉', count: 15 },
                    { type: '椎管内麻醉', count: 6 },
                    { type: '局部麻醉', count: 3 }
                ],
                special: [
                    { type: '困难气道', count: 2 },
                    { type: '术中低血压', count: 1 },
                    { type: '过敏反应', count: 0 }
                ]
            };
        }
    }
    
    /**
     * 获取默认统计数据
     */
    getDefaultStats() {
        if (this.statisticsType === 'today') {
            return [
                { icon: '🏥', number: 18, label: '计划手术', type: 'primary' },
                { icon: '⏳', number: 5, label: '进行中', type: 'info' },
                { icon: '✅', number: 8, label: '已完成', type: 'success' }
            ];
        } else {
            // 昨日手术统计默认数据
            return {
                total: 24,
                emergency: 6,
                elective: 18,
                anesthesia: {
                    general: 15,
                    spinal: 6,
                    local: 3
                }
            };
        }
    }

    /**
     * 创建增强版统计卡片
     */
    createEnhancedStatCard(config) {
        const { title, value, icon, gradient, subtitle } = config;

        return `
            <div class="stat-card" style="background: ${gradient};">
                <div class="stat-icon">${icon}</div>
                <div class="stat-value">${value}</div>
                <div class="stat-title">${title}</div>
                ${subtitle ? `<div class="stat-subtitle">${subtitle}</div>` : ''}
            </div>
        `;
    }

    /**
     * 创建昨日手术统计卡片
     */
    createStatisticsCards() {
        if (!window.cardComponents || this.statisticsType !== 'yesterday') {
            return [];
        }

        const stats = this.data.stats;
        const cards = [];

        // 总手术量卡片
        cards.push(this.createEnhancedStatCard({
            title: '总手术量',
            value: stats.total,
            icon: window.medicalIcons?.getIcon('surgery', 32, '#ffffff') || '🏥',
            gradient: 'var(--theme-primary-card-gradient)',
            subtitle: '昨日完成'
        }));

        // 急诊手术卡片
        cards.push(this.createEnhancedStatCard({
            title: '急诊手术',
            value: stats.emergency,
            icon: window.medicalIcons?.getIcon('emergency', 32, '#ffffff') || '🚨',
            gradient: 'var(--theme-warning-card-gradient)',
            subtitle: `占比 ${Math.round(stats.emergency / stats.total * 100)}%`
        }));

        // 择期手术卡片
        cards.push(this.createEnhancedStatCard({
            title: '择期手术',
            value: stats.elective,
            icon: window.medicalIcons?.getIcon('calendar', 32, '#ffffff') || '📅',
            gradient: 'var(--theme-success-card-gradient)',
            subtitle: `占比 ${Math.round(stats.elective / stats.total * 100)}%`
        }));

        // 全身麻醉卡片
        cards.push(this.createEnhancedStatCard({
            title: '全身麻醉',
            value: stats.anesthesia.general,
            icon: window.medicalIcons?.getIcon('anesthesia', 32, '#ffffff') || '💉',
            gradient: 'var(--theme-info-card-gradient)',
            subtitle: `占比 ${Math.round(stats.anesthesia.general / stats.total * 100)}%`
        }));

        // 椎管内麻醉卡片
        cards.push(this.createEnhancedStatCard({
            title: '椎管内麻醉',
            value: stats.anesthesia.spinal,
            icon: window.medicalIcons?.getIcon('spinal', 32, '#ffffff') || '🦴',
            gradient: 'var(--theme-secondary-card-gradient)',
            subtitle: `占比 ${Math.round(stats.anesthesia.spinal / stats.total * 100)}%`
        }));

        // 局部麻醉卡片
        cards.push(this.createEnhancedStatCard({
            title: '局部麻醉',
            value: stats.anesthesia.local,
            icon: window.medicalIcons?.getIcon('local', 32, '#ffffff') || '🎯',
            gradient: 'var(--theme-accent-card-gradient)',
            subtitle: `占比 ${Math.round(stats.anesthesia.local / stats.total * 100)}%`
        }));

        return cards;
    }

    /**
     * 创建统计图表
     */
    createCharts() {
        if (this.statisticsType !== 'yesterday' || !window.Chart) {
            return;
        }

        const stats = this.data.stats;

        // 延迟创建图表，确保DOM已渲染
        setTimeout(() => {
            this.createSurgeryTypeChart(stats);
            this.createAnesthesiaChart(stats);
        }, 100);
    }

    /**
     * 创建手术类型分布图表
     */
    createSurgeryTypeChart(stats) {
        const ctx = document.getElementById('surgery-type-chart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['急诊手术', '择期手术'],
                datasets: [{
                    data: [stats.emergency, stats.elective],
                    backgroundColor: [
                        'rgba(240, 147, 251, 0.8)',
                        'rgba(79, 172, 254, 0.8)'
                    ],
                    borderColor: [
                        'rgba(240, 147, 251, 1)',
                        'rgba(79, 172, 254, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#ffffff',
                            font: {
                                size: 14,
                                weight: '600'
                            },
                            padding: 20
                        }
                    }
                }
            }
        });
    }

    /**
     * 创建麻醉方式分布图表
     */
    createAnesthesiaChart(stats) {
        const ctx = document.getElementById('anesthesia-chart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['全身麻醉', '椎管内麻醉', '局部麻醉'],
                datasets: [{
                    data: [stats.anesthesia.general, stats.anesthesia.spinal, stats.anesthesia.local],
                    backgroundColor: [
                        'rgba(67, 233, 123, 0.8)',
                        'rgba(250, 112, 154, 0.8)',
                        'rgba(168, 237, 234, 0.8)'
                    ],
                    borderColor: [
                        'rgba(67, 233, 123, 1)',
                        'rgba(250, 112, 154, 1)',
                        'rgba(168, 237, 234, 1)'
                    ],
                    borderWidth: 2,
                    borderRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: '#ffffff',
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: '#ffffff',
                            font: {
                                size: 12,
                                weight: '600'
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    /**
     * 获取默认数据
     */
    getDefaultData() {
        return {
            type: this.statisticsType,
            title: this.getPageTitle(),
            date: this.getDateString(),
            stats: this.getDefaultStats(),
            details: this.getDetailedStats()
        };
    }
    
    /**
     * 获取页面标题
     */
    getPageTitle() {
        return this.statisticsType === 'today' ? '今日手术情况' : '昨日手术统计';
    }
    
    /**
     * 获取日期字符串
     */
    getDateString() {
        const date = new Date();
        if (this.statisticsType === 'yesterday') {
            date.setDate(date.getDate() - 1);
        }
        
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        
        return `${year}年${month}月${day}日`;
    }
    
    /**
     * 获取页面模板
     */
    getTemplate() {
        // 昨日统计使用卡片+图表布局
        if (this.statisticsType === 'yesterday') {
            return `
                <div class="stats-page-enhanced">
                    <div class="stats-main-grid">
                        <div class="stats-cards-section" id="stats-cards-container">
                            <!-- 统计卡片将在这里动态插入 -->
                        </div>
                        <div class="stats-charts-section">
                            <div class="chart-container">
                                <h3 class="chart-title">手术类型分布</h3>
                                <canvas id="surgery-type-chart"></canvas>
                            </div>
                            <div class="chart-container">
                                <h3 class="chart-title">麻醉方式分布</h3>
                                <canvas id="anesthesia-chart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 今日统计保持原有布局
        return `
            <div class="stats-page">
                <div class="page-header">
                    <h2>{{title}}</h2>
                    <div class="date-range">{{date}}</div>
                </div>

                <div class="stats-grid">
                    {{#each stats}}
                    <div class="stat-card {{this.type}}">
                        <div class="stat-icon">{{this.icon}}</div>
                        <div class="stat-content">
                            <div class="stat-number">{{formatNumber this.number}}</div>
                            <div class="stat-label">{{this.label}}</div>
                        </div>
                    </div>
                    {{/each}}
                </div>
                
                <div class="detail-sections">
                    {{#if details.departments}}
                    <div class="detail-section">
                        <h3>分科室统计</h3>
                        <div class="department-grid">
                            {{#each details.departments}}
                            <div class="dept-item">
                                <span class="dept-name">{{this.name}}</span>
                                <span class="dept-count">{{this.count}}例</span>
                            </div>
                            {{/each}}
                        </div>
                    </div>
                    {{/if}}
                    
                    {{#if details.anesthesia}}
                    <div class="detail-section">
                        <h3>麻醉方式{{#if type === 'today'}}分布{{else}}统计{{/if}}</h3>
                        <div class="{{#if details.anesthesia.0.percentage}}anesthesia-chart{{else}}detail-grid{{/if}}">
                            {{#each details.anesthesia}}
                            {{#if this.percentage}}
                            <div class="chart-item">
                                <div class="chart-bar" style="width: {{this.percentage}}%"></div>
                                <span class="chart-label">{{this.type}} ({{this.count}}例)</span>
                            </div>
                            {{else}}
                            <div class="detail-item">
                                <span class="detail-label">{{this.type}}</span>
                                <span class="detail-value">{{this.count}}例</span>
                            </div>
                            {{/if}}
                            {{/each}}
                        </div>
                    </div>
                    {{/if}}
                    
                    {{#if details.special}}
                    <div class="detail-section">
                        <h3>特殊情况</h3>
                        <div class="special-cases">
                            {{#each details.special}}
                            <div class="case-item">
                                <span class="case-type">{{this.type}}</span>
                                <span class="case-count">{{this.count}}例</span>
                            </div>
                            {{/each}}
                        </div>
                    </div>
                    {{/if}}
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染页面
     */
    async render() {
        if (!this.isInitialized) {
            await this.init();
        }

        const template = this.getTemplate();
        const html = window.templateEngine.render(template, this.data);

        // 昨日统计需要动态插入卡片和图表
        if (this.statisticsType === 'yesterday') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;

            if (window.cardComponents) {
                const cards = this.createStatisticsCards();
                const container = tempDiv.querySelector('#stats-cards-container');

                if (container && cards.length > 0) {
                    container.innerHTML = cards.join('');
                }
            }

            // 在DOM更新后创建图表
            setTimeout(() => {
                this.createCharts();
            }, 200);

            return tempDiv.innerHTML;
        }

        return html;
    }
    
    /**
     * 获取页面标题
     */
    getTitle() {
        return this.getPageTitle();
    }
    
    /**
     * 获取页面类型
     */
    getType() {
        return `statistics-${this.statisticsType}`;
    }
}

// 全局导出
window.StatisticsPage = StatisticsPage;
