/**
 * 标题页组件
 * 显示早交班信息和值班人员
 */

class TitlePage extends BasePage {
    constructor(options = {}) {
        super({
            autoRefresh: false,
            ...options
        });
    }
    
    /**
     * 加载页面数据
     */
    async loadData() {
        try {
            // 获取当前日期信息
            const now = new Date();
            const dateInfo = this.getDateInfo(now);
            
            // 获取值班人员信息（这里使用模拟数据，实际应该从API获取）
            const dutyInfo = await this.getDutyInfo();
            
            this.data = {
                hospital: {
                    name: 'XX医院麻醉科',
                    session: '早交班'
                },
                date: dateInfo,
                duty: dutyInfo
            };
            
            console.log('标题页数据加载完成:', this.data);
        } catch (error) {
            console.error('标题页数据加载失败:', error);
            // 使用默认数据
            this.data = this.getDefaultData();
        }
    }
    
    /**
     * 获取日期信息
     */
    getDateInfo(date) {
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const dayNames = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        const dayName = dayNames[date.getDay()];
        
        return {
            year,
            month,
            day,
            dayName,
            formatted: `${year}年${month}月${day}日`,
            full: `${year}年${month}月${day}日 ${dayName}`
        };
    }
    
    /**
     * 获取值班人员信息 - 简化为1医师+2护士
     */
    async getDutyInfo() {
        // 这里应该调用实际的API
        // const response = await fetch('/api/duty/current');
        // return await response.json();

        // 简化的模拟数据：1个医师 + 2个护士
        return {
            doctor: { role: '值班医师', name: '张主任' },
            nurses: [
                { role: '护士长', name: '陈护士长' },
                { role: '值班护士', name: '刘护师' }
            ]
        };
    }
    
    /**
     * 获取默认数据
     */
    getDefaultData() {
        const now = new Date();
        return {
            hospital: {
                name: 'XX医院麻醉科',
                session: '早交班'
            },
            date: this.getDateInfo(now),
            duty: {
                doctor: { role: '值班医师', name: '张主任' },
                nurses: [
                    { role: '护士长', name: '陈护士长' },
                    { role: '值班护士', name: '刘护师' }
                ]
            }
        };
    }
    
    /**
     * 创建人员卡片 - 1医师+2护士
     */
    createPersonCards() {
        if (!window.cardComponents) {
            console.error('卡片组件库未加载');
            return [];
        }

        const cards = [];

        // 创建医生卡片
        cards.push(window.cardComponents.createPersonCard({
            name: this.data.duty.doctor.name,
            role: this.data.duty.doctor.role,
            icon: window.medicalIcons?.getIcon('doctor', 24, '#ffffff') || '👨‍⚕️',
            status: 'active',
            gradient: 'var(--theme-doctor-card-gradient)'
        }));

        // 创建护士卡片
        this.data.duty.nurses.forEach((nurse, index) => {
            const gradientVar = index === 0 ? 'var(--theme-nurse-card-gradient)' : 'var(--theme-staff-card-gradient)';

            cards.push(window.cardComponents.createPersonCard({
                name: nurse.name,
                role: nurse.role,
                icon: window.medicalIcons?.getIcon('nurse', 24, '#ffffff') || '👩‍⚕️',
                status: 'active',
                gradient: gradientVar
            }));
        });

        return cards;
    }

    /**
     * 获取页面模板 - 简化版，无标题，只有卡片
     */
    getTemplate() {
        return `
            <div class="title-page-simple">
                <div class="duty-cards-full" id="duty-cards-container">
                    <!-- 所有人员卡片将在这里动态插入 -->
                </div>
            </div>
        `;
    }
    
    /**
     * 渲染页面 - 简化版
     */
    async render() {
        if (!this.isInitialized) {
            await this.init();
        }

        const template = this.getTemplate();
        const html = window.templateEngine.render(template, this.data);

        // 创建临时容器来解析HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // 插入所有卡片到单一容器
        if (window.cardComponents) {
            const cards = this.createPersonCards();
            const container = tempDiv.querySelector('#duty-cards-container');

            if (container && cards.length > 0) {
                container.innerHTML = cards.join('');
            }
        }

        return tempDiv.innerHTML;
    }
    
    /**
     * 获取页面标题
     */
    getTitle() {
        return '早交班信息';
    }
    
    /**
     * 获取页面类型
     */
    getType() {
        return 'title';
    }
    
    /**
     * 页面激活时调用
     */
    onActivate() {
        super.onActivate();
        // 可以在这里添加特定的激活逻辑
    }
    
    /**
     * 页面失活时调用
     */
    onDeactivate() {
        super.onDeactivate();
        // 可以在这里添加特定的失活逻辑
    }
    
    /**
     * 处理键盘事件
     */
    onKeyDown(event) {
        switch (event.key) {
            case 'r':
            case 'R':
                this.refresh();
                break;
            default:
                super.onKeyDown(event);
        }
    }
}

// 全局导出
window.TitlePage = TitlePage;
