"""
手术相关API路由
"""
from datetime import datetime, date
from typing import List, Optional, Dict, Any
import logging

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from src.models.surgery import Surgery, SurgeryStatus
from src.database.connection import db_connection
from src.services.handover_service import HandoverService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/surgeries", tags=["surgeries"])

class SurgeryResponse(BaseModel):
    """手术信息响应模型"""
    id: int
    patient_name: str
    patient_id: str
    surgery_type: str
    anesthesia_type: str
    surgeon: str
    anesthesiologist: str
    room_number: str
    start_time: str
    end_time: Optional[str] = None
    status: str
    notes: Optional[str] = None
    created_at: str
    updated_at: str

class SurgeryCreate(BaseModel):
    """创建手术信息请求模型"""
    patient_name: str
    patient_id: str
    surgery_type: str
    anesthesia_type: str
    surgeon: str
    anesthesiologist: str
    room_number: str
    start_time: str
    end_time: Optional[str] = None
    status: SurgeryStatus = SurgeryStatus.SCHEDULED
    notes: Optional[str] = None

@router.get("/today", response_model=List[SurgeryResponse])
async def get_today_surgeries():
    """获取今日手术安排"""
    try:
        today = date.today().isoformat()
        
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT * FROM surgeries 
                WHERE DATE(start_time) = ? 
                ORDER BY start_time
            """, (today,))
            
            rows = cursor.fetchall()
            surgeries = []
            
            for row in rows:
                surgery = Surgery.from_row(row)
                surgeries.append(SurgeryResponse(
                    id=surgery.id,
                    patient_name=surgery.patient_name,
                    patient_id=surgery.patient_id,
                    surgery_type=surgery.surgery_type,
                    anesthesia_type=surgery.anesthesia_type,
                    surgeon=surgery.surgeon,
                    anesthesiologist=surgery.anesthesiologist,
                    room_number=surgery.room_number,
                    start_time=surgery.start_time.isoformat(),
                    end_time=surgery.end_time.isoformat() if surgery.end_time else None,
                    status=surgery.status.value,
                    notes=surgery.notes,
                    created_at=surgery.created_at.isoformat(),
                    updated_at=surgery.updated_at.isoformat()
                ))
            
            logger.info(f"获取今日手术安排成功，共{len(surgeries)}条记录")
            return surgeries
            
    except Exception as e:
        logger.error(f"获取今日手术安排失败: {e}")
        raise HTTPException(status_code=500, detail="获取今日手术安排失败")

@router.get("/date/{target_date}", response_model=List[SurgeryResponse])
async def get_surgeries_by_date(target_date: str):
    """根据日期获取手术安排"""
    try:
        # 验证日期格式
        datetime.fromisoformat(target_date)
        
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT * FROM surgeries 
                WHERE DATE(start_time) = ? 
                ORDER BY start_time
            """, (target_date,))
            
            rows = cursor.fetchall()
            surgeries = []
            
            for row in rows:
                surgery = Surgery.from_row(row)
                surgeries.append(SurgeryResponse(
                    id=surgery.id,
                    patient_name=surgery.patient_name,
                    patient_id=surgery.patient_id,
                    surgery_type=surgery.surgery_type,
                    anesthesia_type=surgery.anesthesia_type,
                    surgeon=surgery.surgeon,
                    anesthesiologist=surgery.anesthesiologist,
                    room_number=surgery.room_number,
                    start_time=surgery.start_time.isoformat(),
                    end_time=surgery.end_time.isoformat() if surgery.end_time else None,
                    status=surgery.status.value,
                    notes=surgery.notes,
                    created_at=surgery.created_at.isoformat(),
                    updated_at=surgery.updated_at.isoformat()
                ))
            
            logger.info(f"获取{target_date}手术安排成功，共{len(surgeries)}条记录")
            return surgeries
            
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式错误")
    except Exception as e:
        logger.error(f"获取手术安排失败: {e}")
        raise HTTPException(status_code=500, detail="获取手术安排失败")

@router.get("/{surgery_id}", response_model=SurgeryResponse)
async def get_surgery(surgery_id: int):
    """获取单个手术信息"""
    try:
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("SELECT * FROM surgeries WHERE id = ?", (surgery_id,))
            row = cursor.fetchone()
            
            if not row:
                raise HTTPException(status_code=404, detail="手术信息不存在")
            
            surgery = Surgery.from_row(row)
            return SurgeryResponse(
                id=surgery.id,
                patient_name=surgery.patient_name,
                patient_id=surgery.patient_id,
                surgery_type=surgery.surgery_type,
                anesthesia_type=surgery.anesthesia_type,
                surgeon=surgery.surgeon,
                anesthesiologist=surgery.anesthesiologist,
                room_number=surgery.room_number,
                start_time=surgery.start_time.isoformat(),
                end_time=surgery.end_time.isoformat() if surgery.end_time else None,
                status=surgery.status.value,
                notes=surgery.notes,
                created_at=surgery.created_at.isoformat(),
                updated_at=surgery.updated_at.isoformat()
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取手术信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取手术信息失败")

@router.post("/", response_model=SurgeryResponse)
async def create_surgery(surgery_data: SurgeryCreate):
    """创建新的手术信息"""
    try:
        # 创建Surgery对象
        surgery = Surgery(
            patient_name=surgery_data.patient_name,
            patient_id=surgery_data.patient_id,
            surgery_type=surgery_data.surgery_type,
            anesthesia_type=surgery_data.anesthesia_type,
            surgeon=surgery_data.surgeon,
            anesthesiologist=surgery_data.anesthesiologist,
            room_number=surgery_data.room_number,
            start_time=datetime.fromisoformat(surgery_data.start_time),
            end_time=datetime.fromisoformat(surgery_data.end_time) if surgery_data.end_time else None,
            status=surgery_data.status,
            notes=surgery_data.notes
        )
        
        # 验证数据
        if not surgery.is_valid():
            errors = surgery.validate()
            raise HTTPException(status_code=400, detail=f"数据验证失败: {', '.join(errors)}")
        
        # 插入数据库
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                INSERT INTO surgeries (
                    patient_name, patient_id, surgery_type, anesthesia_type,
                    surgeon, anesthesiologist, room_number, start_time, end_time,
                    status, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                surgery.patient_name, surgery.patient_id, surgery.surgery_type,
                surgery.anesthesia_type, surgery.surgeon, surgery.anesthesiologist,
                surgery.room_number, surgery.start_time.isoformat(),
                surgery.end_time.isoformat() if surgery.end_time else None,
                surgery.status.value, surgery.notes
            ))
            
            surgery_id = cursor.lastrowid
            conn.commit()
            
            # 获取创建的记录
            cursor = conn.execute("SELECT * FROM surgeries WHERE id = ?", (surgery_id,))
            row = cursor.fetchone()
            created_surgery = Surgery.from_row(row)
            
            logger.info(f"创建手术信息成功，ID: {surgery_id}")
            
            return SurgeryResponse(
                id=created_surgery.id,
                patient_name=created_surgery.patient_name,
                patient_id=created_surgery.patient_id,
                surgery_type=created_surgery.surgery_type,
                anesthesia_type=created_surgery.anesthesia_type,
                surgeon=created_surgery.surgeon,
                anesthesiologist=created_surgery.anesthesiologist,
                room_number=created_surgery.room_number,
                start_time=created_surgery.start_time.isoformat(),
                end_time=created_surgery.end_time.isoformat() if created_surgery.end_time else None,
                status=created_surgery.status.value,
                notes=created_surgery.notes,
                created_at=created_surgery.created_at.isoformat(),
                updated_at=created_surgery.updated_at.isoformat()
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建手术信息失败: {e}")
        raise HTTPException(status_code=500, detail="创建手术信息失败")

class SurgeryStatusUpdate(BaseModel):
    """手术状态更新请求模型"""
    status: SurgeryStatus
    notes: Optional[str] = None

@router.put("/{surgery_id}/status", response_model=Dict[str, Any])
async def update_surgery_status(surgery_id: int, status_update: SurgeryStatusUpdate):
    """更新手术状态"""
    try:
        handover_service = HandoverService()
        success = handover_service.update_surgery_status(
            surgery_id,
            status_update.status,
            status_update.notes
        )

        if success:
            # 获取更新后的手术信息
            updated_surgery = await get_surgery(surgery_id)

            logger.info(f"手术状态更新成功: ID={surgery_id}, 状态={status_update.status.value}")
            return {
                "success": True,
                "message": "手术状态更新成功",
                "surgery": updated_surgery
            }
        else:
            raise HTTPException(status_code=400, detail="手术状态更新失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新手术状态失败: {e}")
        raise HTTPException(status_code=500, detail="更新手术状态失败")
