/**
 * 日期时间工具函数
 */

class DateTimeUtils {
    /**
     * 格式化完整时间显示
     * @param {Date} date 日期对象，默认为当前时间
     * @returns {string} 格式化后的时间字符串 "2025年08月03日 21:42:08 星期日"
     */
    static formatFullDateTime(date = new Date()) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        const dayNames = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        const dayName = dayNames[date.getDay()];
        
        return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds} ${dayName}`;
    }

    /**
     * 格式化日期
     * @param {Date} date 日期对象
     * @returns {string} 格式化后的日期字符串 "2025年08月03日"
     */
    static formatDate(date = new Date()) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        
        return `${year}年${month}月${day}日`;
    }

    /**
     * 格式化时间
     * @param {Date} date 日期对象
     * @returns {string} 格式化后的时间字符串 "21:42:08"
     */
    static formatTime(date = new Date()) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        
        return `${hours}:${minutes}:${seconds}`;
    }

    /**
     * 获取星期几
     * @param {Date} date 日期对象
     * @returns {string} 星期几 "星期日"
     */
    static getDayName(date = new Date()) {
        const dayNames = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        return dayNames[date.getDay()];
    }

    /**
     * 格式化日期和星期
     * @param {Date} date 日期对象
     * @returns {string} 格式化后的字符串 "2025年08月03日 星期日"
     */
    static formatDateWithDay(date = new Date()) {
        return `${this.formatDate(date)} ${this.getDayName(date)}`;
    }

    /**
     * 获取昨天的日期
     * @returns {Date} 昨天的日期对象
     */
    static getYesterday() {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return yesterday;
    }

    /**
     * 获取明天的日期
     * @returns {Date} 明天的日期对象
     */
    static getTomorrow() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow;
    }

    /**
     * 判断是否为今天
     * @param {Date} date 要判断的日期
     * @returns {boolean} 是否为今天
     */
    static isToday(date) {
        const today = new Date();
        return date.getFullYear() === today.getFullYear() &&
               date.getMonth() === today.getMonth() &&
               date.getDate() === today.getDate();
    }

    /**
     * 判断是否为昨天
     * @param {Date} date 要判断的日期
     * @returns {boolean} 是否为昨天
     */
    static isYesterday(date) {
        const yesterday = this.getYesterday();
        return date.getFullYear() === yesterday.getFullYear() &&
               date.getMonth() === yesterday.getMonth() &&
               date.getDate() === yesterday.getDate();
    }
}

// 全局导出
window.DateTimeUtils = DateTimeUtils;
