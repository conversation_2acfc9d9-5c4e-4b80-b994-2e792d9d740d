/**
 * 模板引擎
 * 支持数据绑定、条件渲染、循环渲染等功能
 */

class TemplateEngine {
    constructor() {
        this.templates = new Map();
        this.helpers = new Map();
        
        // 注册默认助手函数
        this.registerDefaultHelpers();
    }
    
    /**
     * 注册模板
     * @param {string} name 模板名称
     * @param {string} template 模板字符串
     */
    register(name, template) {
        this.templates.set(name, template);
    }
    
    /**
     * 渲染模板
     * @param {string} template 模板字符串或模板名称
     * @param {object} data 数据对象
     * @returns {string} 渲染后的HTML
     */
    render(template, data = {}) {
        // 如果是模板名称，获取注册的模板
        if (this.templates.has(template)) {
            template = this.templates.get(template);
        }
        
        // 创建渲染上下文
        const context = {
            ...data,
            $helpers: this.helpers
        };
        
        // 执行渲染
        return this.processTemplate(template, context);
    }
    
    /**
     * 处理模板
     * @param {string} template 模板字符串
     * @param {object} context 上下文对象
     * @returns {string} 处理后的HTML
     */
    processTemplate(template, context) {
        let result = template;
        
        // 处理条件渲染 {{#if condition}}...{{/if}}
        result = this.processConditionals(result, context);
        
        // 处理循环渲染 {{#each items}}...{{/each}}
        result = this.processLoops(result, context);
        
        // 处理变量替换 {{variable}}
        result = this.processVariables(result, context);
        
        // 处理助手函数 {{helper arg1 arg2}}
        result = this.processHelpers(result, context);
        
        return result;
    }
    
    /**
     * 处理条件渲染
     */
    processConditionals(template, context) {
        const conditionalRegex = /\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
        
        return template.replace(conditionalRegex, (match, condition, content) => {
            const value = this.evaluateExpression(condition.trim(), context);
            return value ? this.processTemplate(content, context) : '';
        });
    }
    
    /**
     * 处理循环渲染
     */
    processLoops(template, context) {
        const loopRegex = /\{\{#each\s+([^}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g;
        
        return template.replace(loopRegex, (match, arrayPath, content) => {
            const array = this.getNestedValue(context, arrayPath.trim());
            
            if (!Array.isArray(array)) {
                return '';
            }
            
            return array.map((item, index) => {
                const itemContext = {
                    ...context,
                    this: item,
                    '@index': index,
                    '@first': index === 0,
                    '@last': index === array.length - 1
                };
                return this.processTemplate(content, itemContext);
            }).join('');
        });
    }
    
    /**
     * 处理变量替换
     */
    processVariables(template, context) {
        const variableRegex = /\{\{([^#\/][^}]*)\}\}/g;
        
        return template.replace(variableRegex, (match, expression) => {
            const value = this.evaluateExpression(expression.trim(), context);
            return value !== undefined && value !== null ? String(value) : '';
        });
    }
    
    /**
     * 处理助手函数
     */
    processHelpers(template, context) {
        const helperRegex = /\{\{(\w+)\s+([^}]*)\}\}/g;
        
        return template.replace(helperRegex, (match, helperName, args) => {
            if (this.helpers.has(helperName)) {
                const helper = this.helpers.get(helperName);
                const argValues = this.parseArguments(args, context);
                return helper(...argValues);
            }
            return match;
        });
    }
    
    /**
     * 解析参数
     */
    parseArguments(argsString, context) {
        if (!argsString.trim()) return [];
        
        const args = argsString.split(/\s+/);
        return args.map(arg => {
            // 如果是字符串字面量
            if (arg.startsWith('"') && arg.endsWith('"')) {
                return arg.slice(1, -1);
            }
            // 如果是数字
            if (!isNaN(arg)) {
                return Number(arg);
            }
            // 否则作为变量路径
            return this.getNestedValue(context, arg);
        });
    }
    
    /**
     * 计算表达式
     */
    evaluateExpression(expression, context) {
        // 简单的表达式计算，支持变量访问
        return this.getNestedValue(context, expression);
    }
    
    /**
     * 获取嵌套对象的值
     */
    getNestedValue(obj, path) {
        if (!path) return obj;
        
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }
    
    /**
     * 注册助手函数
     * @param {string} name 助手函数名称
     * @param {function} helper 助手函数
     */
    registerHelper(name, helper) {
        this.helpers.set(name, helper);
    }
    
    /**
     * 注册默认助手函数
     */
    registerDefaultHelpers() {
        // 格式化数字
        this.registerHelper('formatNumber', (num) => {
            return Number(num).toLocaleString('zh-CN');
        });
        
        // 格式化日期
        this.registerHelper('formatDate', (date, format = 'YYYY-MM-DD') => {
            const d = new Date(date);
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            
            return format
                .replace('YYYY', year)
                .replace('MM', month)
                .replace('DD', day);
        });
        
        // 格式化时间
        this.registerHelper('formatTime', (date) => {
            return new Date(date).toLocaleTimeString('zh-CN', { hour12: false });
        });
        
        // 条件类名
        this.registerHelper('classIf', (condition, className) => {
            return condition ? className : '';
        });
        
        // 默认值
        this.registerHelper('default', (value, defaultValue) => {
            return value !== undefined && value !== null ? value : defaultValue;
        });
        
        // 截断文本
        this.registerHelper('truncate', (text, length = 50) => {
            if (!text) return '';
            return text.length > length ? text.substring(0, length) + '...' : text;
        });
    }
}

// 创建全局模板引擎实例
window.templateEngine = new TemplateEngine();
window.TemplateEngine = TemplateEngine;
