/**
 * 缩放管理器 - 处理固定布局的等比例缩放
 */
class ScaleManager {
    constructor() {
        this.designWidth = 1920;  // 设计宽度
        this.designHeight = 1080; // 设计高度
        this.container = null;
        this.scale = 1;
        
        this.init();
    }
    
    /**
     * 初始化缩放管理器
     */
    init() {
        this.createScaleContainer();
        this.updateScale();
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.updateScale();
        });
        
        // 监听方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.updateScale();
            }, 100);
        });
    }
    
    /**
     * 创建缩放容器
     */
    createScaleContainer() {
        // 查找现有的页面容器
        const pageContainer = document.getElementById('pageContainer');
        if (!pageContainer) {
            console.error('未找到页面容器');
            return;
        }
        
        // 创建缩放容器
        const scaleContainer = document.createElement('div');
        scaleContainer.className = 'scale-container';
        
        // 将页面容器移动到缩放容器中
        pageContainer.parentNode.insertBefore(scaleContainer, pageContainer);
        scaleContainer.appendChild(pageContainer);
        
        this.container = pageContainer;
        
        // 添加固定布局类
        pageContainer.classList.add('page-container');
    }
    
    /**
     * 更新缩放比例
     */
    updateScale() {
        if (!this.container) return;
        
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        
        // 计算缩放比例
        const scaleX = windowWidth / this.designWidth;
        const scaleY = windowHeight / this.designHeight;
        
        // 使用较小的缩放比例以确保完整显示
        this.scale = Math.min(scaleX, scaleY);
        
        // 应用缩放
        this.container.style.transform = `scale(${this.scale})`;
        
        // 计算居中位置
        const scaledWidth = this.designWidth * this.scale;
        const scaledHeight = this.designHeight * this.scale;
        
        const offsetX = (windowWidth - scaledWidth) / 2;
        const offsetY = (windowHeight - scaledHeight) / 2;
        
        // 设置容器位置
        this.container.style.transformOrigin = 'top left';
        this.container.style.left = `${offsetX}px`;
        this.container.style.top = `${offsetY}px`;
        
        console.log(`缩放比例: ${this.scale.toFixed(3)}, 窗口: ${windowWidth}x${windowHeight}, 设计: ${this.designWidth}x${this.designHeight}`);
    }
    
    /**
     * 获取当前缩放比例
     */
    getScale() {
        return this.scale;
    }
    
    /**
     * 将屏幕坐标转换为设计坐标
     */
    screenToDesign(screenX, screenY) {
        const rect = this.container.getBoundingClientRect();
        const designX = (screenX - rect.left) / this.scale;
        const designY = (screenY - rect.top) / this.scale;
        return { x: designX, y: designY };
    }
    
    /**
     * 将设计坐标转换为屏幕坐标
     */
    designToScreen(designX, designY) {
        const rect = this.container.getBoundingClientRect();
        const screenX = rect.left + designX * this.scale;
        const screenY = rect.top + designY * this.scale;
        return { x: screenX, y: screenY };
    }
    
    /**
     * 强制重新计算缩放
     */
    forceUpdate() {
        setTimeout(() => {
            this.updateScale();
        }, 50);
    }
    
    /**
     * 获取设计尺寸
     */
    getDesignSize() {
        return {
            width: this.designWidth,
            height: this.designHeight
        };
    }
    
    /**
     * 获取当前显示尺寸
     */
    getCurrentSize() {
        return {
            width: this.designWidth * this.scale,
            height: this.designHeight * this.scale
        };
    }
    
    /**
     * 检查是否为移动设备
     */
    isMobile() {
        return window.innerWidth < 768 || window.innerHeight < 600;
    }
    
    /**
     * 检查是否为平板设备
     */
    isTablet() {
        return window.innerWidth >= 768 && window.innerWidth < 1024;
    }
    
    /**
     * 检查是否为桌面设备
     */
    isDesktop() {
        return window.innerWidth >= 1024;
    }
    
    /**
     * 获取设备类型
     */
    getDeviceType() {
        if (this.isMobile()) return 'mobile';
        if (this.isTablet()) return 'tablet';
        return 'desktop';
    }
    
    /**
     * 添加调试信息
     */
    showDebugInfo() {
        const debugInfo = document.createElement('div');
        debugInfo.id = 'scale-debug';
        debugInfo.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            pointer-events: none;
        `;
        
        document.body.appendChild(debugInfo);
        
        const updateDebugInfo = () => {
            debugInfo.innerHTML = `
                设计尺寸: ${this.designWidth}x${this.designHeight}<br>
                窗口尺寸: ${window.innerWidth}x${window.innerHeight}<br>
                缩放比例: ${this.scale.toFixed(3)}<br>
                设备类型: ${this.getDeviceType()}<br>
                显示尺寸: ${Math.round(this.designWidth * this.scale)}x${Math.round(this.designHeight * this.scale)}
            `;
        };
        
        updateDebugInfo();
        
        // 定期更新调试信息
        setInterval(updateDebugInfo, 1000);
    }
    
    /**
     * 隐藏调试信息
     */
    hideDebugInfo() {
        const debugInfo = document.getElementById('scale-debug');
        if (debugInfo) {
            debugInfo.remove();
        }
    }
}

// 全局缩放管理器实例
window.scaleManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.scaleManager = new ScaleManager();
    
    // 开发模式下显示调试信息
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        // 按F12显示/隐藏调试信息
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F12') {
                e.preventDefault();
                const debugInfo = document.getElementById('scale-debug');
                if (debugInfo) {
                    window.scaleManager.hideDebugInfo();
                } else {
                    window.scaleManager.showDebugInfo();
                }
            }
        });
    }
});

// 导出缩放管理器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ScaleManager;
}
