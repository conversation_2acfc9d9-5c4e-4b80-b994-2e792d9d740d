-- 麻醉科交班大屏软件数据库结构
-- 创建时间: 2025-08-03

-- 手术信息表 (科室, 患者姓名, 年龄, 床号, 手术方式, 麻醉方式, 过敏史, 特殊情况, 手术日期, 是否急诊, 状态, 创建时间, 更新时间)
CREATE TABLE IF NOT EXISTS surgeries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    department VARCHAR(100) NOT NULL,
    patient_name VARCHAR(100) NOT NULL,
    age INTEGER NOT NULL,
    bed_number VARCHAR(20) NOT NULL,
    surgery_type VARCHAR(200) NOT NULL,
    anesthesia_type VARCHAR(100) NOT NULL,
    allergy_history TEXT DEFAULT '',
    special_situation TEXT DEFAULT '',
    surgery_date DATE NOT NULL,
    is_emergency INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'scheduled', -- scheduled/in_progress/completed/cancelled
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 统计数据表 (统计日期, 总手术数, 已完成手术数, 已取消手术数, 急诊手术数, 平均手术时长, 创建时间)
CREATE TABLE IF NOT EXISTS statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    total_surgeries INTEGER DEFAULT 0,
    completed_surgeries INTEGER DEFAULT 0,
    cancelled_surgeries INTEGER DEFAULT 0,
    emergency_surgeries INTEGER DEFAULT 0,
    average_duration DECIMAL(5,2),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date)
);

-- 系统配置表 (配置键, 配置值, 配置描述, 创建时间, 更新时间)
CREATE TABLE IF NOT EXISTS configurations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文件上传记录表 (文件名, 原始文件名, 文件路径, 文件大小, 上传时间, 处理时间, 状态, 错误信息, 导入记录数)
CREATE TABLE IF NOT EXISTS upload_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_time DATETIME,
    status VARCHAR(20) DEFAULT 'uploaded', -- uploaded/processing/completed/failed
    error_message TEXT,
    imported_count INTEGER DEFAULT 0
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_surgeries_date ON surgeries(surgery_date);
CREATE INDEX IF NOT EXISTS idx_surgeries_status ON surgeries(status);
CREATE INDEX IF NOT EXISTS idx_surgeries_department ON surgeries(department);
CREATE INDEX IF NOT EXISTS idx_surgeries_emergency ON surgeries(is_emergency);
CREATE INDEX IF NOT EXISTS idx_statistics_date ON statistics(date);
CREATE INDEX IF NOT EXISTS idx_upload_records_status ON upload_records(status);

-- 插入默认配置数据
INSERT OR IGNORE INTO configurations (config_key, config_value, description) VALUES
('hospital_name', '示例医院', '医院名称'),
('department_name', '麻醉科', '科室名称'),
('duty_doctor', '李医生', '值班医师'),
('duty_nurse', '张护士', '值班护士'),
('refresh_interval', '30', '页面刷新间隔(秒)'),
('display_rows', '10', '表格显示行数'),
('chart_colors', '["#1e88e5", "#43a047", "#e53935", "#fb8c00", "#039be5"]', '图表颜色配置'),
('timezone', 'Asia/Shanghai', '时区设置');

-- 插入示例数据（可选）
INSERT OR IGNORE INTO surgeries (
    department, patient_name, age, bed_number, surgery_type, anesthesia_type,
    allergy_history, special_situation, surgery_date, is_emergency, status
) VALUES
('普外科', '张三', 45, '15床', '阑尾切除术', '全身麻醉', '', '', '2025-08-03', 0, 'completed'),
('肝胆外科', '李四', 52, '8床', '胆囊切除术', '全身麻醉', '青霉素过敏', '', '2025-08-03', 0, 'in_progress'),
('骨科', '王五', 38, '12床', '骨折内固定术', '局部麻醉', '', '高血压', '2025-08-03', 1, 'scheduled'),
('心外科', '赵六', 65, '3床', '心脏搭桥术', '全身麻醉', '碘过敏', '糖尿病', '2025-08-04', 0, 'scheduled'),
('神经外科', '钱七', 28, '7床', '脑肿瘤切除术', '全身麻醉', '', '癫痫病史', '2025-08-04', 1, 'scheduled');
