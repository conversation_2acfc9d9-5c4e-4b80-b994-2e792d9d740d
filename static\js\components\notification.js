/**
 * 通知组件
 */

class NotificationManager {
    constructor() {
        this.container = null;
        this.notifications = [];
        this.maxNotifications = 5;
        this.defaultDuration = 5000; // 5秒
        
        this.init();
    }
    
    /**
     * 初始化通知容器
     */
    init() {
        // 创建通知容器
        this.container = document.createElement('div');
        this.container.className = 'notification-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            pointer-events: none;
        `;
        
        document.body.appendChild(this.container);
    }
    
    /**
     * 显示通知
     */
    show(message, type = 'info', duration = null) {
        const notification = this.createNotification(message, type, duration);
        this.addNotification(notification);
        
        return notification;
    }
    
    /**
     * 显示成功通知
     */
    success(message, duration = null) {
        return this.show(message, 'success', duration);
    }
    
    /**
     * 显示错误通知
     */
    error(message, duration = null) {
        return this.show(message, 'error', duration);
    }
    
    /**
     * 显示警告通知
     */
    warning(message, duration = null) {
        return this.show(message, 'warning', duration);
    }
    
    /**
     * 显示信息通知
     */
    info(message, duration = null) {
        return this.show(message, 'info', duration);
    }
    
    /**
     * 创建通知元素
     */
    createNotification(message, type, duration) {
        const notification = document.createElement('div');
        const id = 'notification-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
        
        notification.id = id;
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 12px;
            padding: 16px;
            border-left: 4px solid ${this.getTypeColor(type)};
            transform: translateX(100%);
            transition: all 0.3s ease;
            pointer-events: auto;
            position: relative;
            overflow: hidden;
        `;
        
        // 创建内容
        const content = document.createElement('div');
        content.style.cssText = `
            display: flex;
            align-items: flex-start;
            gap: 12px;
        `;
        
        // 图标
        const icon = document.createElement('div');
        icon.style.cssText = `
            font-size: 20px;
            line-height: 1;
            flex-shrink: 0;
        `;
        icon.textContent = this.getTypeIcon(type);
        
        // 消息文本
        const messageEl = document.createElement('div');
        messageEl.style.cssText = `
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
        `;
        messageEl.textContent = message;
        
        // 关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #999;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s ease;
            flex-shrink: 0;
        `;
        closeBtn.innerHTML = '×';
        closeBtn.addEventListener('click', () => {
            this.removeNotification(id);
        });
        
        closeBtn.addEventListener('mouseenter', () => {
            closeBtn.style.backgroundColor = '#f0f0f0';
        });
        
        closeBtn.addEventListener('mouseleave', () => {
            closeBtn.style.backgroundColor = 'transparent';
        });
        
        content.appendChild(icon);
        content.appendChild(messageEl);
        content.appendChild(closeBtn);
        notification.appendChild(content);
        
        // 进度条（如果有持续时间）
        const finalDuration = duration !== null ? duration : this.defaultDuration;
        if (finalDuration > 0) {
            const progressBar = document.createElement('div');
            progressBar.style.cssText = `
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background-color: ${this.getTypeColor(type)};
                width: 100%;
                transform-origin: left;
                animation: notification-progress ${finalDuration}ms linear forwards;
            `;
            
            notification.appendChild(progressBar);
            
            // 添加CSS动画
            if (!document.getElementById('notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                    @keyframes notification-progress {
                        from { transform: scaleX(1); }
                        to { transform: scaleX(0); }
                    }
                `;
                document.head.appendChild(style);
            }
            
            // 自动移除
            setTimeout(() => {
                this.removeNotification(id);
            }, finalDuration);
        }
        
        return {
            id,
            element: notification,
            type,
            message,
            duration: finalDuration
        };
    }
    
    /**
     * 添加通知到容器
     */
    addNotification(notification) {
        // 限制通知数量
        while (this.notifications.length >= this.maxNotifications) {
            const oldest = this.notifications.shift();
            this.removeNotificationElement(oldest.id);
        }
        
        this.notifications.push(notification);
        this.container.appendChild(notification.element);
        
        // 触发动画
        setTimeout(() => {
            notification.element.style.transform = 'translateX(0)';
        }, 10);
    }
    
    /**
     * 移除通知
     */
    removeNotification(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification) {
            this.removeNotificationElement(id);
            this.notifications = this.notifications.filter(n => n.id !== id);
        }
    }
    
    /**
     * 移除通知元素
     */
    removeNotificationElement(id) {
        const element = document.getElementById(id);
        if (element) {
            element.style.transform = 'translateX(100%)';
            element.style.opacity = '0';
            
            setTimeout(() => {
                if (element.parentNode) {
                    element.parentNode.removeChild(element);
                }
            }, 300);
        }
    }
    
    /**
     * 清除所有通知
     */
    clearAll() {
        this.notifications.forEach(notification => {
            this.removeNotificationElement(notification.id);
        });
        this.notifications = [];
    }
    
    /**
     * 获取类型颜色
     */
    getTypeColor(type) {
        const colors = {
            success: '#43a047',
            error: '#e53935',
            warning: '#fb8c00',
            info: '#1e88e5'
        };
        return colors[type] || colors.info;
    }
    
    /**
     * 获取类型图标
     */
    getTypeIcon(type) {
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        return icons[type] || icons.info;
    }
    
    /**
     * 设置最大通知数量
     */
    setMaxNotifications(max) {
        this.maxNotifications = max;
    }
    
    /**
     * 设置默认持续时间
     */
    setDefaultDuration(duration) {
        this.defaultDuration = duration;
    }
}

// 全局实例
window.notification = new NotificationManager();
