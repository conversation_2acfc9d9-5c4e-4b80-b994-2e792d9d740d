"""
配置管理相关API路由
"""
from typing import List, Dict, Any
import logging

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from src.database.connection import db_connection

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/config", tags=["config"])

class ConfigResponse(BaseModel):
    """配置响应模型"""
    id: int
    config_key: str
    config_value: str
    description: str = None
    created_at: str
    updated_at: str

class ConfigUpdate(BaseModel):
    """配置更新请求模型"""
    config_value: str

class SystemInfo(BaseModel):
    """系统信息响应模型"""
    hospital_name: str
    department_name: str
    refresh_interval: int
    display_rows: int
    timezone: str
    version: str = "1.0.0"

@router.get("/", response_model=List[ConfigResponse])
async def get_all_configs():
    """获取所有配置"""
    try:
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT id, config_key, config_value, description, 
                       created_at, updated_at
                FROM configurations 
                ORDER BY config_key
            """)
            
            configs = []
            for row in cursor.fetchall():
                configs.append(ConfigResponse(
                    id=row[0],
                    config_key=row[1],
                    config_value=row[2],
                    description=row[3],
                    created_at=row[4],
                    updated_at=row[5]
                ))
            
            logger.info(f"获取所有配置成功，共{len(configs)}条")
            return configs
            
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取配置失败")

@router.get("/{config_key}", response_model=ConfigResponse)
async def get_config(config_key: str):
    """获取单个配置"""
    try:
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT id, config_key, config_value, description, 
                       created_at, updated_at
                FROM configurations 
                WHERE config_key = ?
            """, (config_key,))
            
            row = cursor.fetchone()
            if not row:
                raise HTTPException(status_code=404, detail="配置不存在")
            
            config = ConfigResponse(
                id=row[0],
                config_key=row[1],
                config_value=row[2],
                description=row[3],
                created_at=row[4],
                updated_at=row[5]
            )
            
            logger.info(f"获取配置成功: {config_key}")
            return config
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取配置失败")

@router.put("/{config_key}", response_model=ConfigResponse)
async def update_config(config_key: str, config_data: ConfigUpdate):
    """更新配置"""
    try:
        with db_connection.get_db_context() as conn:
            # 检查配置是否存在
            cursor = conn.execute(
                "SELECT id FROM configurations WHERE config_key = ?", 
                (config_key,)
            )
            
            if not cursor.fetchone():
                raise HTTPException(status_code=404, detail="配置不存在")
            
            # 更新配置
            cursor = conn.execute("""
                UPDATE configurations 
                SET config_value = ?, updated_at = CURRENT_TIMESTAMP
                WHERE config_key = ?
            """, (config_data.config_value, config_key))
            
            conn.commit()
            
            # 获取更新后的配置
            cursor = conn.execute("""
                SELECT id, config_key, config_value, description, 
                       created_at, updated_at
                FROM configurations 
                WHERE config_key = ?
            """, (config_key,))
            
            row = cursor.fetchone()
            config = ConfigResponse(
                id=row[0],
                config_key=row[1],
                config_value=row[2],
                description=row[3],
                created_at=row[4],
                updated_at=row[5]
            )
            
            logger.info(f"更新配置成功: {config_key} = {config_data.config_value}")
            return config
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新配置失败: {e}")
        raise HTTPException(status_code=500, detail="更新配置失败")

@router.get("/system/info", response_model=SystemInfo)
async def get_system_info():
    """获取系统信息"""
    try:
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT config_key, config_value 
                FROM configurations 
                WHERE config_key IN (
                    'hospital_name', 'department_name', 'refresh_interval', 
                    'display_rows', 'timezone'
                )
            """)
            
            config_dict = {}
            for row in cursor.fetchall():
                config_dict[row[0]] = row[1]
            
            system_info = SystemInfo(
                hospital_name=config_dict.get('hospital_name', '示例医院'),
                department_name=config_dict.get('department_name', '麻醉科'),
                refresh_interval=int(config_dict.get('refresh_interval', 30)),
                display_rows=int(config_dict.get('display_rows', 10)),
                timezone=config_dict.get('timezone', 'Asia/Shanghai'),
                version="1.0.0"
            )
            
            logger.info("获取系统信息成功")
            return system_info
            
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail="获取系统信息失败")

@router.get("/chart/colors")
async def get_chart_colors():
    """获取图表颜色配置"""
    try:
        with db_connection.get_db_context() as conn:
            cursor = conn.execute("""
                SELECT config_value 
                FROM configurations 
                WHERE config_key = 'chart_colors'
            """)
            
            row = cursor.fetchone()
            if row:
                import json
                colors = json.loads(row[0])
            else:
                # 默认颜色配置
                colors = ["#1e88e5", "#43a047", "#e53935", "#fb8c00", "#039be5"]
            
            logger.info("获取图表颜色配置成功")
            return {"colors": colors}
            
    except Exception as e:
        logger.error(f"获取图表颜色配置失败: {e}")
        raise HTTPException(status_code=500, detail="获取图表颜色配置失败")
