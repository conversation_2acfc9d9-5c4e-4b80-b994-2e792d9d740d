/**
 * PPT式全屏缩放管理器
 * 像PowerPoint一样，在任何屏幕上都能完美全屏显示
 * 使用等比例缩放+居中显示，确保内容完整且充分利用屏幕
 */
class PPTStyleScaleManager {
    constructor() {
        // 设计基准分辨率（16:9比例）
        this.designWidth = 1920;
        this.designHeight = 1080;

        // 当前屏幕信息
        this.screenWidth = 0;
        this.screenHeight = 0;
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;

        // DOM元素
        this.container = null;
        this.wrapper = null;

        this.init();
    }

    init() {
        console.log('🎯 初始化PPT式全屏缩放管理器...');

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupFullscreenContainer();
            });
        } else {
            this.setupFullscreenContainer();
        }
    }

    setupFullscreenContainer() {
        // 查找页面容器
        this.container = document.getElementById('pageContainer') || document.querySelector('.page-container');
        if (!this.container) {
            console.error('❌ 未找到页面容器');
            return;
        }

        console.log('✅ 找到页面容器:', this.container.id || this.container.className);

        // 创建全屏包装器（如果不存在）
        this.createFullscreenWrapper();

        // 获取屏幕信息
        this.updateScreenInfo();

        // 计算PPT式缩放
        this.calculatePPTScale();

        // 应用全屏样式
        this.applyFullscreenStyles();

        // 监听窗口变化
        this.setupEventListeners();

        console.log('✅ PPT式全屏缩放管理器初始化完成');
        this.logScaleInfo();
    }

    createFullscreenWrapper() {
        // 检查是否已经有包装器
        if (this.container.parentElement && this.container.parentElement.classList.contains('ppt-fullscreen-wrapper')) {
            this.wrapper = this.container.parentElement;
            console.log('✅ 使用现有全屏包装器');
            return;
        }

        // 创建全屏包装器
        this.wrapper = document.createElement('div');
        this.wrapper.className = 'ppt-fullscreen-wrapper';

        // 设置包装器样式
        this.wrapper.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            z-index: 1000;
        `;

        // 将容器移动到包装器中
        this.container.parentNode.insertBefore(this.wrapper, this.container);
        this.wrapper.appendChild(this.container);

        console.log('✅ 创建全屏包装器');
    }
    
    /**
     * 更新屏幕信息
     */
    updateScreenInfo() {
        // 获取当前窗口尺寸（实际可用空间）
        this.screenWidth = window.innerWidth;
        this.screenHeight = window.innerHeight;

        console.log(`📐 当前屏幕尺寸: ${this.screenWidth}x${this.screenHeight}`);
        console.log(`📐 设计基准尺寸: ${this.designWidth}x${this.designHeight}`);
    }

    /**
     * 计算PPT式缩放（等比例缩放+居中）
     */
    calculatePPTScale() {
        // 计算长宽缩放比例
        const scaleX = this.screenWidth / this.designWidth;
        const scaleY = this.screenHeight / this.designHeight;

        // 使用较小的缩放比例，确保内容完整显示（类似PPT的适合窗口模式）
        this.scale = Math.min(scaleX, scaleY);

        // 计算居中偏移
        const scaledWidth = this.designWidth * this.scale;
        const scaledHeight = this.designHeight * this.scale;

        this.offsetX = (this.screenWidth - scaledWidth) / 2;
        this.offsetY = (this.screenHeight - scaledHeight) / 2;

        console.log(`🎯 PPT缩放计算:`);
        console.log(`   缩放比例: ${this.scale.toFixed(3)}`);
        console.log(`   缩放后尺寸: ${scaledWidth.toFixed(0)}x${scaledHeight.toFixed(0)}`);
        console.log(`   居中偏移: (${this.offsetX.toFixed(0)}, ${this.offsetY.toFixed(0)})`);
    }

    /**
     * 应用全屏样式
     */
    applyFullscreenStyles() {
        if (!this.container) return;

        // 设置容器为设计基准尺寸
        this.container.style.cssText = `
            width: ${this.designWidth}px;
            height: ${this.designHeight}px;
            position: relative;
            transform: scale(${this.scale});
            transform-origin: center center;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            overflow: hidden;
        `;

        console.log(`🎯 应用PPT式全屏样式:`);
        console.log(`   容器尺寸: ${this.designWidth}x${this.designHeight}`);
        console.log(`   缩放变换: scale(${this.scale.toFixed(3)})`);
        console.log(`   变换原点: center center`);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 防抖处理
        let resizeTimeout;

        const handleResize = () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleWindowResize();
            }, 100);
        };

        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);

        // 监听方向变化（移动设备）
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleWindowResize();
            }, 200);
        });

        // 监听全屏变化
        document.addEventListener('fullscreenchange', () => {
            setTimeout(() => {
                this.handleWindowResize();
            }, 100);
        });

        console.log('✅ 事件监听器设置完成');
    }

    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        console.log('🔄 窗口大小变化，重新计算PPT式缩放...');

        // 重新获取屏幕信息
        this.updateScreenInfo();

        // 重新计算PPT式缩放
        this.calculatePPTScale();

        // 重新应用全屏样式
        this.applyFullscreenStyles();

        // 输出日志
        this.logScaleInfo();
    }
    
    /**
     * 输出缩放信息
     */
    logScaleInfo() {
        console.log('📋 PPT式全屏缩放信息:');
        console.log(`   设计基准: ${this.designWidth}x${this.designHeight}`);
        console.log(`   屏幕尺寸: ${this.screenWidth}x${this.screenHeight}`);
        console.log(`   缩放比例: ${this.scale.toFixed(3)}`);
        console.log(`   居中偏移: (${this.offsetX.toFixed(0)}, ${this.offsetY.toFixed(0)})`);
        console.log(`   缩放后尺寸: ${(this.designWidth * this.scale).toFixed(0)}x${(this.designHeight * this.scale).toFixed(0)}`);

        // 计算屏幕利用率
        const utilization = (this.scale * 100).toFixed(1);
        console.log(`   屏幕利用率: ${utilization}%`);
    }

    /**
     * 获取缩放比例
     */
    getScale() {
        return this.scale;
    }

    /**
     * 获取屏幕信息
     */
    getScreenInfo() {
        return {
            screenWidth: this.screenWidth,
            screenHeight: this.screenHeight,
            designWidth: this.designWidth,
            designHeight: this.designHeight,
            scale: this.scale,
            offsetX: this.offsetX,
            offsetY: this.offsetY
        };
    }

    /**
     * 强制重新缩放
     */
    forceUpdate() {
        console.log('🔄 强制更新PPT式缩放...');
        setTimeout(() => {
            this.handleWindowResize();
        }, 50);
    }

    /**
     * 重新设置容器（用于页面切换后）
     */
    resetContainer() {
        this.container = null;
        this.wrapper = null;
        this.setupFullscreenContainer();
    }

    /**
     * 进入全屏模式
     */
    enterFullscreen() {
        if (this.wrapper && this.wrapper.requestFullscreen) {
            this.wrapper.requestFullscreen().catch(err => {
                console.log('全屏请求失败:', err);
            });
        }
    }

    /**
     * 退出全屏模式
     */
    exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen().catch(err => {
                console.log('退出全屏失败:', err);
            });
        }
    }
}

// 创建全局实例
console.log('🚀 创建PPT式全屏缩放管理器实例...');
window.pptScaleManager = new PPTStyleScaleManager();

// 兼容性别名
window.resolutionScaleManager = window.pptScaleManager;
window.modernScaleManager = window.pptScaleManager;
window.backgroundScaleManager = window.pptScaleManager;
